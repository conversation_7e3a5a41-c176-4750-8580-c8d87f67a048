package com.westcatr.rd.car.business.audit.entity;

import java.io.Serializable;
import java.util.Date;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.web.excel.annotation.ExcelField;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author: Roo
 * @Date: 2025-03-31
 * @Detail: 业务审计日志表 实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("audit_log")
@Schema(description = "业务审计日志表")
public class AuditLogInfo extends Model<AuditLogInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @ExcelField(name = "主键ID")
    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "用户唯一ID")
    @ExcelField(name = "用户唯一ID")
    @NotBlank(message = "id不能为空", groups = { Update.class })
    @Length(max = 255, message = "用户唯一ID长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("user_id")
    private String userId;

    @Schema(description = "用户名")
    @ExcelField(name = "用户名")
    @Length(max = 255, message = "用户名长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("username")
    private String username;

    @Schema(description = "操作时间")
    @ExcelField(name = "操作时间")
    @TableField("operation_time")
    private Date operationTime;

    @Schema(description = "操作IP地址")
    @ExcelField(name = "操作IP地址")
    @Length(max = 64, message = "操作IP地址长度不能超过64", groups = { Insert.class, Update.class })
    @TableField("ip_address")
    private String ipAddress;

    @Schema(description = "操作类型")
    @ExcelField(name = "操作类型")
    @Length(max = 50, message = "操作类型长度不能超过50", groups = { Insert.class, Update.class })
    @TableField("operation_type")
    private String operationType;

    @Schema(description = "事件描述")
    @ExcelField(name = "事件描述")
    @Length(max = 65535, message = "事件描述长度不能超过65535", groups = { Insert.class, Update.class })
    @TableField("event_description")
    private String eventDescription;

    @Schema(description = "业务唯一标识")
    @ExcelField(name = "业务唯一标识")
    @Length(max = 255, message = "业务唯一标识长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("business_id")
    private String businessId;

    @Schema(description = "事件结果 (成功/失败)")
    @ExcelField(name = "事件结果 (成功/失败)")
    @Length(max = 10, message = "事件结果 (成功/失败)长度不能超过10", groups = { Insert.class, Update.class })
    @TableField("result")
    private String result;

    @Schema(description = "失败时的错误信息")
    @ExcelField(name = "失败时的错误信息")
    @Length(max = 65535, message = "失败时的错误信息长度不能超过65535", groups = { Insert.class, Update.class })
    @TableField("error_message")
    private String errorMessage;

    @Schema(description = "请求参数 (JSON格式)")
    @ExcelField(name = "请求参数 (JSON格式)")
    @Length(max = 65535, message = "请求参数 (JSON格式)长度不能超过65535", groups = { Insert.class, Update.class })
    @TableField("request_params")
    private String requestParams;

    @Schema(description = "操作的类名")
    @ExcelField(name = "操作的类名")
    @Length(max = 512, message = "操作的类名长度不能超过512", groups = { Insert.class, Update.class })
    @TableField("class_name")
    private String className;

    @Schema(description = "操作的方法名")
    @ExcelField(name = "操作的方法名")
    @Length(max = 255, message = "操作的方法名长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("method_name")
    private String methodName;

    @Schema(description = "同步状态：0-未同步，1-已同步")
    @ExcelField(name = "同步状态：0-未同步，1-已同步")
    @TableField("sync_status")
    private Integer syncStatus;

    @Schema(description = "同步时间")
    @ExcelField(name = "同步时间")
    @TableField("sync_time")
    private Date syncTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}