# 泛泛平台OpenAPI对接文档

## 版本 2025060401

## 1. 概述

本文档描述了泛泛平台与E出行系统的OpenAPI接口对接方案。采用国密算法（SM3、SM4）实现安全的数据交互，确保维修订单数据的机密性、完整性和不可抵赖性。

## 2. 接入信息

### 2.1 认证信息

- **应用ID**: `fanfan_platform`
- **应用密钥**: `fanfan_platform_secret_key_2025`
- **SM4加密密钥**: `62636465666768693031323334353637`
- **SM4初始向量**: `37363534333231303938373635343332`
- **请求有效期**: 600秒（10分钟）

### 2.2 环境信息

- **开发环境**: `http://192.168.30.166:7331/app`
- **生产环境**: 待提供

### 2.3 安全机制

- 使用SM4对称加密算法（CTR模式）对业务数据进行加密
- 使用SM3哈希算法进行签名验证
- 防重放攻击：每个请求必须使用唯一的nonce值
- 请求时间戳与服务器时间差值不能超过10分钟

## 3. 接口规范

### 3.1 请求格式

所有请求必须使用POST方法，Content-Type为`application/json`，请求体格式如下：

```json
{
  "appId": "fanfan_platform",
  "data": "使用SM4加密后的业务数据（Base64编码）",
  "timestamp": "请求时间戳（毫秒）",
  "nonce": "随机字符串（防重放攻击）",
  "signature": "请求签名"
}
```

### 3.2 响应格式

响应体格式如下：

```json
{
  "appId": "fanfan_platform",
  "data": "使用SM4加密后的业务数据（Base64编码）",
  "timestamp": "响应时间戳（毫秒）",
  "nonce": "随机字符串",
  "signature": "响应签名"
}
```

解密后的业务数据格式：

```json
{
  "success": true,
  "message": "操作成功",
  "data": "具体业务数据"
}
```

### 3.3 签名计算

签名计算公式：`signature = SM3(appId + appSecret + timestamp + nonce + data)`

步骤：
1. 将`appId`、`appSecret`、`timestamp`、`nonce`、`data`按顺序拼接
2. 使用SM3算法计算哈希值
3. 将哈希值转换为十六进制字符串

## 4. 接口列表

### 4.1 创建维修订单

**接口地址**: `/openApi/fanfan/repair/createOrder`

**请求方法**: POST

**功能说明**: 泛泛平台发起创建维修订单，E出行系统接收并处理

**请求参数（解密后）**:

```json
{
  "repairOrderCode": "FF202501200001",
  "vehicleInfo": {
    "plateNumber": "京A12345",
    "vehicleModel": "比亚迪秦PLUS DM-i",
    "vinCode": "LGXC16AF0N0123456"
  },
  "supplierInfo": {
    "supplierId": 123,
    "supplierName": "北京XX汽车维修有限公司"
  },
  "estimatedAmount": 5000.00,
  "repairContent": "更换前刹车片、检查制动系统"
}
```

**响应参数（解密后）**:

```json
{
  "success": true,
  "message": "维修订单创建成功",
  "data": "FF202501200001"
}
```

**字段说明**:

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| repairOrderCode | String | 是 | 维修订单编号，由泛泛平台生成 |
| vehicleInfo.plateNumber | String | 是 | 车牌号 |
| vehicleInfo.vehicleModel | String | 是 | 车辆型号 |
| vehicleInfo.vinCode | String | 否 | 车架号 |
| supplierInfo.supplierId | Long | 否 | 供应商ID |
| supplierInfo.supplierName | String | 是 | 供应商名称 |
| repairContent | String | 是 | 维修内容描述 |
| vehicleType | String | 否 | 车辆类型 |
| mileage | String | 否 | 行驶里程 |
| odometerReading | String | 否 | 表显里程 |
| purchaseTime | String | 否 | 购买时间 |
| maintenanceType | String | 否 | 维保类型 |
| applicant | String | 否 | 申请人 |
| contactNumber | String | 否 | 联系电话 |
| applicationTime | String | 否 | 申请时间 |
| applicationLocation | String | 否 | 申请位置 |

### 4.2 审核结果回调

**接口说明**: 
1. 这里返回的审核类型（auditType）类型为预估报价、认质认价、驾驶员审批。
2. 审核结果（auditResult）只能返回：通过、不通过、重新发起。当返回重新发起时，必须返回supplierInfo（重新发起的供应商信息） 备注：认质认价可以不传入供应商信息。
3. 当返回的类型为认质认价时，必须返回bidQuotationResponseInfo（选择的竞价报价的供应商和这个供应商的报价信息）。
4. 如果返回结果（auditType）为预估报价，那么审核结果（auditResult）只能为通过。
   

**接口地址**: `/openApi/fanfan/repair/auditCallback`

**请求方法**: POST

**功能说明**: 泛泛平台审核完毕后，回调通知E出行系统审核结果

**请求参数（解密后）**:

```json
{
  "repairOrderCode": "FF202501200001",
  "auditType": "预估报价",
  "auditResult": "通过",
  "auditComment": "审核通过，可以进行维修",
  "auditTime": "2025-01-20 10:30:00",
  "auditor": "张三",
  "supplierInfo": {
    "supplierId": 123,
    "supplierName": "北京XX汽车维修有限公司"
  },
  "bidQuotationResponseInfo": {
    "supplierInfo": {
        "supplierId": 123,
        "supplierName": "北京XX汽车维修有限公司",
        "isLowestTotalPrice": true,
        "partsCount": 2,
        "executePartsTotalPrice": 660.00,
        "executeLaborTotalPrice": 220.00
    },
    "partsUsedList": [
        {
            "partsName": "前刹车片",
            "partsCode": "parts_code_1",
            "laborName": "前刹车片更换",
            "laborCode": "labor_code_1",
            "partsAttributes": "维修",
            "hours": 2.5,
            "unitPrice": 100.00,
            "taxRate": 0.16,
            "taxUnitPrice": 116.00,
            "taxTotalPrice": 290.00,
            "taxTotalPrice": 340.00,
            "executePrice": 4800,
            "remark": "前刹车片磨损严重，需要更换"
        }
    ],
    "laborHourList": [
        {
            "laborName": "前刹车片更换",
            "laborCode": "labor_code_1",
            "partsCode": "parts_code_1",
            "partsName": "前刹车片",
            "hours": 2.5,
            "unitPrice": 100.00,
            "taxRate": 0.16,
            "taxUnitPrice": 116.00,
            "taxTotalPrice": 290.00,
            "taxTotalPrice": 340.00,
             "executePrice": 4800,
            "remark": "前刹车片磨损严重，需要更换"
        }
    ]
}
```

**响应参数（解密后）**:

```json
{
  "success": true,
  "message": "审核结果处理成功",
  "data": null
}
```

**字段说明**:

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| repairOrderCode | String | 是 | 维修订单编号 |
| auditType | String | 是 | 审核类型（预估报价、认质认价、驾驶员审批） |
| auditResult | String | 是 | 审核结果：通过、不通过、重新发起 |
| auditComment | String | 否 | 审核意见 |
| auditTime | String | 否 | 审核时间，格式：yyyy-MM-dd HH:mm:ss |
| auditor | String | 否 | 审核人 |
| supplierInfo | Object | 否 | 供应商信息（如果重新发起，必须传入这个） |
| bidQuotationResponseInfo | Object | 否 | 报价信息（如果是预估报价，必须传入这个） |   
| bidQuotationResponseInfo.supplierInfo | Object | 否 | 供应商信息 |
| bidQuotationResponseInfo.supplierInfo.supplierId | String | 否 | 供应商ID |
| bidQuotationResponseInfo.supplierInfo.supplierName | String | 否 | 供应商名称 |
| bidQuotationResponseInfo.supplierInfo.isLowestTotalPrice | Boolean | 否 | 是否最低总价 |
| bidQuotationResponseInfo.supplierInfo.partsCount | Integer | 否 | 涉及配件数 |
| bidQuotationResponseInfo.supplierInfo.executePartsTotalPrice | BigDecimal | 否 | 执行配件总价 |
| bidQuotationResponseInfo.supplierInfo.executeLaborTotalPrice | BigDecimal | 否 | 执行工时总价 |
| bidQuotationResponseInfo.partsUsedList | Array | 否 | 选择的配件list |
| bidQuotationResponseInfo.partsUsedList[].partsName | String | 否 | 配件名称 |
| bidQuotationResponseInfo.partsUsedList[].partsCode | String | 否 | 配件编码 |
| bidQuotationResponseInfo.partsUsedList[].quantity | Integer | 否 | 数量 |
| bidQuotationResponseInfo.partsUsedList[].unitPrice | BigDecimal | 否 | 不含税单价 |
| bidQuotationResponseInfo.partsUsedList[].taxRate | BigDecimal | 否 | 税率 |
| bidQuotationResponseInfo.partsUsedList[].taxUnitPrice | BigDecimal | 否 | 含税单价 |
| bidQuotationResponseInfo.partsUsedList[].taxTotalPrice | BigDecimal | 否 | 不含税总价 |
| bidQuotationResponseInfo.partsUsedList[].taxTotalPrice | BigDecimal | 否 | 含税总价 |
| bidQuotationResponseInfo.partsUsedList[].executePrice | BigDecimal | 否 | 执行价格 |
| bidQuotationResponseInfo.partsUsedList[].remark | String | 否 | 备注 |
| bidQuotationResponseInfo.laborHourList | Array | 否 | 选择的工时list |
| bidQuotationResponseInfo.laborHourList[].laborName | String | 否 | 工时名称 |
| bidQuotationResponseInfo.laborHourList[].laborCode | String | 否 | 工时编码 |
| bidQuotationResponseInfo.laborHourList[].partsCode | String | 否 | 配件编码 |
| bidQuotationResponseInfo.laborHourList[].partsName | String | 否 | 配件名称 |
| bidQuotationResponseInfo.laborHourList[].hours | Integer | 否 | 工时 |
| bidQuotationResponseInfo.laborHourList[].unitPrice | BigDecimal | 否 | 不含税单价 |
| bidQuotationResponseInfo.laborHourList[].taxRate | BigDecimal | 否 | 税率 |
| bidQuotationResponseInfo.laborHourList[].taxUnitPrice | BigDecimal | 否 | 含税单价 |
| bidQuotationResponseInfo.laborHourList[].taxTotalPrice | BigDecimal | 否 | 不含税总价 |
| bidQuotationResponseInfo.laborHourList[].taxTotalPrice | BigDecimal | 否 | 含税总价 |
| bidQuotationResponseInfo.laborHourList[].executePrice | BigDecimal | 否 | 执行价格 |
| bidQuotationResponseInfo.laborHourList[].remark | String | 否 | 备注 |

### 4.3 修理厂数据同步

**接口地址**: `/openApi/fanfan/garage/dataSync`

**请求方法**: POST

**功能说明**: 泛泛平台批量同步修理厂基本信息到E出行系统。支持一次性同步多个修理厂的信息，系统会自动创建或更新修理厂供应商信息，包括折扣设置和联系方式等。

**业务逻辑**:
1. 接收修理厂数据列表，支持批量处理
2. 遍历每个修理厂数据，进行独立处理
3. 验证必填字段（修理厂ID和名称）
4. 根据修理厂名称、供应商类型和泛泛ID查找现有供应商
5. 如果存在则更新供应商信息，否则创建新的维修供应商
6. 设置材料费折扣和人工费折扣
7. 统计处理结果，返回批量同步结果

**请求参数（解密后）**:

```json
[
  {
    "garageId": "GAR202501270001",
    "garageName": "深圳市优质汽车维修服务中心",
    "garageAddress": "深圳市南山区科技园南区高新南七道16号",
    "contactPerson": "张师傅",
    "contactPhone": "13800138000",
    "garageType": "综合维修厂",
    "laborDiscount": 0.85,
    "materialDiscount": 0.90,
    "scope": "全国",
    "status": "正常营业"
  },
  {
    "garageId": "GAR202501270002",
    "garageName": "北京专业钣金喷漆中心",
    "garageAddress": "北京市朝阳区建国路88号汽修大厦3层",
    "contactPerson": "李师傅",
    "contactPhone": "13900139002",
    "garageType": "钣金喷漆",
    "laborDiscount": 0.80,
    "materialDiscount": 0.88,
    "scope": "华北地区",
    "status": "正常营业"
  }
]
```

**响应参数（解密后）**:

```json
{
  "success": true,
  "message": "修理厂数据批量同步成功，共处理2个修理厂",
  "data": null
}
```

**字段说明**:

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| garageId | String | 是 | 修理厂ID，泛泛平台唯一标识 |
| garageName | String | 是 | 修理厂名称，用于创建供应商公司名称 |
| garageAddress | String | 否 | 修理厂地址，存储到供应商地址字段 |
| contactPerson | String | 否 | 联系人，存储到供应商联系人字段 |
| contactPhone | String | 否 | 联系电话，存储到供应商联系电话字段 |
| garageType | String | 否 | 修理厂类型（如：综合维修厂、专业维修厂等） |
| laborDiscount | Double | 否 | 人工费折扣（0-1之间的小数，如0.85表示85折） |
| materialDiscount | Double | 否 | 材料费折扣（0-1之间的小数，如0.90表示9折） |
| scope | String | 否 | 服务范围 |
| status | String | 否 | 营业状态 |

**注意事项**:
1. 系统会根据修理厂名称、供应商类型（"维修供应商"）和泛泛ID进行唯一性判断
2. 如果修理厂已存在，会更新联系方式、地址和折扣信息
3. 如果修理厂不存在，会创建新的维修供应商记录
4. 折扣信息会分别存储到供应商的材料费折扣和人工费折扣字段

## 5. 错误处理

### 5.1 鉴权类错误（未加密响应）

当请求存在以下问题时，系统返回未加密的错误响应：

- AppID无效或不存在
- 签名验证失败
- 时间戳过期（超过10分钟）
- 重放攻击（相同nonce重复使用）

错误响应格式：

```json
{
  "code": 403,
  "message": "签名验证失败",
  "timestamp": "1711509402000",
  "nonce": "a1b2c3d4e5f6"
}
```

### 5.2 业务逻辑错误（加密响应）

当请求通过鉴权验证，但业务处理失败时，返回加密的错误响应：

```json
{
  "success": false,
  "message": "维修订单编号已存在",
  "data": null
}
```

### 5.3 常见错误码

| 错误码 | 说明 |
|--------|------|
| 401 | 未授权（AppID无效） |
| 403 | 禁止访问（签名无效） |
| 408 | 请求超时（时间戳过期） |
| 429 | 请求过于频繁（防重放攻击） |
| 500 | 服务器内部错误 |

## 6. 常见问题

### 6.1 签名验证失败

**可能原因**:
- appId或appSecret不正确
- 签名计算顺序错误
- 时间戳格式错误
- 加密数据不正确

**解决方法**:
- 检查认证信息是否正确
- 确保签名计算按照文档顺序：appId + appSecret + timestamp + nonce + data
- 使用毫秒级时间戳
- 确保SM4加密使用CTR模式

### 6.2 请求超时

**可能原因**:
- 网络延迟
- 时间戳与服务器时间差异过大（超过10分钟）

**解决方法**:
- 检查网络连接
- 确保客户端时间与服务器时间同步
- 使用当前时间戳发送请求

### 6.3 重放攻击检测

**可能原因**:
- 使用了重复的nonce值
- 请求重复发送

**解决方法**:
- 确保每次请求使用唯一的nonce值
- 避免重复发送相同的请求

### 6.4 业务逻辑错误

**可能原因**:
- 请求参数格式不正确
- 必填字段缺失
- 数据验证失败

**解决方法**:
- 检查请求参数是否符合接口规范
- 确保必填字段都已提供
- 验证数据格式和类型

## 7. Java代码示例

### 7.1 依赖配置

在项目的 `pom.xml` 中添加以下依赖：

```xml
<dependencies>
    <!-- BouncyCastle 国密算法支持 -->
    <dependency>
        <groupId>org.bouncycastle</groupId>
        <artifactId>bcprov-jdk15on</artifactId>
        <version>1.70</version>
    </dependency>

    <!-- Jackson JSON处理 -->
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>2.15.2</version>
    </dependency>

    <!-- Apache HttpClient -->
    <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>4.5.14</version>
    </dependency>
</dependencies>
```

### 7.2 泛泛平台OpenAPI客户端

```java
package com.fanfan.openapi.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.Security;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 泛泛平台OpenAPI客户端
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public class FanFanOpenApiClient {

    private static final String ALGORITHM = "SM4/CTR/NoPadding";
    private final String appId;
    private final String appSecret;
    private final byte[] sm4Key;
    private final byte[] sm4Iv;
    private final String baseUrl;
    private final ObjectMapper objectMapper;
    private final HttpClient httpClient;

    static {
        // 注册BouncyCastle提供者
        if (Security.getProvider("BC") == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    /**
     * 构造函数
     *
     * @param baseUrl E出行系统基础URL
     * @param appId 应用ID
     * @param appSecret 应用密钥
     * @param sm4Key SM4加密密钥（十六进制字符串）
     * @param sm4Iv SM4初始向量（十六进制字符串）
     */
    public FanFanOpenApiClient(String baseUrl, String appId, String appSecret, String sm4Key, String sm4Iv) {
        this.baseUrl = baseUrl.endsWith("/") ? baseUrl.substring(0, baseUrl.length() - 1) : baseUrl;
        this.appId = appId;
        this.appSecret = appSecret;
        this.sm4Key = hexToBytes(sm4Key);
        this.sm4Iv = hexToBytes(sm4Iv);
        this.objectMapper = new ObjectMapper();
        this.httpClient = HttpClients.createDefault();
    }

    /**
     * SM4加密
     *
     * @param data 待加密数据
     * @return Base64编码的加密结果
     */
    public String encrypt(String data) throws Exception {
        Cipher cipher = Cipher.getInstance(ALGORITHM, "BC");
        SecretKeySpec keySpec = new SecretKeySpec(sm4Key, "SM4");
        IvParameterSpec ivSpec = new IvParameterSpec(sm4Iv);
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
        byte[] encrypted = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encrypted);
    }

    /**
     * SM4解密
     *
     * @param encryptedData Base64编码的加密数据
     * @return 解密后的原始数据
     */
    public String decrypt(String encryptedData) throws Exception {
        byte[] dataBytes = Base64.getDecoder().decode(encryptedData);
        Cipher cipher = Cipher.getInstance(ALGORITHM, "BC");
        SecretKeySpec keySpec = new SecretKeySpec(sm4Key, "SM4");
        IvParameterSpec ivSpec = new IvParameterSpec(sm4Iv);
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
        byte[] decrypted = cipher.doFinal(dataBytes);
        return new String(decrypted, StandardCharsets.UTF_8);
    }

    /**
     * 计算SM3签名
     *
     * @param timestamp 时间戳
     * @param nonce 随机字符串
     * @param data 加密后的数据
     * @return 签名字符串
     */
    public String calculateSignature(String timestamp, String nonce, String data) throws Exception {
        String content = appId + appSecret + timestamp + nonce + (data == null ? "" : data);
        MessageDigest digest = MessageDigest.getInstance("SM3", "BC");
        byte[] hash = digest.digest(content.getBytes(StandardCharsets.UTF_8));
        return bytesToHex(hash);
    }

    /**
     * 创建请求对象
     *
     * @param businessData 业务数据JSON字符串
     * @return 请求对象
     */
    public Map<String, Object> createRequest(String businessData) throws Exception {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String nonce = generateNonce();
        String encryptedData = encrypt(businessData);
        String signature = calculateSignature(timestamp, nonce, encryptedData);

        Map<String, Object> request = new HashMap<>();
        request.put("appId", appId);
        request.put("data", encryptedData);
        request.put("timestamp", timestamp);
        request.put("nonce", nonce);
        request.put("signature", signature);

        return request;
    }

    /**
     * 发送HTTP POST请求
     *
     * @param url 请求URL
     * @param requestData 请求数据
     * @return 响应结果
     */
    private String sendHttpRequest(String url, Map<String, Object> requestData) throws Exception {
        String requestJson = objectMapper.writeValueAsString(requestData);

        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("Content-Type", "application/json");
        httpPost.setEntity(new StringEntity(requestJson, ContentType.APPLICATION_JSON));

        HttpResponse response = httpClient.execute(httpPost);
        return EntityUtils.toString(response.getEntity());
    }

    /**
     * 处理响应数据
     *
     * @param responseJson 响应JSON字符串
     * @return 解密后的业务数据
     */
    public ApiResponse processResponse(String responseJson) throws Exception {
        JsonNode responseNode = objectMapper.readTree(responseJson);

        // 检查是否为鉴权错误（未加密响应）
        if (!responseNode.has("data")) {
            return new ApiResponse(false, responseNode.get("message").asText(), null,
                                 responseNode.get("code").asInt());
        }

        // 解密响应数据
        String encryptedData = responseNode.get("data").asText();
        String decryptedData = decrypt(encryptedData);

        // 解析业务响应
        JsonNode businessResponse = objectMapper.readTree(decryptedData);
        boolean success = businessResponse.get("success").asBoolean();
        String message = businessResponse.get("message").asText();
        JsonNode data = businessResponse.get("data");

        return new ApiResponse(success, message, data, 200);
    }

    /**
     * 创建维修订单
     *
     * @param repairOrder 维修订单信息
     * @return API响应结果
     */
    public ApiResponse createRepairOrder(RepairOrderRequest repairOrder) throws Exception {
        String businessData = objectMapper.writeValueAsString(repairOrder);
        Map<String, Object> requestData = createRequest(businessData);

        String url = baseUrl + "/openApi/fanfan/repair/createOrder";
        String responseJson = sendHttpRequest(url, requestData);

        return processResponse(responseJson);
    }

    /**
     * 发送审核结果回调
     *
     * @param auditResult 审核结果
     * @return API响应结果
     */
    public ApiResponse sendAuditCallback(AuditCallbackRequest auditResult) throws Exception {
        String businessData = objectMapper.writeValueAsString(auditResult);
        Map<String, Object> requestData = createRequest(businessData);

        String url = baseUrl + "/openApi/fanfan/repair/auditCallback";
        String responseJson = sendHttpRequest(url, requestData);

        return processResponse(responseJson);
    }

    /**
     * 修理厂数据同步
     *
     * @param garageData 修理厂数据
     * @return API响应结果
     */
    public ApiResponse syncGarageData(GarageDataSyncRequest garageData) throws Exception {
        String businessData = objectMapper.writeValueAsString(garageData);
        Map<String, Object> requestData = createRequest(businessData);

        String url = baseUrl + "/openApi/fanfan/garage/dataSync";
        String responseJson = sendHttpRequest(url, requestData);

        return processResponse(responseJson);
    }

    /**
     * 生成随机nonce值
     */
    private String generateNonce() {
        return Long.toHexString(System.nanoTime()) + Long.toHexString(System.currentTimeMillis());
    }

    /**
     * 十六进制字符串转字节数组
     */
    private byte[] hexToBytes(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }

    /**
     * 字节数组转十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }
}
```

### 7.3 数据模型类

#### 7.3.1 API响应封装类

```java
package com.fanfan.openapi.model;

import com.fasterxml.jackson.databind.JsonNode;

/**
 * API响应结果封装类
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public class ApiResponse {
    private boolean success;
    private String message;
    private JsonNode data;
    private int code;

    public ApiResponse() {}

    public ApiResponse(boolean success, String message, JsonNode data, int code) {
        this.success = success;
        this.message = message;
        this.data = data;
        this.code = code;
    }

    // Getter和Setter方法
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }

    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }

    public JsonNode getData() { return data; }
    public void setData(JsonNode data) { this.data = data; }

    public int getCode() { return code; }
    public void setCode(int code) { this.code = code; }

    @Override
    public String toString() {
        return "ApiResponse{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", code=" + code +
                '}';
    }
}
```

#### 7.3.2 维修订单请求类

```java
package com.fanfan.openapi.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;

/**
 * 创建维修订单请求类
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public class RepairOrderRequest {

    @JsonProperty("repairOrderCode")
    private String repairOrderCode;

    @JsonProperty("vehicleInfo")
    private VehicleInfo vehicleInfo;

    @JsonProperty("supplierInfo")
    private SupplierInfo supplierInfo;

    @JsonProperty("estimatedAmount")
    private BigDecimal estimatedAmount;

    @JsonProperty("repairContent")
    private String repairContent;

    // 构造函数
    public RepairOrderRequest() {}

    // Getter和Setter方法
    public String getRepairOrderCode() { return repairOrderCode; }
    public void setRepairOrderCode(String repairOrderCode) { this.repairOrderCode = repairOrderCode; }

    public VehicleInfo getVehicleInfo() { return vehicleInfo; }
    public void setVehicleInfo(VehicleInfo vehicleInfo) { this.vehicleInfo = vehicleInfo; }

    public SupplierInfo getSupplierInfo() { return supplierInfo; }
    public void setSupplierInfo(SupplierInfo supplierInfo) { this.supplierInfo = supplierInfo; }

    public BigDecimal getEstimatedAmount() { return estimatedAmount; }
    public void setEstimatedAmount(BigDecimal estimatedAmount) { this.estimatedAmount = estimatedAmount; }

    public String getRepairContent() { return repairContent; }
    public void setRepairContent(String repairContent) { this.repairContent = repairContent; }

    /**
     * 车辆信息内部类
     */
    public static class VehicleInfo {
        @JsonProperty("plateNumber")
        private String plateNumber;

        @JsonProperty("vehicleModel")
        private String vehicleModel;

        @JsonProperty("vinCode")
        private String vinCode;

        // Getter和Setter方法
        public String getPlateNumber() { return plateNumber; }
        public void setPlateNumber(String plateNumber) { this.plateNumber = plateNumber; }

        public String getVehicleModel() { return vehicleModel; }
        public void setVehicleModel(String vehicleModel) { this.vehicleModel = vehicleModel; }

        public String getVinCode() { return vinCode; }
        public void setVinCode(String vinCode) { this.vinCode = vinCode; }
    }

    /**
     * 供应商信息内部类
     */
    public static class SupplierInfo {
        @JsonProperty("supplierId")
        private Long supplierId;

        @JsonProperty("supplierName")
        private String supplierName;

        // Getter和Setter方法
        public Long getSupplierId() { return supplierId; }
        public void setSupplierId(Long supplierId) { this.supplierId = supplierId; }

        public String getSupplierName() { return supplierName; }
        public void setSupplierName(String supplierName) { this.supplierName = supplierName; }
    }
}
```

#### 7.3.3 审核结果回调请求类

```java
package com.fanfan.openapi.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 审核结果回调请求类
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public class AuditCallbackRequest {

    @JsonProperty("repairOrderCode")
    private String repairOrderCode;

    @JsonProperty("auditResult")
    private String auditResult;

    @JsonProperty("auditComment")
    private String auditComment;

    @JsonProperty("auditTime")
    private String auditTime;

    @JsonProperty("auditor")
    private String auditor;

    // 构造函数
    public AuditCallbackRequest() {}

    public AuditCallbackRequest(String repairOrderCode, String auditResult, String auditComment,
                               String auditTime, String auditor) {
        this.repairOrderCode = repairOrderCode;
        this.auditResult = auditResult;
        this.auditComment = auditComment;
        this.auditTime = auditTime;
        this.auditor = auditor;
    }

    // Getter和Setter方法
    public String getRepairOrderCode() { return repairOrderCode; }
    public void setRepairOrderCode(String repairOrderCode) { this.repairOrderCode = repairOrderCode; }

    public String getAuditResult() { return auditResult; }
    public void setAuditResult(String auditResult) { this.auditResult = auditResult; }

    public String getAuditComment() { return auditComment; }
    public void setAuditComment(String auditComment) { this.auditComment = auditComment; }

    public String getAuditTime() { return auditTime; }
    public void setAuditTime(String auditTime) { this.auditTime = auditTime; }

    public String getAuditor() { return auditor; }
    public void setAuditor(String auditor) { this.auditor = auditor; }
}
```

#### 7.3.4 竞价报价结果回调请求类
```java
package com.fanfan.openapi.model;
import java.util.List;
import com.westcatr.rd.car.business.fanfan.dto.request.FanFanRepairOrderSubmitRequest.LaborHour;
import com.westcatr.rd.car.business.fanfan.dto.request.FanFanRepairOrderSubmitRequest.PartsUsed;
import com.westcatr.rd.car.business.supplier.entity.SupplierInfo;

import lombok.Data;

@Data
public class FanFanBidResultCallbackRequest {
    // 订单编号
    private String repairOrderCode;

    // 供应商信息
    private SupplierInfo supplierInfo;

    // 选择的配件list
    private List<PartsUsed> partsUsed;

    // 选择的工时list
    private List<LaborHour> laborHours;

    //细化SupplierInfo
    public static class SupplierInfo {
        private Long supplierId;
        private String supplierName;
    }

    @Data
    @Schema(description = "使用的配件")
    public static class PartsUsed {

        @Schema(description = "配件名称", required = true)
        @NotBlank(message = "配件名称不能为空")
        private String partsName;

        @Schema(description = "配件编码")
        private String partsCode;

        @Schema(description = "数量", required = false)
        @NotNull(message = "数量不能为空")
        @Positive(message = "数量必须大于0")
        private Integer quantity;

        // 不含税单价
        @Schema(description = "不含税单价")
        private BigDecimal unitPrice;

        // 税率
        @Schema(description = "税率")
        private BigDecimal taxRate;

        @Schema(description = "含税单价")
        private BigDecimal taxUnitPrice;

        @Schema(description = "不含税总价")
        private BigDecimal taxTotalPrice;

        @Schema(description = "含税总价")
        private BigDecimal taxTotalPrice;

        @Schema(description = "备注")
        private String remark;
    }

    @Data
    @Schema(description = "工时")
    public static class LaborHour {

        // 绑定配件名称
        @Schema(description = "绑定配件名称")
        private String partsName;

        // 绑定配件编码
        @Schema(description = "绑定配件编码")
        private String partsCode;

        @Schema(description = "工时名称", required = true)
        @NotBlank(message = "工时名称不能为空")
        private String laborName;

        @Schema(description = "工时编码")
        private String laborCode;

        @Schema(description = "工时数", required = true)
        @NotNull(message = "工时数不能为空")
        @Positive(message = "工时数必须大于0")
        private Double hours;

        @Schema(description = "不含税单价")
        private BigDecimal unitPrice;

        @Schema(description = "税率")
        private BigDecimal taxRate;

        @Schema(description = "含税单价")
        private BigDecimal taxUnitPrice;

        @Schema(description = "不含税总价")
        private BigDecimal taxTotalPrice;

        @Schema(description = "含税总价")
        private BigDecimal taxTotalPrice;

        @Schema(description = "备注")
        private String remark;
    }

}
```

#### 7.3.4 修理厂数据同步请求类

```java
package com.fanfan.openapi.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * 修理厂数据同步请求类
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public class GarageDataSyncRequest {

    @JsonProperty("garageId")
    private Long garageId;

    @JsonProperty("garageName")
    private String garageName;

    @JsonProperty("garageAddress")
    private String garageAddress;

    @JsonProperty("contactPerson")
    private String contactPerson;

    @JsonProperty("contactPhone")
    private String contactPhone;

    @JsonProperty("garageType")
    private String garageType;

    @JsonProperty("laborDiscount")
    private Double laborDiscount;

    @JsonProperty("materialDiscount")
    private Double materialDiscount;

    @JsonProperty("scope")
    private String scope;

    @JsonProperty("status")
    private String status;

    @JsonProperty("staffList")
    private List<GarageStaff> staffList;

    // 构造函数
    public GarageDataSyncRequest() {}

    // Getter和Setter方法
    public Long getGarageId() { return garageId; }
    public void setGarageId(Long garageId) { this.garageId = garageId; }

    public String getGarageName() { return garageName; }
    public void setGarageName(String garageName) { this.garageName = garageName; }

    public String getGarageAddress() { return garageAddress; }
    public void setGarageAddress(String garageAddress) { this.garageAddress = garageAddress; }

    public String getContactPerson() { return contactPerson; }
    public void setContactPerson(String contactPerson) { this.contactPerson = contactPerson; }

    public String getContactPhone() { return contactPhone; }
    public void setContactPhone(String contactPhone) { this.contactPhone = contactPhone; }

    public String getGarageType() { return garageType; }
    public void setGarageType(String garageType) { this.garageType = garageType; }

    public Double getLaborDiscount() { return laborDiscount; }
    public void setLaborDiscount(Double laborDiscount) { this.laborDiscount = laborDiscount; }

    public Double getMaterialDiscount() { return materialDiscount; }
    public void setMaterialDiscount(Double materialDiscount) { this.materialDiscount = materialDiscount; }

    public String getScope() { return scope; }
    public void setScope(String scope) { this.scope = scope; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public List<GarageStaff> getStaffList() { return staffList; }
    public void setStaffList(List<GarageStaff> staffList) { this.staffList = staffList; }

    /**
     * 修理厂装修人员信息内部类
     */
    public static class GarageStaff {
        @JsonProperty("staffId")
        private Long staffId;

        @JsonProperty("staffName")
        private String staffName;

        @JsonProperty("staffType")
        private String staffType;

        @JsonProperty("phone")
        private String phone;

        @JsonProperty("position")
        private String position;

        @JsonProperty("status")
        private String status;

        // Getter和Setter方法
        public Long getStaffId() { return staffId; }
        public void setStaffId(Long staffId) { this.staffId = staffId; }

        public String getStaffName() { return staffName; }
        public void setStaffName(String staffName) { this.staffName = staffName; }

        public String getStaffType() { return staffType; }
        public void setStaffType(String staffType) { this.staffType = staffType; }

        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }

        public String getPosition() { return position; }
        public void setPosition(String position) { this.position = position; }

        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
    }
}
```

### 7.4 使用示例

#### 7.4.1 客户端初始化

```java
package com.fanfan.openapi.example;

import com.fanfan.openapi.client.FanFanOpenApiClient;
import com.fanfan.openapi.model.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 泛泛平台OpenAPI使用示例
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public class FanFanApiExample {

    public static void main(String[] args) {
        // 初始化客户端（使用最新的密钥配置）
        FanFanOpenApiClient client = new FanFanOpenApiClient(
            "https://qtn2ktw9-7331.asse.devtunnels.ms/app",  // E出行系统地址
            "fanfan_platform",                                // 应用ID
            "fanfan_platform_secret_key_2025",               // 应用密钥
            "62636465666768693031323334353637",              // SM4密钥
            "37363534333231303938373635343332"               // SM4初始向量
        );

        try {
            // 示例1：创建维修订单
            createRepairOrderExample(client);

            // 示例2：发送审核结果回调
            sendAuditCallbackExample(client);

            // 示例3：修理厂数据同步
            syncGarageDataExample(client);

        } catch (Exception e) {
            System.err.println("API调用失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建维修订单示例
     */
    private static void createRepairOrderExample(FanFanOpenApiClient client) throws Exception {
        System.out.println("=== 创建维修订单示例 ===");

        // 构建维修订单请求
        RepairOrderRequest repairOrder = new RepairOrderRequest();
        repairOrder.setRepairOrderCode("FF202501200001");
        repairOrder.setEstimatedAmount(new BigDecimal("5000.00"));
        repairOrder.setRepairContent("更换前刹车片、检查制动系统");

        // 设置车辆信息
        RepairOrderRequest.VehicleInfo vehicleInfo = new RepairOrderRequest.VehicleInfo();
        vehicleInfo.setPlateNumber("京A12345");
        vehicleInfo.setVehicleModel("比亚迪秦PLUS DM-i");
        vehicleInfo.setVinCode("LGXC16AF0N0123456");
        repairOrder.setVehicleInfo(vehicleInfo);

        // 设置供应商信息
        RepairOrderRequest.SupplierInfo supplierInfo = new RepairOrderRequest.SupplierInfo();
        supplierInfo.setSupplierId(123L);
        supplierInfo.setSupplierName("北京XX汽车维修有限公司");
        repairOrder.setSupplierInfo(supplierInfo);

        // 发送请求
        ApiResponse response = client.createRepairOrder(repairOrder);

        // 处理响应
        System.out.println("请求结果: " + response);
        if (response.isSuccess()) {
            System.out.println("维修订单创建成功！订单号: " + response.getData().asText());
        } else {
            System.err.println("维修订单创建失败: " + response.getMessage());
        }

        System.out.println();
    }

    /**
     * 发送审核结果回调示例
     */
    private static void sendAuditCallbackExample(FanFanOpenApiClient client) throws Exception {
        System.out.println("=== 发送审核结果回调示例 ===");

        // 构建审核结果回调请求
        AuditCallbackRequest auditCallback = new AuditCallbackRequest(
            "FF202501200001",                                    // 维修订单编号
            true,                                               // 审核结果：通过
            "审核通过，可以进行维修",                              // 审核意见
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), // 审核时间
            "张三"                                              // 审核人
        );

        // 发送请求
        ApiResponse response = client.sendAuditCallback(auditCallback);

        // 处理响应
        System.out.println("请求结果: " + response);
        if (response.isSuccess()) {
            System.out.println("审核结果回调发送成功！");
        } else {
            System.err.println("审核结果回调发送失败: " + response.getMessage());
        }

        System.out.println();
    }

    /**
     * 修理厂数据同步示例
     */
    private static void syncGarageDataExample(FanFanOpenApiClient client) throws Exception {
        System.out.println("=== 修理厂数据同步示例 ===");

        // 构建修理厂数据同步请求
        GarageDataSyncRequest garageData = new GarageDataSyncRequest();
        garageData.setGarageId(123L);
        garageData.setGarageName("北京XX汽车维修有限公司");
        garageData.setGarageAddress("北京市朝阳区XX路XX号");
        garageData.setContactPerson("张三");
        garageData.setContactPhone("13800138000");
        garageData.setGarageType("综合维修厂");
        garageData.setLaborDiscount(0.85);
        garageData.setMaterialDiscount(0.90);
        garageData.setScope("全国");
        garageData.setStatus("正常");

        // 设置装修人员信息
        List<GarageDataSyncRequest.GarageStaff> staffList = new ArrayList<>();

        GarageDataSyncRequest.GarageStaff staff1 = new GarageDataSyncRequest.GarageStaff();
        staff1.setStaffId(1001L);
        staff1.setStaffName("李四");
        staff1.setStaffType("技师");
        staff1.setPhone("13900139000");
        staff1.setPosition("高级技师");
        staff1.setStatus("在职");
        staffList.add(staff1);

        GarageDataSyncRequest.GarageStaff staff2 = new GarageDataSyncRequest.GarageStaff();
        staff2.setStaffId(1002L);
        staff2.setStaffName("王五");
        staff2.setStaffType("装修工");
        staff2.setPhone("13700137000");
        staff2.setPosition("装修主管");
        staff2.setStatus("在职");
        staffList.add(staff2);

        garageData.setStaffList(staffList);

        // 发送请求
        ApiResponse response = client.syncGarageData(garageData);

        // 处理响应
        System.out.println("请求结果: " + response);
        if (response.isSuccess()) {
            System.out.println("修理厂数据同步成功！");
        } else {
            System.err.println("修理厂数据同步失败: " + response.getMessage());
        }

        System.out.println();
    }
}
```

#### 7.4.2 Spring Boot集成示例

```java
package com.fanfan.openapi.config;

import com.fanfan.openapi.client.FanFanOpenApiClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 泛泛平台OpenAPI配置类
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Configuration
public class FanFanApiConfig {

    @Value("${fanfan.platform.base-url:https://qtn2ktw9-7331.asse.devtunnels.ms/app}")
    private String baseUrl;

    @Value("${fanfan.platform.app-id}")
    private String appId;

    @Value("${fanfan.platform.app-secret}")
    private String appSecret;

    @Value("${fanfan.platform.sm4-key}")
    private String sm4Key;

    @Value("${fanfan.platform.sm4-iv}")
    private String sm4Iv;

    @Bean
    public FanFanOpenApiClient fanFanOpenApiClient() {
        return new FanFanOpenApiClient(baseUrl, appId, appSecret, sm4Key, sm4Iv);
    }
}
```

```java
package com.fanfan.openapi.service;

import com.fanfan.openapi.client.FanFanOpenApiClient;
import com.fanfan.openapi.model.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 泛泛平台API服务类
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class FanFanApiService {

    @Autowired
    private FanFanOpenApiClient fanFanOpenApiClient;

    /**
     * 创建维修订单
     *
     * @param repairOrder 维修订单信息
     * @return API响应结果
     */
    public ApiResponse createRepairOrder(RepairOrderRequest repairOrder) {
        try {
            return fanFanOpenApiClient.createRepairOrder(repairOrder);
        } catch (Exception e) {
            return new ApiResponse(false, "创建维修订单失败: " + e.getMessage(), null, 500);
        }
    }

    /**
     * 发送审核结果回调
     *
     * @param auditCallback 审核结果
     * @return API响应结果
     */
    public ApiResponse sendAuditCallback(AuditCallbackRequest auditCallback) {
        try {
            return fanFanOpenApiClient.sendAuditCallback(auditCallback);
        } catch (Exception e) {
            return new ApiResponse(false, "发送审核结果失败: " + e.getMessage(), null, 500);
        }
    }

    /**
     * 修理厂数据同步
     *
     * @param garageData 修理厂数据
     * @return API响应结果
     */
    public ApiResponse syncGarageData(GarageDataSyncRequest garageData) {
        try {
            return fanFanOpenApiClient.syncGarageData(garageData);
        } catch (Exception e) {
            return new ApiResponse(false, "修理厂数据同步失败: " + e.getMessage(), null, 500);
        }
    }
}
```

### 7.5 单元测试示例

```java
package com.fanfan.openapi.test;

import com.fanfan.openapi.client.FanFanOpenApiClient;
import com.fanfan.openapi.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 泛泛平台OpenAPI单元测试
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public class FanFanOpenApiClientTest {

    private FanFanOpenApiClient client;

    @BeforeEach
    void setUp() {
        // 使用测试环境配置
        client = new FanFanOpenApiClient(
            "https://test-api.example.com",
            "fanfan_platform",
            "fanfan_platform_secret_key_2025",
            "62636465666768693031323334353637",
            "37363534333231303938373635343332"
        );
    }

    @Test
    @DisplayName("测试SM4加密解密功能")
    void testSM4EncryptDecrypt() throws Exception {
        String originalData = "Hello, 泛泛平台!";

        // 加密
        String encryptedData = client.encrypt(originalData);
        assertNotNull(encryptedData);
        assertNotEquals(originalData, encryptedData);

        // 解密
        String decryptedData = client.decrypt(encryptedData);
        assertEquals(originalData, decryptedData);
    }

    @Test
    @DisplayName("测试签名计算功能")
    void testSignatureCalculation() throws Exception {
        String timestamp = "1711509402000";
        String nonce = "a1b2c3d4e5f6";
        String data = "test_data";

        String signature = client.calculateSignature(timestamp, nonce, data);
        assertNotNull(signature);
        assertEquals(64, signature.length()); // SM3哈希结果为64位十六进制字符串
    }

    @Test
    @DisplayName("测试创建请求对象")
    void testCreateRequest() throws Exception {
        String businessData = "{\"test\":\"data\"}";

        var request = client.createRequest(businessData);

        assertNotNull(request);
        assertEquals("fanfan_platform", request.get("appId"));
        assertNotNull(request.get("data"));
        assertNotNull(request.get("timestamp"));
        assertNotNull(request.get("nonce"));
        assertNotNull(request.get("signature"));
    }

    @Test
    @DisplayName("测试维修订单请求对象构建")
    void testRepairOrderRequest() {
        RepairOrderRequest repairOrder = new RepairOrderRequest();
        repairOrder.setRepairOrderCode("FF202501200001");
        repairOrder.setEstimatedAmount(new BigDecimal("5000.00"));
        repairOrder.setRepairContent("更换前刹车片");
        repairOrder.setUrgencyLevel("普通");

        // 设置车辆信息
        RepairOrderRequest.VehicleInfo vehicleInfo = new RepairOrderRequest.VehicleInfo();
        vehicleInfo.setPlateNumber("京A12345");
        vehicleInfo.setVehicleModel("比亚迪秦PLUS DM-i");
        repairOrder.setVehicleInfo(vehicleInfo);

        // 设置供应商信息
        RepairOrderRequest.SupplierInfo supplierInfo = new RepairOrderRequest.SupplierInfo();
        supplierInfo.setSupplierId(123L);
        supplierInfo.setSupplierName("北京XX汽车维修有限公司");
        repairOrder.setSupplierInfo(supplierInfo);

        // 验证数据
        assertEquals("FF202501200001", repairOrder.getRepairOrderCode());
        assertEquals(new BigDecimal("5000.00"), repairOrder.getEstimatedAmount());
        assertEquals("京A12345", repairOrder.getVehicleInfo().getPlateNumber());
        assertEquals(Long.valueOf(123), repairOrder.getSupplierInfo().getSupplierId());
    }

    @Test
    @DisplayName("测试审核回调请求对象构建")
    void testAuditCallbackRequest() {
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        AuditCallbackRequest auditCallback = new AuditCallbackRequest(
            "FF202501200001",
            true,
            "审核通过",
            currentTime,
            "张三"
        );

        assertEquals("FF202501200001", auditCallback.getRepairOrderCode());
        assertTrue(auditCallback.getAuditResult());
        assertEquals("审核通过", auditCallback.getAuditComment());
        assertEquals(currentTime, auditCallback.getAuditTime());
        assertEquals("张三", auditCallback.getAuditor());
    }
}
```

## 8. 快速开始指南

### 8.1 环境准备

1. **JDK版本**: JDK 8 或以上
2. **Maven依赖**: 添加上述依赖到项目中
3. **网络环境**: 确保能访问E出行系统接口地址

### 8.2 配置信息

在 `application.properties` 中添加以下配置：

```properties
# 泛泛平台OpenAPI配置
fanfan.platform.base-url=https://qtn2ktw9-7331.asse.devtunnels.ms/app
fanfan.platform.app-id=fanfan_platform
fanfan.platform.app-secret=fanfan_platform_secret_key_2025
fanfan.platform.sm4-key=62636465666768693031323334353637
fanfan.platform.sm4-iv=37363534333231303938373635343332
fanfan.platform.request-expire-seconds=600
```

### 8.3 快速测试

```java
public class QuickTest {
    public static void main(String[] args) throws Exception {
        // 1. 创建客户端
        FanFanOpenApiClient client = new FanFanOpenApiClient(
            "https://qtn2ktw9-7331.asse.devtunnels.ms/app",
            "fanfan_platform",
            "fanfan_platform_secret_key_2025",
            "62636465666768693031323334353637",
            "37363534333231303938373635343332"
        );

        // 2. 测试加密解密
        String testData = "Hello World";
        String encrypted = client.encrypt(testData);
        String decrypted = client.decrypt(encrypted);
        System.out.println("加密解密测试: " + testData.equals(decrypted));

        // 3. 测试签名计算
        String signature = client.calculateSignature("1234567890", "test_nonce", encrypted);
        System.out.println("签名长度: " + signature.length() + " (应为64)");
    }
}
```

## 10. 泛泛平台需要提供的接口

### 10.1 接收维修订单提交

*   **接口地址：** `https://fanfan.example.com/api/repair/order/submit`
*   **请求方法：** POST
*   **功能说明：** 接收E出行系统提交的维修订单完成信息

**请求参数（解密后）：**

```json
{
  "auditType": "预估报价", // 审核类型（预估报价、认质认价、维修厂填报、维修厂整改提交）
  "repairOrderCode": "FF202501200001", // 维修订单编号
  "initType": "新增发起", // 发起类型（新增发起/重新提交）
  "supplierInfo": {
    "supplierId": 456,
    "supplierName": "XX维修厂"
  },
  "vehicleInfo": {
    "plateNumber": "京A12345",
    "vehicleModel": "比亚迪秦PLUS",
    "vinCode": "LGWEF4A59LG123456"
  },
  "repairResult": {
    "actualAmount": 4800.00,
    "actualMaintenanceAmount": 4800.00,
    "actualLaborAmount": 1800.00,
    "actualPartsAmount": 3000.00,
    "actualMaintenancePartsAmount": 3000.00,
    "completedTime": "2025-01-20 15:30:00",
    "repairContent": "更换刹车片、机油保养",
    "attachmentInformations": [
        {
            "attachmentBase64": "图片base64",
            "type": "维修"
        },
        {
            "attachmentBase64": "图片base64",
            "type": "维修"
        },
        {
            "attachmentBase64": "图片base64",
            "type": "保养"
        },
        {
            "attachmentBase64": "图片base64",
            "type": "保养"
        }
    ]
  },
  "partsUsed": [
    {
      "partsName": "刹车片",
      "partsCode": "BP001",
      "sourceType": "认质认价",
      "partsAttributes": "维修",
      "quantity": 4,
      "unitPrice": 150.00,
      "taxRate": 0.1,
      "taxUnitPrice": 165.00,
      "taxTotalPrice": 660.00,
      "taxTotalPrice": 660.00,
      "executePrice": 4800.00,
      "remark": "备注",
      "isTotalPriceLowest": true,
      "recentThreeMonthsRepairCount": 2
    }
  ],
  "laborHours": [
    {
      "laborName": "刹车片更换",
      "laborCode": "LH001",
      "partsName": "刹车片",
      "partsCode": "BP001",
      "sourceType": "竞价报价",
      "hours": 2.0,
      "unitPrice": 100.00,
      "taxRate": 0.1,
      "taxUnitPrice": 110.00,
      "taxTotalPrice": 220.00,
      "taxTotalPrice": 220.00,
      "executePrice": 4800.00,
      "remark": "备注",
      "isTotalPriceLowest": true,
    }
  ],
  //竞价报价相关的(5家供应商报价信息)
  "bidQuotationResponseInfos": [
    {
      "supplierInfo": {
        "supplierId": 123,
        "supplierName": "北京XX汽车维修有限公司",
        "isLowestTotalPrice": true,
        "partsCount": 2,
        "executePartsTotalPrice": 660.00,
        "executeLaborTotalPrice": 220.00
      },
      "partsUsedList": [
        {
          "partsName": "刹车片",
          "partsCode": "BP001",
          "quantity": 4,
          "partsAttributes": "维修",
          "unitPrice": 150.00,
          "taxRate": 0.1,
          "taxUnitPrice": 165.00,
          "taxTotalPrice": 660.00,
          "taxTotalPrice": 660.00,
          "executePrice": 4800,
          "remark": "原厂配件",
          "isTotalPriceLowest": false,
          "recentThreeMonthsRepairCount": 2
        }
      ],
      "laborHours": [
        {
          "laborName": "刹车片更换",
          "laborCode": "LH001",
          "partsName": "刹车片",
          "partsCode": "BP001",
          "hours": 2.0,
          "unitPrice": 100.00,
          "taxRate": 0.1,
          "taxUnitPrice": 110.00,
          "taxTotalPrice": 220.00,
          "taxTotalPrice": 220.00,
          "remark": "标准工时",
          "executePrice": 4800.00,
          "isTotalPriceLowest": false
        }
      ]
    },
    {
      "supplierInfo": {
        "supplierId": 124,
        "supplierName": "上海XX汽修中心",
        "isLowestTotalPrice": false,
        "partsCount": 2,
        "executePartsTotalPrice": 660.00,
        "executeLaborTotalPrice": 220.00
      },
      "partsUsedList": [
        {
          "partsName": "刹车片",
          "partsCode": "BP001",
          "quantity": 4,
          "partsAttributes": "维修",
          "unitPrice": 145.00,
          "taxRate": 0.1,
          "taxUnitPrice": 159.50,
          "taxTotalPrice": 638.00,
          "taxTotalPrice": 638.00,
          "executePrice": 4800.00,
          "remark": "优质替代品",
          "isTotalPriceLowest": true,
          "recentThreeMonthsRepairCount": 5
        }
      ],
      "laborHours": [
        {
          "laborName": "刹车片更换",
          "laborCode": "LH001",
          "partsName": "刹车片",
          "partsCode": "BP001",
          "hours": 1.8,
          "unitPrice": 95.00,
          "taxRate": 0.1,
          "taxUnitPrice": 104.50,
          "taxTotalPrice": 188.10,
          "taxTotalPrice": 188.10,
          "executePrice": 4800.00,
          "remark": "高效工时",
          "isTotalPriceLowest": true
        }
      ]
    },
    {
      "supplierInfo": {
        "supplierId": 125,
        "supplierName": "广州XX汽车服务",
        "isLowestTotalPrice": false,
        "partsCount": 2,
        "executePartsTotalPrice": 660.00,
        "executeLaborTotalPrice": 220.00
      },
      "partsUsedList": [
        {
          "partsName": "刹车片",
          "partsCode": "BP001",
          "quantity": 4,
          "partsAttributes": "维修",
          "unitPrice": 155.00,
          "taxRate": 0.1,
          "taxUnitPrice": 170.50,
          "taxTotalPrice": 682.00,
          "taxTotalPrice": 682.00,
          "executePrice": 4800.00,
          "remark": "进口配件",
          "isTotalPriceLowest": false,
          "recentThreeMonthsRepairCount": 1
        }
      ],
      "laborHours": [
        {
          "laborName": "刹车片更换",
          "laborCode": "LH001",
          "partsName": "刹车片",
          "partsCode": "BP001",
          "hours": 2.2,
          "unitPrice": 105.00,
          "taxRate": 0.1,
          "taxUnitPrice": 115.50,
          "taxTotalPrice": 254.10,
          "taxTotalPrice": 254.10,
          "executePrice": 4800.00,
          "remark": "精细施工",
          "isTotalPriceLowest": false
        }
      ]
    },
    {
      "supplierInfo": {
        "supplierId": 126,
        "supplierName": "深圳XX汽车维修",
        "isLowestTotalPrice": false,
        "partsCount": 2,
        "executePartsTotalPrice": 660.00,
        "executeLaborTotalPrice": 220.00
      },
      "partsUsedList": [
        {
          "partsName": "刹车片",
          "partsCode": "BP001",
          "quantity": 4,
          "partsAttributes": "保养",
          "unitPrice": 148.00,
          "taxRate": 0.1,
          "taxUnitPrice": 162.80,
          "taxTotalPrice": 651.20,
          "taxTotalPrice": 651.20,
          "executePrice": 4800.00,
          "remark": "国产优质",
          "isTotalPriceLowest": false,
          "recentThreeMonthsRepairCount": 3
        }
      ],
      "laborHours": [
        {
          "laborName": "刹车片更换",
          "laborCode": "LH001",
          "partsName": "刹车片",
          "partsCode": "BP001",
          "hours": 1.9,
          "unitPrice": 98.00,
          "taxRate": 0.1,
          "taxUnitPrice": 107.80,
          "taxTotalPrice": 204.82,
          "taxTotalPrice": 204.82,
          "executePrice": 4800.00,
          "remark": "快速服务",
          "isTotalPriceLowest": false
        }
      ]
    },
    {
      "supplierInfo": {
        "supplierId": 127,
        "supplierName": "成都XX汽车技术服务",
        "isLowestTotalPrice": false,
        "partsCount": 2,
        "executePartsTotalPrice": 660.00,
        "executeLaborTotalPrice": 220.00
      },
      "partsUsedList": [
        {
          "partsName": "刹车片",
          "partsCode": "BP001",
          "quantity": 4,
          "partsAttributes": "维修",
          "unitPrice": 152.00,
          "taxRate": 0.1,
          "taxUnitPrice": 167.20,
          "taxTotalPrice": 668.80,
          "taxTotalPrice": 668.80,
          "executePrice": 4800.00,
          "remark": "高性能配件",
          "isTotalPriceLowest": false,
          "recentThreeMonthsRepairCount": 4
        }
      ],
      "laborHours": [
        {
          "laborName": "刹车片更换",
          "laborCode": "LH001",
          "partsName": "刹车片",
          "partsCode": "BP001",
          "hours": 2.1,
          "unitPrice": 102.00,
          "taxRate": 0.1,
          "taxUnitPrice": 112.20,
          "taxTotalPrice": 235.62,
          "taxTotalPrice": 235.62,
          "executePrice": 4800.00,
          "remark": "专业施工",
          "isTotalPriceLowest": false
        }
      ]
    }
  ]
}
```

**响应参数（解密后）：**

```json
{
  "success": true,
  "message": "维修订单接收成功",
  "data": {
    "orderId": "泛泛平台订单ID",
    "status": "待验收"
  }
}
```

**字段说明：**

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| repairOrderCode | String | 是 | 维修订单编号 |
| initType | String | 否 | 发起类型（新增发起/重新提交） |
| supplierInfo | Object | 是 | 供应商信息 |
| supplierInfo.supplierId | String | 是 | 供应商ID |
| supplierInfo.supplierName | String | 是 | 供应商名称 |
| supplierInfo.executePartsTotalPrice | BigDecimal | 否 | 执行配件总报价 |
| supplierInfo.executeLaborTotalPrice | BigDecimal | 否 | 执行工时总报价 |
| vehicleInfo | Object | 是 | 车辆信息 |
| vehicleInfo.plateNumber | String | 是 | 车牌号 |
| vehicleInfo.vehicleModel | String | 是 | 车辆型号 |
| vehicleInfo.vinCode | String | 否 | 车架号 |
| repairResult | Object | 是 | 维修结果 |
| repairResult.actualAmount | BigDecimal | 否 | 实际维修总金额 |
| repairResult.actualMaintenanceAmount | BigDecimal | 否 | 实际保养总金额 |
| repairResult.actualLaborAmount | BigDecimal | 否 | 实际工时费 |
| repairResult.actualPartsAmount | BigDecimal | 否 | 实际配件维修金额 |
| repairResult.actualMaintenancePartsAmount | BigDecimal | 否 | 实际配件保养金额 |
| repairResult.completedTime | String | 否 | 维修完成时间，格式：yyyy-MM-dd HH:mm:ss |
| repairResult.repairContent | String | 否 | 维修内容描述 |
| repairResult.attachmentInformations | Array | 否 | 附件信息数组 |
| attachmentInformations[].attachmentBase64 | String | 是 | 附件base64 |
| attachmentInformations[].type | String | 是 | 类型（维修/保养） |
| partsUsed | Array | 否 | 使用的配件列表 |
| partsUsed[].partsName | String | 是 | 配件名称 |
| partsUsed[].partsCode | String | 是 | 配件编码 |
| partsUsed[].sourceType | String | 是 | 来源类型（招标合同、竞价报价、认质认价） |
| partsUsed[].partsAttributes | String | 是 | 配件属性（维修/保养） |
| partsUsed[].quantity | Integer | 否 | 数量 |
| partsUsed[].unitPrice | BigDecimal | 是 | 不含税单价 |
| partsUsed[].taxRate | BigDecimal | 是 | 税率 |
| partsUsed[].taxUnitPrice | BigDecimal | 是 | 含税单价 |
| partsUsed[].taxTotalPrice | BigDecimal | 是 | 不含税总价 |
| partsUsed[].taxTotalPrice | BigDecimal | 是 | 含税总价 |
| partsUsed[].executePrice | BigDecimal | 是 | 执行价（最终价格） |
| partsUsed[].remark | String | 否 | 备注 |
| partsUsed[].isTotalPriceLowest | Boolean | 否 | 是否总价最低 |
| partsUsed[].recentThreeMonthsRepairCount | Integer | 否 | 近3个月维修次数 |
| laborHours | Array | 否 | 工时列表 |
| laborHours[].laborName | String | 是 | 工时名称 |
| laborHours[].laborCode | String | 是 | 工时编码 |
| laborHours[].partsName | String | 否 | 配件名称 |
| laborHours[].partsCode | String | 否 | 配件编码 |
| laborHours[].sourceType | String | 是 | 来源类型（招标合同、竞价报价、认质认价） |
| laborHours[].hours | Double | 是 | 工时数 |
| laborHours[].unitPrice | BigDecimal | 是 | 不含税单价 |
| laborHours[].taxRate | BigDecimal | 是 | 税率 |
| laborHours[].taxUnitPrice | BigDecimal | 是 | 含税单价 |
| laborHours[].taxTotalPrice | BigDecimal | 是 | 不含税总价 |
| laborHours[].taxTotalPrice | BigDecimal | 是 | 含税总价 |
| laborHours[].executePrice | BigDecimal | 是 | 执行价（最终价格） |
| laborHours[].remark | String | 否 | 备注 |
| laborHours[].isTotalPriceLowest | Boolean | 否 | 是否总价最低 |
| bidQuotationResponseInfos | Array | 否 | 竞价报价响应信息列表 |
| bidQuotationResponseInfos[].supplierInfo | Object | 是 | 供应商信息 |
| bidQuotationResponseInfos[].supplierInfo.supplierId | String | 是 | 供应商ID |
| bidQuotationResponseInfos[].supplierInfo.supplierName | String | 是 | 供应商名称 |
| bidQuotationResponseInfos[].supplierInfo.isLowestTotalPrice | Boolean | 否 | 是否总价最低 |
| bidQuotationResponseInfos[].supplierInfo.partsCount | Integer | 否 | 涉及配件数 |
| bidQuotationResponseInfos[].executePartsTotalPrice | BigDecimal | 否 | 执行配件总报价 |
| bidQuotationResponseInfos[].executeLaborTotalPrice | BigDecimal | 否 | 执行工时总报价 |
| bidQuotationResponseInfos[].partsUsedList | Array | 否 | 使用的配件列表 |
| bidQuotationResponseInfos[].partsUsedList[].partsName | String | 是 | 配件名称 |
| bidQuotationResponseInfos[].partsUsedList[].partsCode | String | 是 | 配件编码 |
| bidQuotationResponseInfos[].partsUsedList[].quantity | Integer | 否 | 数量 |
| bidQuotationResponseInfos[].partsUsedList[].partsAttributes | String | 否 | 配件属性（维修/保养） |
| bidQuotationResponseInfos[].partsUsedList[].unitPrice | BigDecimal | 是 | 不含税单价 |
| bidQuotationResponseInfos[].partsUsedList[].taxRate | BigDecimal | 是 | 税率 |
| bidQuotationResponseInfos[].partsUsedList[].taxUnitPrice | BigDecimal | 是 | 含税单价 |
| bidQuotationResponseInfos[].partsUsedList[].totalPrice | BigDecimal | 是 | 不含税总价 |
| bidQuotationResponseInfos[].partsUsedList[].taxTotalPrice | BigDecimal | 是 | 含税总价 |
| bidQuotationResponseInfos[].partsUsedList[].executePrice | BigDecimal | 是 | 执行价（最终价格） |
| bidQuotationResponseInfos[].partsUsedList[].remark | String | 否 | 备注 |
| bidQuotationResponseInfos[].partsUsedList[].isTotalPriceLowest | Boolean | 否 | 是否总价最低 |
| bidQuotationResponseInfos[].partsUsedList[].recentThreeMonthsRepairCount | Integer | 否 | 近3个月维修次数 |
| bidQuotationResponseInfos[].laborHours | Array | 否 | 工时列表 |
| bidQuotationResponseInfos[].laborHours[].laborName | String | 是 | 工时名称 |
| bidQuotationResponseInfos[].laborHours[].laborCode | String | 是 | 工时编码 |
| bidQuotationResponseInfos[].laborHours[].partsName | String | 否 | 配件名称 |
| bidQuotationResponseInfos[].laborHours[].partsCode | String | 否 | 配件编码 |
| bidQuotationResponseInfos[].laborHours[].hours | Double | 是 | 工时数 |
| bidQuotationResponseInfos[].laborHours[].unitPrice | BigDecimal | 是 | 不含税单价 |
| bidQuotationResponseInfos[].laborHours[].taxRate | BigDecimal | 是 | 税率 |
| bidQuotationResponseInfos[].laborHours[].taxUnitPrice | BigDecimal | 是 | 含税单价 |
| bidQuotationResponseInfos[].laborHours[].totalPrice | BigDecimal | 是 | 不含税总价 |
| bidQuotationResponseInfos[].laborHours[].taxTotalPrice | BigDecimal | 是 | 含税总价 |
| bidQuotationResponseInfos[].laborHours[].executePrice | BigDecimal | 是 | 执行价（最终价格） |
| bidQuotationResponseInfos[].laborHours[].remark | String | 否 | 备注 |
| bidQuotationResponseInfos[].laborHours[].isTotalPriceLowest | Boolean | 否 | 是否总价最低 |

**响应字段说明：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | Boolean | 是否成功 |
| message | String | 返回消息 |
| data | Object | 返回数据 |
| data.orderId | String | 泛泛平台订单ID |
| data.status | String | 订单状态 |

## 9. 注意事项

### 9.1 安全要求

- **密钥保护**: SM4密钥和应用密钥必须妥善保管，不得泄露
- **HTTPS传输**: 生产环境必须使用HTTPS协议
- **时间同步**: 确保系统时间与服务器时间同步，误差不超过10分钟

### 9.2 性能优化

- **连接复用**: 建议使用连接池复用HTTP连接
- **异步处理**: 对于大量请求，建议使用异步处理方式
- **缓存机制**: 可以缓存客户端实例，避免重复创建

### 9.3 错误处理

- **重试机制**: 对于网络错误，建议实现重试机制
- **日志记录**: 记录详细的请求和响应日志，便于问题排查
- **监控告警**: 建议添加接口调用监控和告警机制