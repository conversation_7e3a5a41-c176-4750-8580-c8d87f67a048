<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.car.business.car.mapper.CarModelInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.car.business.car.entity.CarModelInfo">
        <id column="id" property="id" />
        <result column="car_id" property="carId" />
        <result column="year_model" property="yearModel" />
        <result column="config_name" property="configName" />
        <result column="is_test_drive" property="isTestDrive" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, car_id, year_model, config_name, is_test_drive, create_time, update_time
    </sql>

</mapper>
