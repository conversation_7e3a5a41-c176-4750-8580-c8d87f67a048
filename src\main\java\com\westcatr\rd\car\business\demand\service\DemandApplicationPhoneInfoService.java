package com.westcatr.rd.car.business.demand.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.car.business.demand.pojo.query.DemandApplicationPhoneInfoQuery;
import com.westcatr.rd.car.business.demand.entity.DemandApplicationPhoneInfo;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * <p>
 * 需求—供应商申请获取用户电话信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
public interface DemandApplicationPhoneInfoService extends IService<DemandApplicationPhoneInfo> {

    IPage<DemandApplicationPhoneInfo> entityPage(DemandApplicationPhoneInfoQuery query);

    DemandApplicationPhoneInfo getEntityById(Long id);

    boolean saveEntity(DemandApplicationPhoneInfo param);

    boolean updateEntity(DemandApplicationPhoneInfo param);

    boolean removeEntityById(Long id);
}
