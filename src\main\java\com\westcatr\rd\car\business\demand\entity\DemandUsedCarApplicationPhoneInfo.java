package com.westcatr.rd.car.business.demand.entity;

import java.io.Serializable;
import java.util.Date;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.web.excel.annotation.ExcelField;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 需求—二手车供应商申请获取用户电话信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("demand_used_car_application_phone_info")
@Schema(description = "需求—二手车供应商申请获取用户电话信息表")
public class DemandUsedCarApplicationPhoneInfo extends Model<DemandUsedCarApplicationPhoneInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @TableField("demand_id")
    private Long demandId;

    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Length(max = 65535, message = "长度不能超过65535", groups = { Insert.class, Update.class })
    @TableField("remarks")
    private String remarks;

    @TableField("supplier_id")
    private Long supplierId;

    @Schema(description = "标题")
    @ExcelField(name = "标题")
    @Length(max = 255, message = "标题长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("title_info")
    private String titleInfo;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
