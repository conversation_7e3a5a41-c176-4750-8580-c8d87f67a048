package com.westcatr.rd.car.business.car.service.impl;

import java.io.IOException;
import java.util.List;

import org.springframework.stereotype.Service;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.core.exception.IRuntimeException;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.car.business.car.entity.CarVehicleOperationStatisticsInfo;
import com.westcatr.rd.car.business.car.mapper.CarVehicleOperationStatisticsInfoMapper;
import com.westcatr.rd.car.business.car.pojo.dto.CarVehicleOperationStatisticsDto;
import com.westcatr.rd.car.business.car.pojo.query.CarVehicleOperationStatisticsInfoQuery;
import com.westcatr.rd.car.business.car.pojo.vo.CarVehicleOperationStatisticsInfoVO;
import com.westcatr.rd.car.business.car.service.CarVehicleOperationStatisticsInfoService;
import com.westcatr.rd.car.common.ExcelResponseHelper;

/**
 * <p>
 * 车型配件表—操作统计 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Service
public class CarVehicleOperationStatisticsInfoServiceImpl
        extends ServiceImpl<CarVehicleOperationStatisticsInfoMapper, CarVehicleOperationStatisticsInfo>
        implements CarVehicleOperationStatisticsInfoService {

    @Override
    public IPage<CarVehicleOperationStatisticsInfo> entityPage(CarVehicleOperationStatisticsInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<CarVehicleOperationStatisticsInfo>().create(query));
    }

    @Override
    public CarVehicleOperationStatisticsInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(CarVehicleOperationStatisticsInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(CarVehicleOperationStatisticsInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }

    @Override
    public CarVehicleOperationStatisticsDto getCarVehicleOperationStatisticsDto(
            CarVehicleOperationStatisticsInfoQuery query) {
        CarVehicleOperationStatisticsDto dto = new CarVehicleOperationStatisticsDto();
        AssociationQuery<CarVehicleOperationStatisticsInfoVO> associationQuery = new AssociationQuery<>(
                CarVehicleOperationStatisticsInfoVO.class);
        List<CarVehicleOperationStatisticsInfoVO> list = associationQuery.voList(query);
        if (list.isEmpty()) {
            return dto;
        }
        dto.setVehicleModelQueryCount(
                Math.toIntExact(list.stream().filter(vo -> vo.getOperationType().equals("车型查询")).count()));
        dto.setPartsLibraryQueryCount(
                Math.toIntExact(list.stream().filter(vo -> vo.getOperationType().equals("配件库查看")).count()));
        dto.setPartsQueryCount(
                Math.toIntExact(list.stream().filter(vo -> vo.getOperationType().equals("配件查询")).count()));
        dto.setPartsExportCount(
                Math.toIntExact(list.stream().filter(vo -> vo.getOperationType().equals("配件导出")).count()));
        return dto;
    }

    @Override
    public void exportExcel(CarVehicleOperationStatisticsInfoQuery query) {
        AssociationQuery<CarVehicleOperationStatisticsInfoVO> associationQuery = new AssociationQuery<>(
                CarVehicleOperationStatisticsInfoVO.class);
        List<CarVehicleOperationStatisticsInfoVO> list = associationQuery.voList(query);
        try {
            EasyExcel
                    .write(ExcelResponseHelper.getExcelResponse("车型配件表—操作统计数据").getOutputStream(),
                            CarVehicleOperationStatisticsInfoVO.class)
                    .sheet("数据")
                    .doWrite(list);
        } catch (IOException e) {
            e.printStackTrace();
            throw new IRuntimeException("导出数据失败");
        }
    }
}
