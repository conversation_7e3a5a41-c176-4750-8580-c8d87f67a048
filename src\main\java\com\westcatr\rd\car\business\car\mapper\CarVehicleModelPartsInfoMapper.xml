<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.car.business.car.mapper.CarVehicleModelPartsInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.car.business.car.entity.CarVehicleModelPartsInfo">
        <id column="id" property="id" />
        <result column="vehicle_model_id" property="vehicleModelId" />
        <result column="model_code" property="modelCode" />
        <result column="sub_group_code" property="subGroupCode" />
        <result column="sub_group_name" property="subGroupName" />
        <result column="part_code" property="partCode" />
        <result column="part_name" property="partName" />
        <result column="remarks" property="remarks" />
        <result column="original_price" property="originalPrice" />
        <result column="dealer_price" property="dealerPrice" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, vehicle_model_id, model_code, sub_group_code, sub_group_name, part_code, part_name, remarks, original_price, dealer_price, create_time, update_time
    </sql>

</mapper>
