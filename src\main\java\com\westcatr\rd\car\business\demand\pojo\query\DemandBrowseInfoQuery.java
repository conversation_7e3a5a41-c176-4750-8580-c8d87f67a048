package com.westcatr.rd.car.business.demand.pojo.query;

import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import com.westcatr.rd.boot.orm.dto.TimeDTO;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 需求—当前登录用户查看车辆次数信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="需求—当前登录用户查看车辆次数信息表查询对象")
public class DemandBrowseInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID=1L;

    @QueryCondition
    private Long carId;

    @QueryCondition
    private Long carModelId;

    @QueryCondition
    private Long id;

    @QueryCondition
    private Long userId;
}
