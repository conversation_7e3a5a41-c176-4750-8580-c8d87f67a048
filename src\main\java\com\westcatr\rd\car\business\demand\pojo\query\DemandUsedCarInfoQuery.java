package com.westcatr.rd.car.business.demand.pojo.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "二手车需求信息表查询对象")
public class DemandUsedCarInfoQuery extends PageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @QueryCondition
    private Long createUserId;

    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String demandTitle;

    @QueryCondition
    private String descriptionInfo;

    @QueryCondition
    private String oldCarModel;

    @QueryCondition
    private String contactName;

    @QueryCondition
    private String contactPhone;

    @QueryCondition
    private Integer statusInfo;

    @QueryCondition
    private Long supplierId;

    @QueryCondition
    private Date createTime;

    @QueryCondition
    private Date updateTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "开始时间")
    @QueryCondition(condition = QueryCondition.Condition.GE, field = "create_time")
    private Date beginTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "结束时间")
    @QueryCondition(condition = QueryCondition.Condition.LE, field = "create_time")
    private Date endTime;

    @Schema(description = "时间排序，1倒叙，0正序")
    @QueryCondition(condition = QueryCondition.Condition.DEFAULT, field = "create_time", sort = QueryCondition.Sort.AUTO)
    private Integer timeSort;

    @Schema(description = "id排序，1倒叙，0正序")
    @QueryCondition(condition = QueryCondition.Condition.DEFAULT, field = "id", sort = QueryCondition.Sort.AUTO)
    private Integer idSort;
}
