<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.car.business.basic.mapper.BasicComplaintConfigInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.car.business.basic.entity.BasicComplaintConfigInfo">
        <id column="id" property="id" />
        <result column="complaint_phone" property="complaintPhone" />
        <result column="complaint_email" property="complaintEmail" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, complaint_phone, complaint_email, created_time, updated_time
    </sql>

</mapper>
