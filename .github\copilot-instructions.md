## 🏗️ 项目架构

### 📦 核心技术栈
- **框架**: westcatr-boot 2.1.7 (基于Spring Boot的企业级定制框架)
- **Java版本**: Java 17
- **构建工具**: Maven
- **数据库**: MySQL 8.3.0 (mysql-connector-j)
- **ORM框架**: MyBatis Plus 3.x (westcatr定制版)
- **文档处理**: 
  - Excel: Apache POI 5.2.2, EasyExcel 3.1.2, EasyPOI 4.3.0
  - 模板引擎: Thymeleaf
- **安全框架**: Spring Security (westcatr定制版) + RSA/SM2加密
- **缓存**: Redis (可选禁用)
- **任务调度**: Spring Scheduling + @Scheduled注解
- **工具库**: Hutool 5.8.36 (优先使用)、<PERSON>(JSON处理)、Gson 2.11.0

### 🗂️ 项目结构

```
src/main/java/com/westcatr/rd/car/
├── 📁 business/          # 核心业务模块
│   ├── 📁 audit/         # 审计管理
│   ├── 📁 basic/         # 基础信息管理
│   ├── 📁 car/           # 车辆信息管理
│   ├── 📁 demand/        # 需求管理
│   ├── 📁 fanfan/        # 翻翻业务
│   ├── 📁 filesync/      # 文件同步
│   ├── 📁 flow/          # 流程管理
│   ├── 📁 myflow/        # 我的流程
│   ├── 📁 openapi/       # 开放接口
│   ├── 📁 org/           # 组织架构管理
│   ├── 📁 param/         # 参数管理
│   ├── 📁 prize/         # 购车有奖
│   ├── 📁 repair/        # 维修管理
│   ├── 📁 supplier/      # 供应商管理
│   ├── 📁 testdrive/     # 试驾管理
│   └── 📁 usedcar/       # 二手车管理
├── 📁 common/            # 公共工具
├── 📁 configs/           # 扩展配置
├── 📁 enums/             # 枚举类
├── 📁 scheduledtask/     # 定时任务
└── 📁 utils/             # 工具类集合
```

## 🎯 核心业务模块分析

### 1. � 车辆信息管理 (car)
- **功能**: 车辆基础信息、车型信息、参数配置管理
- **特色**: 支持多种车辆参数、展示配置、流程审批
- **流程**: 车辆录入 → 参数配置 → 审核 → 上架展示

### 2. 🏪 供应商管理 (supplier)
- **功能**: 供应商信息管理、资质审核
- **包含**: 供应商注册、认证、业务管理
- **特色**: 支持供应商自助注册、多级审核

### 3. � 维修管理 (repair)
- **功能**: 维修工单、竞价报价、对账管理
- **特色**: 支持维修工单流程、自动对账单生成
- **流程**: 工单创建 → 竞价报价 → 维修执行 → 对账结算

### 4. 🎯 需求管理 (demand)
- **功能**: 用户需求收集、分析、匹配
- **包含**: 需求申请、车辆推荐、需求跟踪
- **特色**: 智能需求匹配、多维度筛选

### 5. 🚙 试驾管理 (testdrive)
- **功能**: 试驾预约、评估、反馈管理
- **流程**: 试驾申请 → 预约安排 → 试驾执行 → 评估反馈

### 6. 🏆 购车有奖 (prize)
- **功能**: 购车奖励活动管理
- **特色**: 奖励规则配置、自动发放机制

## 🛠️ 技术特色

### 📝 文档处理能力
- **Excel处理**: 
  - Apache POI 5.2.2 (Excel读写)
  - EasyExcel 3.1.2 (阿里Excel工具)
  - EasyPOI 4.3.0 (企业级Excel处理)
- **模板引擎**: 
  - Thymeleaf (Web模板引擎)
- **邮件服务**: 
  - Jakarta Mail 2.0.1 (邮件发送)

### 🔐 安全特性
- **加密**: RSA + SM2双重加密
- **认证**: 滑动验证码 + 密码加密
- **权限**: 基于角色的访问控制
- **数据权限**: 支持数据权限管理

### 🌐 集成能力
- **开放接口**: 
  - 标准REST API
  - 国密加密通信支持
  - 第三方系统集成
- **文件管理**: 
  - 文件上传下载
  - 切片上传支持
  - 文件同步机制

## 📊 数据管理

### 🗄️ 数据源配置
- **主数据源**: 支持MySQL数据库
- **连接池**: HikariCP
- **ORM**: MyBatis Plus (westcatr定制版)

### 📈 统计分析
- **车辆统计**: 车辆信息统计分析
- **需求统计**: 用户需求统计
- **维修统计**: 维修工单统计
- **供应商统计**: 供应商业务统计

## 🔧 开发工具

### 📚 代码生成
- **MyBatis Plus Generator**: 自动生成Entity、Mapper、Service
- **模板管理**: 支持多种业务模板

### 🧪 测试支持
- **单元测试**: Spring Test集成
- **接口文档**: Swagger/Knife4j自动生成

## 🚀 部署配置

### 🌍 环境配置
- **开发环境**: mac, maclocal, machome, win
- **测试环境**: test
- **生产环境**: prod, prod2
- **默认端口**: 7330
- **上下文路径**: /app
- **应用名**: e_car

### 📦 Maven仓库
- **内网仓库**: http://10.36.11.223:8081/nexus/
- **阿里云仓库**: https://maven.aliyun.com/repository/public
- **第三方仓库**: e-iceblue, Spring官方仓库