package com.westcatr.rd.car.business.car.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.car.business.car.entity.CarFlowDataInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="VO对象")
public class CarFlowDataInfoVO extends CarFlowDataInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
