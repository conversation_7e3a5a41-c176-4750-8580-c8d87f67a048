package com.westcatr.rd.car.business.demand.entity;

import java.io.Serializable;
import java.util.Date;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.car.common.PhoneNumberSerializer;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 需求—基本信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("demand_info")
@Schema(description = "需求—基本信息表")
public class DemandInfo extends Model<DemandInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("create_user_id")
    private Long createUserId;

    @TableField("supplier_id")
    private Long supplierId;

    @Schema(description = "需求类型")
    @TableField("requirement_type")
    private String requirementType;

    @Schema(description = "需求标题")
    @Length(max = 255, message = "需求标题长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("demand_title")
    private String demandTitle;

    @Schema(description = "描述内容")
    @Length(max = 65535, message = "描述内容长度不能超过65535", groups = { Insert.class, Update.class })
    @TableField("description_info")
    private String descriptionInfo;

    @TableField("car_id")
    private Long carId;

    @TableField("car_model_id")
    private Long carModelId;

    @Schema(description = "联系电话")
    @Length(max = 255, message = "联系电话长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("contact_phone")
    @JsonSerialize(using = PhoneNumberSerializer.class)
    private String contactPhone;

    @Schema(description = "联系人员")
    @TableField("contact_personnel")
    private String contactPersonnel;

    @Schema(description = "旧车车型")
    @TableField("use_car_model")
    private String useCarModel;

    @TableField("status_info")
    private Integer statusInfo;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
