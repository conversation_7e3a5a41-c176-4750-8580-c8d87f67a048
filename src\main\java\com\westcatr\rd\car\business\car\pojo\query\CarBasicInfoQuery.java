package com.westcatr.rd.car.business.car.pojo.query;

import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import com.westcatr.rd.boot.orm.dto.TimeDTO;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 车辆—车辆基本信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="车辆—车辆基本信息表查询对象")
public class CarBasicInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID=1L;

    @QueryCondition
    private Long id;

    @QueryCondition
    private String brandModel;
}
