<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.car.business.audit.mapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.car.business.audit.entity.AuditLogInfo">
        <id column="id" property="id" />
        <id column="user_id" property="userId" />
        <result column="username" property="username" />
        <result column="operation_time" property="operationTime" />
        <result column="ip_address" property="ipAddress" />
        <result column="operation_type" property="operationType" />
        <result column="event_description" property="eventDescription" />
        <result column="business_id" property="businessId" />
        <result column="result" property="result" />
        <result column="error_message" property="errorMessage" />
        <result column="request_params" property="requestParams" />
        <result column="class_name" property="className" />
        <result column="method_name" property="methodName" />
        <result column="sync_status" property="syncStatus" />
        <result column="sync_time" property="syncTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List"> id, user_id, username, operation_time, ip_address, operation_type,
        event_description, business_id, result, error_message, request_params, class_name,
        method_name, sync_status, sync_time </sql>

</mapper>