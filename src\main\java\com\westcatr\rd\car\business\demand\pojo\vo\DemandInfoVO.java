package com.westcatr.rd.car.business.demand.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.car.business.car.entity.CarInfo;
import com.westcatr.rd.car.business.car.entity.CarModelInfo;
import com.westcatr.rd.car.business.demand.entity.DemandApplicationPhoneInfo;
import com.westcatr.rd.car.business.demand.entity.DemandInfo;
import com.westcatr.rd.car.business.org.entity.OrgUserInfo;
import com.westcatr.rd.car.business.supplier.entity.SupplierInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 需求—基本信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "需求—基本信息表VO对象")
public class DemandInfoVO extends DemandInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @JoinSelect(joinClass = OrgUserInfo.class, mainId = "createUserId", field = "full_name")
    private String createUserFullName;

    @JoinSelect(joinClass = CarModelInfo.class, mainId = "carModelId")
    private CarModelInfo carModelInfo;

    @JoinSelect(joinClass = CarInfo.class, mainId = "carId", field = "brand_model")
    private String brandModel;

    @TableField(exist = false)
    @Schema(description = "是否显示获取手机号码的按钮")
    private boolean tfShowGetPhone = true;

    @JoinSelect(joinClass = DemandApplicationPhoneInfo.class, relationId = "demand_id")
    private List<DemandApplicationPhoneInfo> demandApplicationPhoneInfos;

    @JoinSelect(joinClass = SupplierInfo.class, mainId = "supplierId")
    private SupplierInfo supplierInfo;

    @TableField(exist = false)
    private List<SupplierInfo> supplierInfosByCrateUser;

}
