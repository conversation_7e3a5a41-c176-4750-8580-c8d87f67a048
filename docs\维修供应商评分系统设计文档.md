# 🔧 维修供应商评分系统设计文档

## 📋 文档信息

| 项目 | 信息 |
|------|------|
| **文档名称** | 维修供应商评分系统设计文档 |
| **作者** | liusheng |
| **创建日期** | 2025年6月13日 |
| **版本** | v1.0 |
| **系统名称** | E出行供应商管理平台 |

---

## 📖 目录

- [1. 系统概述](#1-系统概述)
- [2. 评分规则](#2-评分规则)
- [3. 供应商分级体系](#3-供应商分级体系)
- [4. 技术实现](#4-技术实现)
- [5. 数据库设计](#5-数据库设计)
- [6. API接口](#6-api接口)
- [7. 业务流程](#7-业务流程)
- [8. 统计分析](#8-统计分析)
- [9. 运营管理](#9-运营管理)

---

## 1. 系统概述

### 🎯 系统目标

维修供应商评分系统是E出行平台供应商质量管理的核心模块，通过自动化和人工评分相结合的方式，对维修供应商的服务质量进行量化评估，实现供应商的分级管理和质量提升。

### 🌟 核心特性

- ⚡ **自动化评分**：基于业务行为自动触发评分机制
- 🎯 **精准分级**：三级供应商分类管理体系
- 📊 **数据可视化**：多维度统计分析和趋势展示
- 🔍 **全程追溯**：完整的评分操作审计日志
- 📈 **持续优化**：月度评分趋势分析

### 📊 评分维度

| 评分类型 | 触发方式 | 分数范围 | 说明 |
|---------|---------|---------|------|
| **系统自动评分** | 业务事件触发 | -10 ~ +5分 | 基于供应商响应行为 |
| **人工管理评分** | 管理员操作 | -15 ~ 0分 | 基于违规行为扣分 |

---

## 2. 评分规则

### 🤖 自动评分规则

#### ✅ 按时报价（+5分）

**触发条件**：
- 供应商在规定截止时间内提交报价
- 报价内容完整有效
- 系统自动检测并记录

**评分逻辑**：
```java
// 处理按时提交报价的情况 - 加5分
private void handleTimelyQuotes(Date now) {
    // 查询需要加分的报价记录
    List<CarVehicleBiddingInfo> timelyBiddings = this.list(
        new LambdaQueryWrapper<CarVehicleBiddingInfo>()
            .eq(CarVehicleBiddingInfo::getQuotationStatus, QuotationStatusEnum.COMPLETED.getCode())
            .le(CarVehicleBiddingInfo::getUpdateTime, now)
    );
    
    // 创建评分记录并更新供应商总分
    for (CarVehicleBiddingInfo bidding : timelyBiddings) {
        CarVehicleBiddingScoreInfo scoreInfo = new CarVehicleBiddingScoreInfo();
        scoreInfo.setOrderId(bidding.getOrderId());
        scoreInfo.setSupplierId(bidding.getSupplierId());
        scoreInfo.setScore(5); // 加5分
        scoreInfo.setViolationType("响应成功");
        scoreInfo.setDescribeInfo("维修厂按时提交报价，系统自动加5分");
        
        // 保存评分记录并更新供应商总分
        carVehicleBiddingScoreInfoService.saveEntity(scoreInfo);
        updateSupplierTotalScore(bidding.getSupplierId(), 5);
    }
}
```

#### ⚠️ 响应超时（-10分）

**触发条件**：
- 供应商超过规定截止时间未提交报价
- 系统定时任务检测超时情况
- 自动标记为"已过期"状态

**评分逻辑**：
```java
// 处理超时未报价的情况 - 扣10分
private void handleTimeoutQuotes(Date now) {
    // 查询超时未报价的记录
    List<CarVehicleBiddingInfo> timeoutBiddings = this.list(
        new LambdaQueryWrapper<CarVehicleBiddingInfo>()
            .eq(CarVehicleBiddingInfo::getQuotationStatus, QuotationStatusEnum.PENDING.getCode())
            .lt(CarVehicleBiddingInfo::getQuotationDeadline, now)
    );
    
    for (CarVehicleBiddingInfo bidding : timeoutBiddings) {
        CarVehicleBiddingScoreInfo scoreInfo = new CarVehicleBiddingScoreInfo();
        scoreInfo.setOrderId(bidding.getOrderId());
        scoreInfo.setSupplierId(bidding.getSupplierId());
        scoreInfo.setScore(-10); // 扣10分
        scoreInfo.setViolationType("响应超时");
        scoreInfo.setDescribeInfo("维修厂超过截止时间未提交报价，系统自动扣10分");
        
        // 保存评分记录并更新供应商总分
        carVehicleBiddingScoreInfoService.saveEntity(scoreInfo);
        updateSupplierTotalScore(bidding.getSupplierId(), -10);
        
        // 更新报价状态为"已过期"
        bidding.setQuotationStatus(QuotationStatusEnum.EXPIRED.getCode());
        this.updateById(bidding);
    }
}
```

### 👨‍💼 人工评分规则

#### 🚫 恶意报价（-15分）

**触发条件**：
- 管理员识别出恶意报价行为
- 通过后台管理界面手动扣分
- 必须提供违规说明和证据

**操作流程**：
1. 管理员在供应商评分详情页面点击"扣分"
2. 选择违规类型为"恶意报价"
3. 系统自动设置扣分数值为15分
4. 填写详细的违规说明
5. 上传相关证据文件（可选）
6. 提交后系统记录评分变更

**前端实现**：
```tsx
// 违规类型选择自动设置扣分值
<ProFormSelect
  name={'violationType'}
  label="违规类型"
  rules={[{ required: true, message: '此项必填' }]}
  fieldProps={{
    showSearch: true,
    onChange(value, option) {
      formRef?.current?.setFieldValue(
        'score',
        value === '响应超时' ? 10 : 15, // 恶意报价扣15分
      );
    },
    options: [
      { label: '响应超时', value: '响应超时' },
      { label: '恶意报价', value: '恶意报价' },
    ],
  }}
/>
```

---

## 3. 供应商分级体系

### 🎯 分级标准

基于供应商总评分进行三级分类管理：

| 评分区间 | 等级标识 | 风险等级 | 管理策略 | 特征描述 |
|---------|---------|---------|---------|----------|
| 🔴 **< 60分** | 高风险供应商 | **高风险** | 重点监控 | 服务质量差，需要改进 |
| 🟡 **60-80分** | 观察期供应商 | **中风险** | 持续观察 | 服务质量一般，需要提升 |
| 🟢 **> 80分** | 优质供应商 | **低风险** | 优先合作 | 服务质量优秀，可信赖 |

### 📊 分级计算逻辑

```java
// 供应商分级计算
private String calculateRatingType(Integer fraction) {
    if (fraction == null) {
        return "未评级";
    }
    
    if (fraction < 60) {
        return "高风险供应商";
    } else if (fraction >= 60 && fraction <= 80) {
        return "观察期供应商";
    } else {
        return "优质供应商";
    }
}
```

### 🎯 分级应用场景

#### 🔴 高风险供应商管理
- ⚠️ **限制接单**：降低订单分配优先级
- 📞 **重点沟通**：主动联系了解问题原因
- 📈 **改进计划**：制定具体的服务提升方案
- 🔍 **密切监控**：增加质量检查频次

#### 🟡 观察期供应商管理
- 👀 **持续观察**：定期跟踪评分变化趋势
- 💡 **培训指导**：提供服务质量改进建议
- 📊 **数据分析**：分析问题根因并给出解决方案

#### 🟢 优质供应商管理
- 🏆 **优先合作**：优先分配订单资源
- 💰 **激励机制**：提供更好的合作条件
- 🌟 **标杆推广**：作为优秀案例进行推广

---

## 4. 技术实现

### 🏗️ 系统架构

```
前端展示层 (React + Ant Design Pro)
    ↓
API接口层 (Spring Boot + RESTful)
    ↓
业务服务层 (Service + 评分规则引擎)
    ↓
数据访问层 (MyBatis Plus + MySQL)
    ↓
数据存储层 (MySQL 8.3.0)
```

### 📋 核心实体类

#### 维修竞价评分实体
```java
@TableName("car_vehicle_bidding_score_info")
public class CarVehicleBiddingScoreInfo {
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;                    // 主键ID
    
    @TableField("supplier_id")
    private Long supplierId;            // 供应商ID
    
    @TableField("score")
    private Integer score;              // 评分（可正可负）
    
    @TableField("violation_type")
    private String violationType;       // 违规类型
    
    @TableField("describe_info")
    private String describeInfo;        // 违规说明
    
    @TableField("operator_user_id")
    private Long operatorUserId;        // 操作人ID
    
    @TableField("evidence_url")
    private String evidenceUrl;         // 证据文件URL
    
    @TableField("order_id")
    private Long orderId;               // 关联订单ID
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;            // 创建时间
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;            // 更新时间
}
```

#### 供应商信息实体
```java
@TableName("supplier_info")
public class SupplierInfo {
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;                    // 主键ID
    
    @TableField("company_name")
    private String companyName;         // 企业名称
    
    @TableField("fraction")
    private Integer fraction;           // 总评分
    
    @TableField("rating_type")
    private String ratingType;          // 评分类型
    
    @TableField("quotation_response_rate")
    private Double quotationResponseRate; // 报价响应率
    
    // ...其他字段
}
```

### ⚙️ 核心服务实现

#### 评分服务接口
```java
public interface CarVehicleBiddingScoreInfoService extends IService<CarVehicleBiddingScoreInfo> {
    
    /**
     * 分页查询评分记录
     */
    IPage<CarVehicleBiddingScoreInfo> entityPage(CarVehicleBiddingScoreInfoQuery query);
    
    /**
     * 保存评分记录
     */
    boolean saveEntity(CarVehicleBiddingScoreInfo param);
    
    /**
     * 更新评分记录
     */
    boolean updateEntity(CarVehicleBiddingScoreInfo param);
}
```

#### 供应商总分更新逻辑
```java
/**
 * 更新供应商总分
 * @param supplierId  供应商ID
 * @param scoreChange 分数变化量
 */
private void updateSupplierTotalScore(Long supplierId, int scoreChange) {
    SupplierInfo supplierInfo = supplierInfoService.getById(supplierId);
    if (supplierInfo != null) {
        // 获取当前分数，如果为空则默认为100分
        Integer currentScore = supplierInfo.getFraction();
        if (currentScore == null) {
            currentScore = 100;
        }
        
        // 计算新分数，确保在0-100范围内
        int newScore = Math.min(100, Math.max(0, currentScore + scoreChange));
        
        // 更新供应商分数和等级
        supplierInfo.setFraction(newScore);
        supplierInfo.setRatingType(calculateRatingType(newScore));
        supplierInfoService.updateById(supplierInfo);
        
        log.info("📊 更新供应商[ID={}]评分: {} -> {}", supplierId, currentScore, newScore);
    }
}
```

---

## 5. 数据库设计

### 📋 维修竞价评分表

```sql
CREATE TABLE `car_vehicle_bidding_score_info` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `supplier_id` bigint DEFAULT NULL COMMENT '供应商ID',
  `score` int DEFAULT NULL COMMENT '评分（可正可负）',
  `describe_info` text COMMENT '违规说明',
  `operator_user_id` bigint DEFAULT NULL COMMENT '操作人ID',
  `violation_type` varchar(255) DEFAULT NULL COMMENT '违规类型',
  `evidence_url` varchar(255) DEFAULT NULL COMMENT '证据文件URL',
  `order_id` bigint DEFAULT NULL COMMENT '关联订单ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_supplier_id` (`supplier_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='维修竞价报价—评分表';
```

### 📋 供应商信息表（评分相关字段）

```sql
-- 供应商信息表中的评分相关字段
ALTER TABLE `supplier_info` ADD COLUMN `fraction` int DEFAULT 100 COMMENT '总评分';
ALTER TABLE `supplier_info` ADD COLUMN `rating_type` varchar(50) DEFAULT NULL COMMENT '评分类型';
ALTER TABLE `supplier_info` ADD COLUMN `quotation_response_rate` decimal(5,2) DEFAULT NULL COMMENT '报价响应率';

-- 添加索引
CREATE INDEX `idx_fraction` ON `supplier_info` (`fraction`);
CREATE INDEX `idx_rating_type` ON `supplier_info` (`rating_type`);
```

### 📊 数据字典

#### 违规类型枚举
| 值 | 含义 | 扣分 |
|----|------|------|
| `响应成功` | 按时提交报价 | +5分 |
| `响应超时` | 超时未报价 | -10分 |
| `恶意报价` | 恶意报价行为 | -15分 |

#### 评分类型枚举
| 值 | 含义 | 分数区间 |
|----|------|----------|
| `优质供应商` | 高质量供应商 | 80-100分 |
| `观察期供应商` | 中等质量供应商 | 60-80分 |
| `高风险供应商` | 低质量供应商 | 0-60分 |

---

## 6. API接口

### 🔧 维修评分相关接口

#### 6.1 获取评分分页数据
```http
POST /app/carVehicleBiddingScoreInfo/page
Content-Type: application/json

{
  "page": 1,
  "size": 10,
  "supplierId": 123456
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "total": 50,
    "records": [
      {
        "id": 1001,
        "supplierId": 123456,
        "score": -10,
        "violationType": "响应超时",
        "describeInfo": "维修厂超过截止时间未提交报价，系统自动扣10分",
        "orderId": 789012,
        "createTime": "2025-06-13 10:30:00"
      }
    ]
  }
}
```

#### 6.2 手动扣分接口
```http
POST /app/carVehicleBiddingScoreInfo/add
Content-Type: application/json

{
  "supplierId": 123456,
  "score": -15,
  "violationType": "恶意报价",
  "describeInfo": "供应商故意报虚高价格，影响正常竞价秩序",
  "evidenceUrl": "https://example.com/evidence.pdf",
  "orderId": 789012
}
```

### 🏢 供应商评分相关接口

#### 6.3 获取供应商评分详情
```http
POST /app/supplierInfo/getRatingDetails
Content-Type: application/json

{
  "id": 123456
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "supplierId": 123456,
    "companyName": "XX汽车维修厂",
    "score": 75,
    "ratingType": "观察期供应商",
    "supplierLogoUrl": "https://example.com/logo.png",
    "monthlyScores": [
      {
        "month": "2024-07",
        "score": 85
      },
      {
        "month": "2024-08",
        "score": 80
      }
    ],
    "carVehicleBiddingScoreInfoVOs": [
      {
        "id": 1001,
        "score": -10,
        "violationType": "响应超时",
        "createTime": "2025-06-13 10:30:00"
      }
    ]
  }
}
```

#### 6.4 获取供应商评分分布
```http
POST /app/supplierInfo/getRatingDistribution
Content-Type: application/json

{}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "highRiskCount": 5,
    "warningCount": 15,
    "highQualityCount": 30,
    "timeoutPercentage": 0.15,
    "maliciousPercentage": 0.05
  }
}
```

#### 6.5 获取供应商评分分页
```http
POST /app/supplierInfo/getRatingPage
Content-Type: application/json

{
  "page": 1,
  "size": 10,
  "ratingType": "优质供应商"
}
```

---

## 7. 业务流程

### 🔄 自动评分流程图

```mermaid
graph TD
    A[竞价订单发布] --> B[供应商收到通知]
    B --> C{供应商响应}
    C -->|按时报价| D[系统检测按时提交]
    C -->|超时未报价| E[系统检测响应超时]
    
    D --> F[自动加5分]
    E --> G[自动扣10分]
    
    F --> H[更新供应商总分]
    G --> H
    
    H --> I[重新计算供应商等级]
    I --> J[记录评分日志]
    J --> K[发送通知给供应商]
    
    style D fill:#90EE90
    style E fill:#FFB6C1
    style F fill:#90EE90
    style G fill:#FFB6C1
```

### 👨‍💼 人工评分流程图

```mermaid
graph TD
    A[管理员发现违规行为] --> B[进入供应商评分页面]
    B --> C[点击扣分按钮]
    C --> D[选择违规类型]
    D --> E[系统自动设置扣分值]
    E --> F[填写违规说明]
    F --> G[上传证据文件]
    G --> H[提交扣分申请]
    H --> I[系统记录评分变更]
    I --> J[更新供应商总分]
    J --> K[重新计算供应商等级]
    K --> L[发送通知给供应商]
    
    style A fill:#FFA500
    style H fill:#FF6347
    style J fill:#32CD32
```

### 📊 月度评分计算流程

```mermaid
graph TD
    A[定时任务触发] --> B[获取过去12个月数据]
    B --> C[按月份分组评分记录]
    C --> D[计算每月累计分数变化]
    D --> E[生成月度评分趋势]
    E --> F[更新缓存数据]
    F --> G[前端图表展示]
    
    style A fill:#87CEEB
    style E fill:#98FB98
    style G fill:#DDA0DD
```

---

## 8. 统计分析

### 📈 月度评分趋势分析

#### 计算逻辑
```java
/**
 * 计算供应商月度评分趋势数据
 * @param scoreInfoList 评分记录列表
 * @return 月度评分趋势数据
 */
private List<MonthlyScoreDto> calculateMonthlyScores(List<CarVehicleBiddingScoreInfoVO> scoreInfoList) {
    // 如果没有评分记录，返回近12个月的默认100分
    if (CollUtil.isEmpty(scoreInfoList)) {
        return generateDefaultMonthlyScores();
    }
    
    // 按月分组评分记录
    Map<String, List<CarVehicleBiddingScoreInfoVO>> scoresByMonth = new HashMap<>();
    for (CarVehicleBiddingScoreInfoVO scoreInfo : scoreInfoList) {
        Date createTime = scoreInfo.getCreateTime();
        if (createTime != null) {
            String monthKey = new SimpleDateFormat("yyyy-MM").format(createTime);
            scoresByMonth.computeIfAbsent(monthKey, k -> new ArrayList<>()).add(scoreInfo);
        }
    }
    
    // 计算每月最后一次评分后的分数
    Map<String, Integer> lastScoreByMonth = new HashMap<>();
    int currentScore = 100; // 默认初始分数为100
    
    // 按时间顺序处理评分记录
    List<String> sortedMonths = new ArrayList<>(scoresByMonth.keySet());
    Collections.sort(sortedMonths);
    
    for (String month : sortedMonths) {
        List<CarVehicleBiddingScoreInfoVO> monthScores = scoresByMonth.get(month);
        // 按创建时间排序
        monthScores.sort(Comparator.comparing(CarVehicleBiddingScoreInfoVO::getCreateTime));
        
        // 计算月末分数
        for (CarVehicleBiddingScoreInfoVO scoreInfo : monthScores) {
            if (scoreInfo.getScore() != null) {
                currentScore += scoreInfo.getScore(); // 累加评分（可能为正或负）
            }
        }
        
        // 确保分数在0-100之间
        currentScore = Math.min(100, Math.max(0, currentScore));
        lastScoreByMonth.put(month, currentScore);
    }
    
    // 生成过去12个月的评分趋势数据
    List<MonthlyScoreDto> result = new ArrayList<>();
    LocalDate now = LocalDate.now();
    
    // 获取过去12个月的月份列表
    List<String> last12Months = new ArrayList<>();
    for (int i = 11; i >= 0; i--) {
        LocalDate date = now.minusMonths(i);
        last12Months.add(date.format(DateTimeFormatter.ofPattern("yyyy-MM")));
    }
    
    // 填充月度评分数据
    int lastScore = 100; // 初始默认分数
    for (String month : last12Months) {
        if (lastScoreByMonth.containsKey(month)) {
            lastScore = lastScoreByMonth.get(month);
        } else if (sortedMonths.size() > 0 && month.compareTo(sortedMonths.get(0)) < 0) {
            // 如果月份早于第一条记录，使用默认分数100
            lastScore = 100;
        }
        // 否则使用上个月的分数
        
        MonthlyScoreDto monthlyScore = new MonthlyScoreDto();
        monthlyScore.setMonth(month);
        monthlyScore.setScore(lastScore);
        result.add(monthlyScore);
    }
    
    return result;
}
```

#### 前端图表展示
```tsx
// ECharts配置 - 月度评分趋势图
const getOption = () => {
  const monthlyScores = getRowsDataInfo?.data?.monthlyScores || [];
  
  return {
    title: {
      text: '月度评分趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        return `${params[0].name}<br/>评分：${params[0].value}分`;
      }
    },
    xAxis: {
      type: 'category',
      data: monthlyScores.map(item => item.month)
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      name: '评分'
    },
    series: [{
      name: '评分',
      type: 'line',
      data: monthlyScores.map(item => item.score),
      smooth: true,
      lineStyle: {
        color: '#1890ff'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(24, 144, 255, 0.6)' },
            { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
          ]
        }
      }
    }]
  };
};
```

### 📊 供应商评分分布统计

#### 统计指标
```java
/**
 * 获取供应商评分分布统计
 */
@Override
public SupplierRatingDistributionDto getSupplierRatingDistribution() {
    // 获取所有已认证的维修供应商
    List<SupplierInfo> supplierInfos = this.list(new LambdaQueryWrapper<>(SupplierInfo.class)
            .eq(SupplierInfo::getStatusInfo, SupplierQualificationStatusEnum.AUTHENTICATED.getCode())
            .eq(SupplierInfo::getSupplierType, RoleBusEnum.REPAIR.getRoleName()));
    
    // 初始化结果对象
    SupplierRatingDistributionDto result = new SupplierRatingDistributionDto();
    result.setHighRiskCount(0);
    result.setWarningCount(0);
    result.setHighQualityCount(0);
    
    // 统计各等级供应商数量
    for (SupplierInfo supplierInfo : supplierInfos) {
        Integer fraction = supplierInfo.getFraction();
        
        if (fraction == null) {
            continue; // 跳过未评分的供应商
        }
        
        // 根据分数分类统计
        if (fraction < 60) {
            result.setHighRiskCount(result.getHighRiskCount() + 1);
        } else if (fraction >= 60 && fraction <= 80) {
            result.setWarningCount(result.getWarningCount() + 1);
        } else {
            result.setHighQualityCount(result.getHighQualityCount() + 1);
        }
    }
    
    // 计算违规行为占比
    List<CarVehicleBiddingScoreInfoVO> carVehicleBiddingScoreInfos = getCarVehicleBiddingScoreInfos();
    
    if (CollUtil.isNotEmpty(carVehicleBiddingScoreInfos)) {
        // 计算响应超时占比
        long timeoutCount = carVehicleBiddingScoreInfos.stream()
                .filter(scoreInfo -> Objects.equals(scoreInfo.getViolationType(), "响应超时"))
                .count();
        double timeoutPercentage = (double) timeoutCount / carVehicleBiddingScoreInfos.size();
        result.setTimeoutPercentage(timeoutPercentage);
        
        // 计算恶意报价占比
        long maliciousCount = carVehicleBiddingScoreInfos.stream()
                .filter(scoreInfo -> Objects.equals(scoreInfo.getViolationType(), "恶意报价"))
                .count();
        double maliciousPercentage = (double) maliciousCount / carVehicleBiddingScoreInfos.size();
        result.setMaliciousPercentage(maliciousPercentage);
    }
    
    return result;
}
```

### 📈 关键业务指标

| 指标名称 | 计算方式 | 业务意义 |
|---------|---------|----------|
| **响应及时率** | 按时报价次数 / 总报价次数 | 供应商响应效率 |
| **违规率** | 违规次数 / 总参与次数 | 供应商服务质量 |
| **平均评分** | 所有供应商评分总和 / 供应商数量 | 整体服务水平 |
| **优质供应商占比** | 优质供应商数量 / 总供应商数量 | 供应商质量结构 |

---

## 9. 运营管理

### 🎯 日常运营流程

#### 9.1 定时任务管理
```java
/**
 * 每日凌晨2点执行评分统计任务
 */
@Scheduled(cron = "0 0 2 * * ?")
public void dailyScoreStatistics() {
    log.info("🕐 开始执行每日评分统计任务");
    
    try {
        // 1. 处理超时未报价的扣分
        handleTimeoutQuotes(new Date());
        
        // 2. 处理按时报价的加分
        handleTimelyQuotes(new Date());
        
        // 3. 更新供应商等级
        updateSupplierRatingTypes();
        
        // 4. 生成统计报表
        generateDailyReport();
        
        log.info("✅ 每日评分统计任务执行完成");
    } catch (Exception e) {
        log.error("❌ 每日评分统计任务执行失败", e);
    }
}
```

#### 9.2 供应商等级维护
```java
/**
 * 批量更新供应商等级
 */
private void updateSupplierRatingTypes() {
    List<SupplierInfo> suppliers = supplierInfoService.list(
        new LambdaQueryWrapper<>(SupplierInfo.class)
            .eq(SupplierInfo::getSupplierType, RoleBusEnum.REPAIR.getRoleName())
            .isNotNull(SupplierInfo::getFraction)
    );
    
    for (SupplierInfo supplier : suppliers) {
        String newRatingType = calculateRatingType(supplier.getFraction());
        if (!Objects.equals(supplier.getRatingType(), newRatingType)) {
            supplier.setRatingType(newRatingType);
            supplierInfoService.updateById(supplier);
            
            log.info("📊 供应商[{}]等级更新: {} -> {}", 
                supplier.getCompanyName(), supplier.getRatingType(), newRatingType);
        }
    }
}
```

### 🔍 质量监控

#### 预警机制
```java
/**
 * 供应商质量预警检查
 */
@Scheduled(cron = "0 0 9 * * ?") // 每日9点执行
public void supplierQualityAlert() {
    // 检查高风险供应商
    List<SupplierInfo> highRiskSuppliers = supplierInfoService.list(
        new LambdaQueryWrapper<>(SupplierInfo.class)
            .eq(SupplierInfo::getRatingType, "高风险供应商")
    );
    
    if (CollUtil.isNotEmpty(highRiskSuppliers)) {
        // 发送预警通知
        String alertMessage = String.format("⚠️ 发现%d个高风险供应商，请及时处理！", 
            highRiskSuppliers.size());
        sendAlert(alertMessage, highRiskSuppliers);
    }
    
    // 检查评分急剧下降的供应商
    checkScoreDropAlert();
}
```

### 📊 数据分析报表

#### 月度评分报表
```sql
-- 供应商月度评分统计报表
SELECT 
    si.company_name AS '供应商名称',
    si.fraction AS '当前评分',
    si.rating_type AS '供应商等级',
    COUNT(cvbsi.id) AS '本月评分次数',
    SUM(CASE WHEN cvbsi.score > 0 THEN cvbsi.score ELSE 0 END) AS '本月加分',
    SUM(CASE WHEN cvbsi.score < 0 THEN ABS(cvbsi.score) ELSE 0 END) AS '本月扣分',
    AVG(cvbsi.score) AS '平均分数变化'
FROM supplier_info si
LEFT JOIN car_vehicle_bidding_score_info cvbsi ON si.id = cvbsi.supplier_id
    AND DATE_FORMAT(cvbsi.create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
WHERE si.supplier_type = '维修'
    AND si.status_info = 3 -- 已认证状态
GROUP BY si.id, si.company_name, si.fraction, si.rating_type
ORDER BY si.fraction DESC;
```

### 🎯 持续优化建议

#### 评分规则优化
1. **动态权重调整**：根据业务重要性调整不同行为的评分权重
2. **分级评分**：针对不同等级供应商采用差异化评分标准
3. **季度重置**：定期重置评分，给供应商改进机会

#### 技术优化
1. **实时计算**：使用Redis缓存提升评分计算性能
2. **异步处理**：评分计算采用消息队列异步处理
3. **数据分析**：引入机器学习优化评分模型

#### 运营优化
1. **培训机制**：为低分供应商提供专项培训
2. **激励措施**：为高分供应商提供更多合作机会
3. **反馈机制**：建立供应商评分申诉和反馈渠道

---

## 📚 附录

### A. 常见问题FAQ

**Q1: 供应商评分的初始值是多少？**
A1: 新注册并通过认证的供应商初始评分为100分。

**Q2: 评分记录可以删除或修改吗？**
A2: 为保证数据的完整性和可追溯性，评分记录一般不允许删除，但支持管理员添加备注说明。

**Q3: 供应商可以查看自己的评分记录吗？**
A3: 是的，供应商可以在个人中心查看自己的评分记录和趋势分析。

### B. 版本更新记录

| 版本 | 日期 | 更新内容 | 作者 |
|------|------|----------|------|
| v1.0 | 2025-06-13 | 初始版本，包含基础评分功能 | liusheng |

### C. 相关文档链接

- [供应商管理系统总体设计](./供应商管理系统设计文档.md)
- [竞价报价系统设计](./竞价报价系统设计文档.md)
- [系统API接口文档](./API接口文档.md)

---

**📞 技术支持**：如有疑问，请联系开发团队
**📧 邮箱**：<EMAIL>
**🕐 最后更新**：2025年6月13日
