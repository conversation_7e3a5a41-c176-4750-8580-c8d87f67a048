package com.westcatr.rd.car.business.demand.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.core.domain.IUser;
import com.westcatr.rd.boot.core.exception.IRuntimeException;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.entity.JoinInfo;
import com.westcatr.rd.boot.orm.service.BaseDao;
import com.westcatr.rd.boot.orm.service.JoinInfoService;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.boot.sso.util.AuthUtil;
import com.westcatr.rd.car.business.car.entity.CarInfo;
import com.westcatr.rd.car.business.car.entity.CarModelInfo;
import com.westcatr.rd.car.business.car.service.CarInfoService;
import com.westcatr.rd.car.business.car.service.CarModelInfoService;
import com.westcatr.rd.car.business.demand.entity.DemandApplicationPhoneInfo;
import com.westcatr.rd.car.business.demand.entity.DemandInfo;
import com.westcatr.rd.car.business.demand.mapper.DemandInfoMapper;
import com.westcatr.rd.car.business.demand.pojo.dto.GetPhoneNumberReturnDto;
import com.westcatr.rd.car.business.demand.pojo.dto.GetUserPhoneAuditDto;
import com.westcatr.rd.car.business.demand.pojo.query.DemandApplicationPhoneInfoQuery;
import com.westcatr.rd.car.business.demand.pojo.query.DemandInfoQuery;
import com.westcatr.rd.car.business.demand.pojo.vo.DemandApplicationPhoneInfoVO;
import com.westcatr.rd.car.business.demand.pojo.vo.DemandInfoVO;
import com.westcatr.rd.car.business.demand.service.DemandApplicationPhoneInfoService;
import com.westcatr.rd.car.business.demand.service.DemandInfoService;
import com.westcatr.rd.car.business.supplier.entity.SupplierInfo;
import com.westcatr.rd.car.business.supplier.service.SupplierInfoService;
import com.westcatr.rd.car.enums.DemandstatusEnum;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <p>
 * 需求—基本信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Service
public class DemandInfoServiceImpl extends ServiceImpl<DemandInfoMapper, DemandInfo> implements DemandInfoService {
    @Autowired
    private BaseDao baseDao;

    @Autowired
    private JoinInfoService joinInfoService;

    @Autowired
    private CarInfoService carInfoService;

    @Autowired
    private CarModelInfoService carModelInfoService;

    @Autowired
    private DemandApplicationPhoneInfoService demandApplicationPhoneInfoService;

    @Autowired
    private SupplierInfoService supplierInfoService;

    @Override
    public IPage<DemandInfo> entityPage(DemandInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<DemandInfo>().create(query));
    }

    @Override
    public DemandInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(DemandInfo param) {
        Long userId = AuthUtil.getUserIdE();
        CarInfo carInfo = carInfoService.getById(param.getCarId());
        List<CarInfo> carInfos = new ArrayList<>();
        if (carInfo != null) {
            carInfos = carInfoService
                    .list(new LambdaQueryWrapper<>(CarInfo.class).eq(CarInfo::getCarBasicId, carInfo.getCarBasicId()));
            if (CollUtil.isEmpty(carInfos)) {
                carInfos = new ArrayList<>();
                carInfos.add(carInfo);
            }
        }
        carInfos.forEach(x -> {
            LambdaQueryWrapper<CarModelInfo> carModelInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            if (param.getCarModelId() != null) {
                carModelInfoLambdaQueryWrapper.eq(CarModelInfo::getId, param.getCarModelId());
                carModelInfoLambdaQueryWrapper.eq(CarModelInfo::getCarId, x.getId());
            } else {
                carModelInfoLambdaQueryWrapper.eq(CarModelInfo::getCarId, x.getId());
            }
            List<CarModelInfo> carModelInfos = carModelInfoService.list(carModelInfoLambdaQueryWrapper);
            if (CollUtil.isNotEmpty(carModelInfos)) {
                List<Long> supplierIds = carModelInfos.stream().map(CarModelInfo::getSupplierId).distinct().toList();
                if (CollUtil.isNotEmpty(supplierIds)) {
                    supplierIds.forEach(x1 -> {
                        carModelInfos.forEach(y1 -> {
                            DemandInfo demandInfo = new DemandInfo();
                            demandInfo.setCarId(x.getId());
                            demandInfo.setSupplierId(x1);
                            demandInfo.setCarModelId(y1.getId());
                            demandInfo.setCreateUserId(userId);
                            demandInfo.setRequirementType(param.getRequirementType());
                            demandInfo.setDemandTitle(param.getDemandTitle());
                            demandInfo.setDescriptionInfo(param.getDescriptionInfo());
                            demandInfo.setContactPhone(param.getContactPhone());
                            demandInfo.setContactPersonnel(param.getContactPersonnel());
                            demandInfo.setUseCarModel(param.getUseCarModel());
                            demandInfo.setStatusInfo(DemandstatusEnum.TO_BE_CONFIRMED.getCode());
                            this.save(demandInfo);
                        });

                    });
                }
            }
        });
        return true;
    }

    @Override
    public boolean updateEntity(DemandInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }

    @Override
    public GetPhoneNumberReturnDto mobilePhoneNumberTheUserNeed(Long id) {
        GetPhoneNumberReturnDto returnDto = new GetPhoneNumberReturnDto();
        DemandInfo demandInfo = this.getById(id);
        if (demandInfo.getStatusInfo().equals(DemandstatusEnum.HAS_AGREED.getCode())) {
            String sql = "select contact_phone from demand_info where id=" + id;
            Map<String, Object> map = baseDao.selectOneBySql(sql, null);
            if (ObjectUtil.isNotNull(map)) {
                returnDto.setResultStatus(true);
                returnDto.setInfo(MapUtil.getStr(map, "contact_phone"));
                return returnDto;
            }
        }
        DemandApplicationPhoneInfo demandApplicationPhoneInfo = demandApplicationPhoneInfoService
                .getOne(new LambdaQueryWrapper<>(DemandApplicationPhoneInfo.class)
                        .eq(DemandApplicationPhoneInfo::getDemandId, id)
                        .eq(DemandApplicationPhoneInfo::getSupplierId, demandInfo.getSupplierId()).last("limit 1"));
        if (ObjectUtil.isNull(demandApplicationPhoneInfo)) {
            demandApplicationPhoneInfo = new DemandApplicationPhoneInfo();
            demandApplicationPhoneInfo.setDemandId(id);
            demandApplicationPhoneInfo.setSupplierId(demandInfo.getSupplierId());
            SupplierInfo supplierInfo = supplierInfoService.getById(demandInfo.getSupplierId());
            if (supplierInfo != null) {
                demandApplicationPhoneInfo.setTitleInfo("供应商【" + supplierInfo.getCompanyName() + "】需要获取您的联系方式");
            } else {
                demandApplicationPhoneInfo.setTitleInfo("您的购车需求【" + demandInfo.getDemandTitle() + "】，供应商需要获取您的联系方式");
            }

            demandApplicationPhoneInfoService.save(demandApplicationPhoneInfo);
        }
        returnDto.setResultStatus(false);
        returnDto.setInfo("已经发起获取联系方式的申请，等待用户同意！");
        return returnDto;
    }

    @Override
    public boolean ignoreNeeds(Long id) {
        IUser iUser = AuthUtil.getUserE();
        if (ObjectUtil.isNotNull(iUser.getExtendData().getLong("supplierId"))) {
            Long supplierId = iUser.getExtendData().getLong("supplierId");
            joinInfoService.add(1, supplierId, id);
        } else {
            throw new IRuntimeException("当前用户不属于供应商，无法进行次操作");
        }
        return true;
    }

    @Override
    public IPage<DemandInfoVO> myVoPage(DemandInfoQuery query) {
        if (query.getBeginTime() != null) {
            query.setBeginTime(DateUtil.beginOfDay(query.getBeginTime()));
        }
        if (query.getEndTime() != null) {
            query.setEndTime(DateUtil.endOfDay(query.getEndTime()));
        }
        AssociationQuery<DemandInfoVO> associationQuery = new AssociationQuery<>(DemandInfoVO.class);
        IUser iUser = AuthUtil.getUserE();
        QueryWrapper<DemandInfoVO> wrapper = new WrapperFactory<DemandInfoVO>().create(query);
        // 检查是否为供应商查询场景，并且当前用户确实是供应商
        boolean isSupplierQuery = !"user".equals(query.getQuerySource()); // 默认为供应商查询或兼容旧调用
        if (isSupplierQuery && ObjectUtil.isNotNull(iUser.getExtendData().getLong("supplierId"))) {
            Long supplierId = iUser.getExtendData().getLong("supplierId");
            List<JoinInfo> joinInfos = joinInfoService.list(new LambdaQueryWrapper<>(JoinInfo.class)
                    .eq(JoinInfo::getId1, supplierId).eq(JoinInfo::getJoinCode, 1)); // 查询忽略记录
            // 仅在供应商查询时过滤已忽略的需求
            wrapper.notIn(CollUtil.isNotEmpty(joinInfos), "id", joinInfos.stream().map(JoinInfo::getId2).toList());
            wrapper.eq("supplier_id", supplierId); // 供应商只能看到分配给自己的

            // 清除原有的排序条件，避免出现两个 ORDER BY 子句
            wrapper.getExpression().getOrderBy().clear(); // 清除排序条件
            // 设置新的排序条件 (供应商视图的排序)
            wrapper.last(
                    "ORDER BY CASE status_info WHEN 0 THEN 0 WHEN 2 THEN 1 WHEN 1 THEN 2 ELSE 3 END, update_time desc");
        } else {
             // 用户查询自己发布的需求时，应用用户ID过滤 (如果前端未传 createUserId，则从 AuthUtil 获取)
             if (query.getCreateUserId() == null) {
                 query.setCreateUserId(AuthUtil.getUserIdE());
                 wrapper.eq("create_user_id", query.getCreateUserId());
             }
             // 用户视图可以按默认排序或前端指定的排序
        }
        IPage<DemandInfoVO> iPage = associationQuery.voPage(query, wrapper);
        if (CollUtil.isNotEmpty(iPage.getRecords())) {
            iPage.getRecords().forEach(x -> {
                if (!x.getStatusInfo().equals(DemandstatusEnum.TO_BE_CONFIRMED.getCode())
                        || CollUtil.isNotEmpty(x.getDemandApplicationPhoneInfos())) {
                    x.setTfShowGetPhone(false);
                }

            });
        }
        return iPage;
    }

    @Transactional
    @Override
    public boolean getUserPhoneNumber(GetUserPhoneAuditDto getUserPhoneAuditDto) {
        DemandApplicationPhoneInfo demandApplicationPhoneInfo = demandApplicationPhoneInfoService
                .getById(getUserPhoneAuditDto.getId());
        demandApplicationPhoneInfo.setRemarks(getUserPhoneAuditDto.getRemarks());
        if (demandApplicationPhoneInfoService.updateById(demandApplicationPhoneInfo)) {
            DemandInfo demandInfo = this.getById(demandApplicationPhoneInfo.getDemandId());
            if (ObjectUtil.isNotNull(demandInfo)) {
                if (getUserPhoneAuditDto.isResult()) {
                    demandInfo.setStatusInfo(DemandstatusEnum.HAS_AGREED.getCode());
                } else {
                    demandInfo.setStatusInfo(DemandstatusEnum.HAS_REJECTED.getCode());
                }
                return this.updateById(demandInfo);
            } else {
                throw new IRuntimeException("未找到对应的申请记录，请联系管理员或者请忽略这条信息！");
            }
        }
        return true;
    }

    @Override
    public IPage<DemandApplicationPhoneInfoVO> getPhoneVoPage(DemandApplicationPhoneInfoQuery query) {
        query.setTimeSort(null);
        IUser iUser = AuthUtil.getUserE();
        if (ObjectUtil.isNotNull(iUser.getExtendData().getLong("supplierId"))) {
            Long supplierId = iUser.getExtendData().getLong("supplierId");
            query.setSupplierId(supplierId);
        } else {
            query.setCreateUserId(iUser.getId());
        }
        AssociationQuery<DemandApplicationPhoneInfoVO> associationQuery = new AssociationQuery<>(
                DemandApplicationPhoneInfoVO.class);
        QueryWrapper<DemandApplicationPhoneInfoVO> wrapper = new WrapperFactory<DemandApplicationPhoneInfoVO>()
                .create(query);
        if (StrUtil.isNotEmpty(query.getKeyword())) {
            wrapper.like("demand_application_phone_info.title_info", query.getKeyword()).or().like("die.demand_title",
                    query.getKeyword());
        }

        // 清除原有的排序条件，避免出现两个 ORDER BY 子句
        wrapper.getExpression().getOrderBy().clear(); // 清除排序条件
        // 设置新的排序条件
        wrapper.last(
                "ORDER BY CASE die.status_info WHEN 0 THEN 0 WHEN 2 THEN 1 WHEN 1 THEN 2 ELSE 3 END, demand_application_phone_info.update_time desc");

        IPage<DemandApplicationPhoneInfoVO> iPage = associationQuery.voPage(query, wrapper);
        if (CollUtil.isNotEmpty(iPage.getRecords())) {
            iPage.getRecords().forEach(x -> {
                x.setDemandInfo(new AssociationQuery<>(DemandInfoVO.class).getVo(x.getDemandId()));
            });
        }
        return iPage;
    }

    @Override
    public IPage<DemandInfoVO> myPublishedRequirements(DemandInfoQuery query) {
        List<DemandInfoVO> result = new ArrayList<>();
        query.setCreateUserId(AuthUtil.getUserIdE());
        List<JoinInfo> joinInfos = joinInfoService.list(
                new LambdaQueryWrapper<JoinInfo>()
                        .eq(JoinInfo::getJoinCode, 1));
        QueryWrapper<DemandInfoVO> queryWrapper = new WrapperFactory<DemandInfoVO>().create(query);
        if (CollUtil.isNotEmpty(joinInfos)) {
            queryWrapper.notIn("id", joinInfos.stream().map(JoinInfo::getId2).distinct().toList());
        }
        List<DemandInfoVO> vos = new AssociationQuery<>(DemandInfoVO.class).voList(query, queryWrapper);
        if (CollUtil.isNotEmpty(vos)) {
            Map<String, List<DemandInfoVO>> resultMap = this.groupByFieldsAndSortByCreateTime(vos);
            if (!resultMap.isEmpty()) {
                resultMap.forEach((k, v) -> {
                    DemandInfoVO demandInfoVO = v.get(0);
                    List<SupplierInfo> supplierInfos = v.stream()
                            .map(vo -> {
                                SupplierInfo supplierInfo = vo.getSupplierInfo();
                                if (supplierInfo != null) {
                                    SupplierInfo newSupplierInfo = new SupplierInfo();
                                    BeanUtils.copyProperties(supplierInfo, newSupplierInfo);
                                    newSupplierInfo.setHandleTime(vo.getUpdateTime());
                                    newSupplierInfo.setDemandStatus(vo.getStatusInfo());
                                    return newSupplierInfo;
                                }
                                return null;
                            })
                            .filter(ObjectUtil::isNotNull)
                            .toList();
                    if (CollUtil.isNotEmpty(supplierInfos)) {
                        demandInfoVO.setSupplierInfosByCrateUser(supplierInfos);
                    }
                    result.add(demandInfoVO);
                });
            }
        }
        IPage<DemandInfoVO> page = new Page<>(query.getPage(), query.getSize());
        if (query.getSize() != null && query.getPage() != null) {
            int startIndex = (query.getPage() - 1) * query.getSize();
            int endIndex = Math.min(startIndex + query.getSize(), result.size());

            if (startIndex < result.size()) {
                page.setRecords(result.subList(startIndex, endIndex));
            } else {
                page.setRecords(new ArrayList<>());
            }
            page.setTotal(result.size());
        } else {
            page.setRecords(result);
            page.setTotal(result.size());
        }
        return page;
    }

    private Map<String, List<DemandInfoVO>> groupByFieldsAndSortByCreateTime(List<DemandInfoVO> demandList) {
        // 先进行分组
        Map<String, List<DemandInfoVO>> groupedMap = demandList.stream()
                .collect(Collectors.groupingBy(
                        item -> item.getDemandTitle() + "_"
                                + item.getDescriptionInfo() + "_"
                                + item.getCarId()));

        // 创建按创建时间倒序排序的TreeMap
        TreeMap<String, List<DemandInfoVO>> sortedMap = new TreeMap<>((k1, k2) -> {
            Date time1 = groupedMap.get(k1).get(0).getCreateTime();
            Date time2 = groupedMap.get(k2).get(0).getCreateTime();
            return time2.compareTo(time1); // 倒序排序
        });

        // 将分组结果放入排序的map中
        sortedMap.putAll(groupedMap);

        return sortedMap;
    }
}
