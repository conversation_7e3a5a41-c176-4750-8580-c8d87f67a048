package com.westcatr.rd.car.business.car.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.car.business.car.pojo.query.CarFlowParamDataInfoQuery;
import com.westcatr.rd.car.business.car.entity.CarFlowParamDataInfo;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * <p>
 * 新增修改车辆参数流程信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface CarFlowParamDataInfoService extends IService<CarFlowParamDataInfo> {

    IPage<CarFlowParamDataInfo> entityPage(CarFlowParamDataInfoQuery query);

    CarFlowParamDataInfo getEntityById(Long id);

    boolean saveEntity(CarFlowParamDataInfo param);

    boolean updateEntity(CarFlowParamDataInfo param);

    boolean removeEntityById(Long id);
}
