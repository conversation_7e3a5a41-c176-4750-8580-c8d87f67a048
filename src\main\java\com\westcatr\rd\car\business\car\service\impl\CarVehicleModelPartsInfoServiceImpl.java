package com.westcatr.rd.car.business.car.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.core.exception.IRuntimeException;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.boot.sso.util.AuthUtil;
import com.westcatr.rd.car.business.car.entity.CarVehicleModelInfo;
import com.westcatr.rd.car.business.car.entity.CarVehicleModelPartsInfo;
import com.westcatr.rd.car.business.car.entity.CarVehicleOperationStatisticsInfo;
import com.westcatr.rd.car.business.car.listener.CarVehicleModelPartsInfoListener;
import com.westcatr.rd.car.business.car.mapper.CarVehicleModelPartsInfoMapper;
import com.westcatr.rd.car.business.car.pojo.dto.CarVehicleModelPartsExcelDto;
import com.westcatr.rd.car.business.car.pojo.query.CarVehicleModelPartsInfoQuery;
import com.westcatr.rd.car.business.car.pojo.vo.CarVehicleModelPartsInfoVO;
import com.westcatr.rd.car.business.car.service.CarVehicleModelInfoService;
import com.westcatr.rd.car.business.car.service.CarVehicleModelPartsInfoService;
import com.westcatr.rd.car.business.car.service.CarVehicleOperationStatisticsInfoService;
import com.westcatr.rd.car.business.repair.entity.CarVehicleModelLaborHourInfo;
import com.westcatr.rd.car.business.repair.service.CarVehicleModelLaborHourInfoService;
import com.westcatr.rd.car.common.ExcelResponseHelper;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 车型配件信息表--配件 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Service
@Slf4j
public class CarVehicleModelPartsInfoServiceImpl
        extends ServiceImpl<CarVehicleModelPartsInfoMapper, CarVehicleModelPartsInfo>
        implements CarVehicleModelPartsInfoService {

    @Autowired
    private CarVehicleModelInfoService carVehicleModelInfoService;

    @Autowired
    private CarVehicleOperationStatisticsInfoService carVehicleOperationStatisticsInfoService;

    @Autowired
    private CarVehicleModelLaborHourInfoService carVehicleModelLaborHourInfoService;

    @Override
    public IPage<CarVehicleModelPartsInfo> entityPage(CarVehicleModelPartsInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<CarVehicleModelPartsInfo>().create(query));
    }

    @Override
    public CarVehicleModelPartsInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(CarVehicleModelPartsInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(CarVehicleModelPartsInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }

    @Override
    public void exportTemplate() {
        try {
            EasyExcel
                    .write(ExcelResponseHelper.getExcelResponse("车型配件信息表--配件模板").getOutputStream(),
                            CarVehicleModelPartsExcelDto.class)
                    .sheet("模板")
                    .doWrite(new ArrayList<>());
        } catch (IOException e) {
            throw new IRuntimeException("导出模板失败");
        }
    }

    @Override
    public boolean importExcel(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IRuntimeException("文件为空");
        }
        CarVehicleModelPartsInfoListener listener = new CarVehicleModelPartsInfoListener(
                new CarVehicleModelPartsInfoListener.BatchInsertCallback() {
                    @Override
                    public void doSaveBatch(List<CarVehicleModelPartsInfo> dataList) {
                        saveBatch(dataList);
                    }

                    @Override
                    public List<CarVehicleModelPartsInfo> getExistingParts() {
                        LambdaQueryWrapper<CarVehicleModelPartsInfo> wrapper = new LambdaQueryWrapper<>();
                        wrapper.select(CarVehicleModelPartsInfo::getModelCode, CarVehicleModelPartsInfo::getPartCode);
                        return list(wrapper);
                    }
                },
                carVehicleModelInfoService);
        EasyExcel.read(file.getInputStream(), CarVehicleModelPartsExcelDto.class, listener).sheet().doRead();
        return true;
    }

    @Override
    public void exportExcel(CarVehicleModelPartsInfoQuery query) {
        if (query.getVehicleModelId() != null) {
            CarVehicleModelInfo carVehicleModelInfo = carVehicleModelInfoService.getById(query.getVehicleModelId());
            if (carVehicleModelInfo != null) {
                CarVehicleOperationStatisticsInfo carVehicleOperationStatisticsInfo = new CarVehicleOperationStatisticsInfo();
                carVehicleOperationStatisticsInfo.setOperationType("配件导出");
                carVehicleOperationStatisticsInfo.setInvolvedVehicleModel(carVehicleModelInfo.getModelName());
                carVehicleOperationStatisticsInfo.setOperatorFullName(AuthUtil.getUserE().getFullName());
                carVehicleOperationStatisticsInfoService.save(carVehicleOperationStatisticsInfo);
            }
        }
        try {
            IPage<CarVehicleModelPartsInfoVO> page = new AssociationQuery<>(CarVehicleModelPartsInfoVO.class)
                    .voPage(query);
            List<CarVehicleModelPartsExcelDto> excelDtos = new ArrayList<>();

            for (CarVehicleModelPartsInfoVO info : page.getRecords()) {
                CarVehicleModelPartsExcelDto dto = new CarVehicleModelPartsExcelDto();
                dto.setModelName(info.getModelName());
                dto.setModelCode(info.getModelCode());
                dto.setSubGroupCode(info.getSubGroupCode());
                dto.setSubGroupName(info.getSubGroupName());
                dto.setPartCode(info.getPartCode());
                dto.setPartName(info.getPartName());
                dto.setOriginalPrice(info.getOriginalPrice());
                dto.setDealerPrice(info.getDealerPrice());
                dto.setRemarks(info.getRemarks());
                dto.setTypeInfo(info.getTypeInfo()); // 添加类别字段
                excelDtos.add(dto);
            }

            EasyExcel
                    .write(ExcelResponseHelper.getExcelResponse("车型配件信息表--配件数据").getOutputStream(),
                            CarVehicleModelPartsExcelDto.class)
                    .sheet("数据")
                    .doWrite(excelDtos);
        } catch (IOException e) {
            throw new IRuntimeException("导出数据失败");
        }

    }

    @Override
    public IPage<CarVehicleModelPartsInfoVO> myVoPage(CarVehicleModelPartsInfoQuery query) {
        AssociationQuery<CarVehicleModelPartsInfoVO> associationQuery = new AssociationQuery<>(
                CarVehicleModelPartsInfoVO.class);
        String operatorFullName = AuthUtil.getUserE().getFullName();
        if (query.getVehicleModelId() != null && query.getPartCode() == null && query.getPartName() == null
                && query.getSubGroupName() == null && query.getSubGroupCode() == null) {
            CarVehicleModelInfo carVehicleModelInfo = carVehicleModelInfoService.getById(query.getVehicleModelId());
            if (carVehicleModelInfo != null) {
                CarVehicleOperationStatisticsInfo carVehicleOperationStatisticsInfo = new CarVehicleOperationStatisticsInfo();
                carVehicleOperationStatisticsInfo.setOperationType("配件库查看");
                carVehicleOperationStatisticsInfo.setInvolvedVehicleModel(carVehicleModelInfo.getModelName());
                carVehicleOperationStatisticsInfo.setOperatorFullName(operatorFullName);
                carVehicleOperationStatisticsInfoService.save(carVehicleOperationStatisticsInfo);
            }
        }
        IPage<CarVehicleModelPartsInfoVO> page = associationQuery.voPage(query);
        if (CollUtil.isNotEmpty(page.getRecords())) {

            page.getRecords().forEach(x -> {
                x.setBiddingHoursCount(
                        carVehicleModelLaborHourInfoService.count(new LambdaQueryWrapper<CarVehicleModelLaborHourInfo>()
                                .eq(CarVehicleModelLaborHourInfo::getPartsId, x.getId())));
            });

            // 按照车型名称 modelName 分组，处理null值
            Map<String, List<CarVehicleModelPartsInfoVO>> groupByModelName = page.getRecords().stream()
                    .collect(Collectors.groupingBy(
                            vo -> vo.getModelName() != null ? vo.getModelName() : "未知车型", // 将null值替换为"未知车型"
                            Collectors.toList()));

            // 记录日志，如果有null值的记录
            long nullModelNameCount = page.getRecords().stream()
                    .filter(vo -> vo.getModelName() == null)
                    .count();
            if (nullModelNameCount > 0) {
                log.warn("发现{}条记录的modelName为null，这些记录已被分组到'未知车型'组", nullModelNameCount);
            }
            List<CarVehicleOperationStatisticsInfo> carVehicleOperationStatisticsInfoList = new ArrayList<>();
            for (Map.Entry<String, List<CarVehicleModelPartsInfoVO>> entry : groupByModelName.entrySet()) {
                if (query.getPartCode() != null) {
                    CarVehicleOperationStatisticsInfo statisticsInfo = new CarVehicleOperationStatisticsInfo();
                    statisticsInfo.setOperatorFullName(operatorFullName);
                    statisticsInfo.setOperationType("配件查询");
                    statisticsInfo.setInvolvedVehicleModel(entry.getKey());
                    statisticsInfo.setOperationItem("配件编码");
                    carVehicleOperationStatisticsInfoList.add(statisticsInfo);
                }
                if (query.getPartName() != null) {
                    CarVehicleOperationStatisticsInfo statisticsInfo = new CarVehicleOperationStatisticsInfo();
                    statisticsInfo.setOperatorFullName(operatorFullName);
                    statisticsInfo.setOperationType("配件查询");
                    statisticsInfo.setInvolvedVehicleModel(entry.getKey());
                    statisticsInfo.setOperationItem("配件名称");
                    carVehicleOperationStatisticsInfoList.add(statisticsInfo);
                }
                if (query.getSubGroupName() != null) {
                    CarVehicleOperationStatisticsInfo statisticsInfo = new CarVehicleOperationStatisticsInfo();
                    statisticsInfo.setOperatorFullName(operatorFullName);
                    statisticsInfo.setOperationType("配件查询");
                    statisticsInfo.setInvolvedVehicleModel(entry.getKey());
                    statisticsInfo.setOperationItem("子组名称");
                    carVehicleOperationStatisticsInfoList.add(statisticsInfo);
                }
                if (query.getSubGroupCode() != null) {
                    CarVehicleOperationStatisticsInfo statisticsInfo = new CarVehicleOperationStatisticsInfo();
                    statisticsInfo.setOperatorFullName(operatorFullName);
                    statisticsInfo.setOperationType("配件查询");
                    statisticsInfo.setInvolvedVehicleModel(entry.getKey());
                    statisticsInfo.setOperationItem("子组编码");
                    carVehicleOperationStatisticsInfoList.add(statisticsInfo);
                }
            }
            carVehicleOperationStatisticsInfoService.saveBatch(carVehicleOperationStatisticsInfoList);
        }
        return page;
    }

    /**
     * 批量获取车型的配件数量
     *
     * @param modelIds 车型ID列表
     * @return Map<车型ID, 配件数量>
     */
    @Override
    public Map<Long, Long> getPartsCountByModelIds(List<Long> modelIds) {
        if (CollUtil.isEmpty(modelIds)) {
            return new HashMap<>();
        }

        // 使用 group by 一次性查询所有车型的配件数量
        QueryWrapper<CarVehicleModelPartsInfo> wrapper = new QueryWrapper<>();
        wrapper.select("vehicle_model_id, count(1) as count")
                .in("vehicle_model_id", modelIds)
                .groupBy("vehicle_model_id");

        List<Map<String, Object>> countList = this.baseMapper.selectMaps(wrapper);

        // 转换结果为Map
        return countList.stream().collect(Collectors.toMap(
                map -> Long.valueOf(map.get("vehicle_model_id").toString()),
                map -> Long.valueOf(map.get("count").toString()),
                (k1, k2) -> k1));
    }

    /**
     * 批量获取车型的竞价配件数量
     *
     * @param modelIds 车型ID列表
     * @return Map<车型ID, 竞价配件数量>
     */
    @Override
    public Map<Long, Long> getBiddingPartsCountByModelIds(List<Long> modelIds) {
        if (CollUtil.isEmpty(modelIds)) {
            return new HashMap<>();
        }

        // 使用 group by 一次性查询所有车型的竞价配件数量
        QueryWrapper<CarVehicleModelPartsInfo> wrapper = new QueryWrapper<>();
        wrapper.select("vehicle_model_id, count(1) as count")
                .in("vehicle_model_id", modelIds)
                .eq("type_info", "历史竞价") // 只查询竞价类型的配件
                .groupBy("vehicle_model_id");

        List<Map<String, Object>> countList = this.baseMapper.selectMaps(wrapper);

        // 转换结果为Map
        return countList.stream().collect(Collectors.toMap(
                map -> Long.valueOf(map.get("vehicle_model_id").toString()),
                map -> Long.valueOf(map.get("count").toString()),
                (k1, k2) -> k1));
    }
}
