package com.westcatr.rd.car.business.car.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.car.business.car.entity.CarVehicleOperationStatisticsInfo;
import com.westcatr.rd.car.business.car.pojo.dto.CarVehicleOperationStatisticsDto;
import com.westcatr.rd.car.business.car.pojo.query.CarVehicleOperationStatisticsInfoQuery;
import com.westcatr.rd.car.business.car.pojo.vo.CarVehicleOperationStatisticsInfoVO;
import com.westcatr.rd.car.business.car.service.CarVehicleOperationStatisticsInfoService;
import com.westcatr.rd.car.common.annotation.AuditLog;
import com.westcatr.rd.car.enums.OperationTypeEnum;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * CarVehicleOperationStatisticsInfo 控制器
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Validated
@Tag(name = "车型配件表—操作统计接口", description = "车型配件表—操作统计接口", extensions = {
        @Extension(properties = { @ExtensionProperty(name = "x-order", value = "100") }) })
@Slf4j
@RestController
public class CarVehicleOperationStatisticsInfoController {

    @Autowired
    private CarVehicleOperationStatisticsInfoService carVehicleOperationStatisticsInfoService;

    @Operation(summary = "获取车型配件表—操作统计分页数据")
    @PostMapping("/carVehicleOperationStatisticsInfo/page")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车型配件表操作统计分页数据")
    public IResult<IPage<CarVehicleOperationStatisticsInfo>> getCarVehicleOperationStatisticsInfoPage(
            @RequestBody CarVehicleOperationStatisticsInfoQuery query) {
        return IResult.ok(carVehicleOperationStatisticsInfoService.entityPage(query));
    }

    @Operation(summary = "获取车型配件表—操作统计数据")
    @PostMapping("/carVehicleOperationStatisticsInfo/get")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车型配件表操作统计数据: ID={#id.id}")
    public IResult<CarVehicleOperationStatisticsInfo> getCarVehicleOperationStatisticsInfoById(@RequestBody @Id ID id) {
        return IResult.ok(carVehicleOperationStatisticsInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增车型配件表—操作统计数据")
    @PostMapping("/carVehicleOperationStatisticsInfo/add")
    @AuditLog(operationType = OperationTypeEnum.ADD, description = "新增车型配件表操作统计数据: ID={#param.id}")
    public IResult addCarVehicleOperationStatisticsInfo(
            @RequestBody @Validated(Insert.class) CarVehicleOperationStatisticsInfo param) {
        return IResult.auto(carVehicleOperationStatisticsInfoService.saveEntity(param));
    }

    @Operation(summary = "更新车型配件表—操作统计数据")
    @PostMapping("/carVehicleOperationStatisticsInfo/update")
    @AuditLog(operationType = OperationTypeEnum.UPDATE, description = "更新车型配件表操作统计数据: ID={#param.id}")
    public IResult updateCarVehicleOperationStatisticsInfoById(
            @RequestBody @Validated(Update.class) CarVehicleOperationStatisticsInfo param) {
        return IResult.auto(carVehicleOperationStatisticsInfoService.updateEntity(param));
    }

    @Operation(summary = "删除车型配件表—操作统计数据")
    @PostMapping("/carVehicleOperationStatisticsInfo/delete")
    @AuditLog(operationType = OperationTypeEnum.DELETE, description = "删除车型配件表操作统计数据: ID={#id.id}")
    public IResult deleteCarVehicleOperationStatisticsInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            carVehicleOperationStatisticsInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取车型配件表—操作统计VO分页数据")
    @PostMapping("/carVehicleOperationStatisticsInfo/voPage")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车型配件表操作统计VO分页数据")
    public IResult<IPage<CarVehicleOperationStatisticsInfoVO>> getCarVehicleOperationStatisticsInfoVoPage(
            @RequestBody CarVehicleOperationStatisticsInfoQuery query) {
        AssociationQuery<CarVehicleOperationStatisticsInfoVO> associationQuery = new AssociationQuery<>(
                CarVehicleOperationStatisticsInfoVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取车型配件表—操作统计VO数据")
    @PostMapping("/carVehicleOperationStatisticsInfo/getVo")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车型配件表操作统计VO数据: ID={#id.id}")
    public IResult<CarVehicleOperationStatisticsInfoVO> getCarVehicleOperationStatisticsInfoVoById(
            @RequestBody @Id ID id) {
        AssociationQuery<CarVehicleOperationStatisticsInfoVO> associationQuery = new AssociationQuery<>(
                CarVehicleOperationStatisticsInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    @Operation(summary = "获取车型配件表—操作统计统计数据")
    @PostMapping("/carVehicleOperationStatisticsInfo/statistics")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车型配件表操作统计统计数据")
    public IResult<CarVehicleOperationStatisticsDto> getCarVehicleOperationStatisticsDto(
            @RequestBody CarVehicleOperationStatisticsInfoQuery query) {
        return IResult.ok(carVehicleOperationStatisticsInfoService.getCarVehicleOperationStatisticsDto(query));
    }

    @Operation(summary = "导出车型配件表—操作统计数据")
    @PostMapping("/carVehicleOperationStatisticsInfo/export")
    @AuditLog(operationType = OperationTypeEnum.EXPORT, description = "导出车型配件表操作统计数据")
    public void export(@RequestBody CarVehicleOperationStatisticsInfoQuery query) {
        carVehicleOperationStatisticsInfoService.exportExcel(query);
    }
}
