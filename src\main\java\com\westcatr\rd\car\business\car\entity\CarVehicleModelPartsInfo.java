package com.westcatr.rd.car.business.car.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.web.excel.annotation.ExcelField;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 车型配件信息表--配件
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("car_vehicle_model_parts_info")
@Schema(description = "车型配件信息表--配件")
public class CarVehicleModelPartsInfo extends Model<CarVehicleModelPartsInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "车型名称")
    @TableField(exist = false)
    private String modelName;

    @Schema(description = "车型id")
    @TableField("vehicle_model_id")
    private Long vehicleModelId;

    @Schema(description = "车型编码")
    @ExcelField(name = "车型编码")
    @Length(max = 255, message = "车型编码长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("model_code")
    private String modelCode;

    @Schema(description = "子组编码")
    @ExcelField(name = "子组编码")
    @Length(max = 255, message = "子组编码长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("sub_group_code")
    private String subGroupCode;

    @Schema(description = "子组名称")
    @ExcelField(name = "子组名称")
    @Length(max = 255, message = "子组名称长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("sub_group_name")
    private String subGroupName;

    @Schema(description = "配件编码")
    @ExcelField(name = "配件编码")
    @Length(max = 255, message = "配件编码长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("part_code")
    private String partCode;

    @Schema(description = "配件名称")
    @ExcelField(name = "配件名称")
    @Length(max = 255, message = "配件名称长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("part_name")
    private String partName;

    @Schema(description = "备注")
    @ExcelField(name = "备注")
    @Length(max = 255, message = "备注长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("remarks")
    private String remarks;

    @Schema(description = "原厂价格")
    @ExcelField(name = "原厂价格")
    @TableField("original_price")
    private Double originalPrice;

    @Schema(description = "4S店价格")
    @ExcelField(name = "4S店价格")
    @TableField("dealer_price")
    private Double dealerPrice;

    @Schema(description = "类别")
    @TableField("type_info")
    private String typeInfo;

    @Schema(description = "供应商ID")
    @TableField("supplier_id")
    private Long supplierId;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    // 配件类别 accessories_category
    @Schema(description = "配件类别")
    @TableField("accessories_category")
    private String accessoriesCategory;

    @Schema(description = "不含税单价")
    @TableField("price_without_tax")
    private BigDecimal priceWithoutTax;

    @Schema(description = "单位")
    @TableField("unit")
    private String unit;

    @Schema(description = "含税单价")
    @TableField("price_with_tax")
    private BigDecimal priceWithTax;

    // tf_del
    @TableField("tf_del")
    @Schema(description = "删除标记")
    private Integer tfDel;

    // discount
    @TableField("discount")
    @Schema(description = "折扣")
    private Double discount;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
