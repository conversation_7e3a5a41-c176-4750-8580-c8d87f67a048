package com.westcatr.rd.car.business.car.task;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.westcatr.rd.car.business.car.entity.CarVehicleModelInfo;
import com.westcatr.rd.car.business.car.entity.CarVehicleModelPartsInfo;
import com.westcatr.rd.car.business.car.service.CarVehicleModelInfoService;
import com.westcatr.rd.car.business.car.service.CarVehicleModelPartsInfoService;

/**
 * Scheduled task for deduplicating car vehicle parts data
 *
 * <AUTHOR>
 */
@Component
public class CarVehiclePartsDeduplicationTask {

    private static final Logger log = LoggerFactory.getLogger(CarVehiclePartsDeduplicationTask.class);

    @Autowired
    private CarVehicleModelInfoService carVehicleModelInfoService;

    @Autowired
    private CarVehicleModelPartsInfoService carVehicleModelPartsInfoService;

    /**
     * Scheduled task to deduplicate car parts records
     * Runs at 1:30 AM every day
     */
    @Scheduled(cron = "0 30 1 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void deduplicateCarParts() {
        log.info("开始执行配件库去重任务");
        long startTime = System.currentTimeMillis();

        try {
            // 第一步：对车型信息进行去重（按照车型编码去重）
            deduplicateCarModels();

            // 第二步：对配件库数据进行去重（按照车型编码、子组编码、配件编码去重）
            deduplicatePartsByModelAndSubGroup();

            long endTime = System.currentTimeMillis();
            log.info("配件库去重任务执行完成，耗时: {} 毫秒", (endTime - startTime));
        } catch (Exception e) {
            log.error("配件库去重任务执行失败", e);
        }
    }

    /**
     * 对车型首页展示处按照车型编码去重
     */
    private void deduplicateCarModels() {
        log.info("开始对车型信息进行去重（车型编码）");

        // 查询所有车型信息
        List<CarVehicleModelInfo> allModels = carVehicleModelInfoService.list();

        // 按照车型编码分组
        Map<String, List<CarVehicleModelInfo>> modelCodeGroups = allModels.stream()
                .filter(model -> model.getModelCode() != null && !model.getModelCode().isEmpty())
                .collect(Collectors.groupingBy(CarVehicleModelInfo::getModelCode));

        int totalDuplicatesRemoved = 0;

        // 处理每个分组
        for (Map.Entry<String, List<CarVehicleModelInfo>> entry : modelCodeGroups.entrySet()) {
            String modelCode = entry.getKey();
            List<CarVehicleModelInfo> models = entry.getValue();

            // 如果存在重复的车型编码
            if (models.size() > 1) {
                log.info("发现车型编码 {} 存在 {} 条重复记录", modelCode, models.size() - 1);

                // 保留创建时间最新的一条记录，如果时间一致则保留ID大的那条
                models.sort((m1, m2) -> {
                    int timeCompare = m2.getCreateTime().compareTo(m1.getCreateTime());
                    return timeCompare != 0 ? timeCompare : m2.getId().compareTo(m1.getId());
                });

                CarVehicleModelInfo keepModel = models.get(0);
                List<Long> idsToRemove = new ArrayList<>();

                // 收集要删除的ID
                for (int i = 1; i < models.size(); i++) {
                    idsToRemove.add(models.get(i).getId());
                }

                // 删除重复记录
                if (!idsToRemove.isEmpty()) {
                    boolean removed = carVehicleModelInfoService.removeByIds(idsToRemove);
                    if (removed) {
                        totalDuplicatesRemoved += idsToRemove.size();
                        log.info("已删除车型编码 {} 的 {} 条重复记录，保留ID: {}", modelCode, idsToRemove.size(), keepModel.getId());
                    }
                }
            }
        }

        log.info("车型信息去重完成，共删除 {} 条重复记录", totalDuplicatesRemoved);
    }

    /**
     * 对配件库数据按照车型编码、子组编码、配件编码去重
     */
    private void deduplicatePartsByModelAndSubGroup() {
        log.info("开始对配件库数据进行去重（车型编码、子组编码、配件编码）");

        // 查询所有配件信息
        List<CarVehicleModelPartsInfo> allParts = carVehicleModelPartsInfoService.list();

        // 按照车型编码+子组编码+配件编码分组
        Map<String, List<CarVehicleModelPartsInfo>> partGroups = new HashMap<>();

        for (CarVehicleModelPartsInfo part : allParts) {
            if (part.getModelCode() == null || part.getSubGroupCode() == null || part.getPartCode() == null) {
                continue; // 跳过数据不完整的记录
            }

            String key = part.getModelCode() + "::" + part.getSubGroupCode() + "::" + part.getPartCode();
            if (!partGroups.containsKey(key)) {
                partGroups.put(key, new ArrayList<>());
            }
            partGroups.get(key).add(part);
        }

        int totalDuplicatesRemoved = 0;

        // 处理每个分组
        for (Map.Entry<String, List<CarVehicleModelPartsInfo>> entry : partGroups.entrySet()) {
            String key = entry.getKey();
            List<CarVehicleModelPartsInfo> parts = entry.getValue();

            // 如果存在重复的配件记录
            if (parts.size() > 1) {
                // 分析分组的键
                String[] keyParts = key.split("::");
                if (keyParts.length == 3) {
                    log.info("发现重复配件记录: 车型编码={}, 子组编码={}, 配件编码={}, 重复数量={}",
                            keyParts[0], keyParts[1], keyParts[2], parts.size() - 1);
                }

                // 保留创建时间最新的一条记录，如果时间一致则保留ID大的那条
                parts.sort((p1, p2) -> {
                    int timeCompare = p2.getCreateTime().compareTo(p1.getCreateTime());
                    return timeCompare != 0 ? timeCompare : p2.getId().compareTo(p1.getId());
                });

                CarVehicleModelPartsInfo keepPart = parts.get(0);
                List<Long> idsToRemove = new ArrayList<>();

                // 收集要删除的ID
                for (int i = 1; i < parts.size(); i++) {
                    idsToRemove.add(parts.get(i).getId());
                }

                // 删除重复记录
                if (!idsToRemove.isEmpty()) {
                    boolean removed = carVehicleModelPartsInfoService.removeByIds(idsToRemove);
                    if (removed) {
                        totalDuplicatesRemoved += idsToRemove.size();
                        if (keyParts.length == 3) {
                            log.info("已删除车型编码={}, 子组编码={}, 配件编码={} 的 {} 条重复记录，保留ID: {}",
                                    keyParts[0], keyParts[1], keyParts[2], idsToRemove.size(), keepPart.getId());
                        }
                    }
                }
            }
        }

        log.info("配件库数据去重完成，共删除 {} 条重复记录", totalDuplicatesRemoved);
    }
}