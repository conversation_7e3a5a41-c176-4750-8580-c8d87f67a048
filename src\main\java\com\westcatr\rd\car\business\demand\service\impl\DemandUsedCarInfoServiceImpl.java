package com.westcatr.rd.car.business.demand.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.core.domain.IUser;
import com.westcatr.rd.boot.core.exception.IRuntimeException;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.orm.entity.JoinInfo;
import com.westcatr.rd.boot.orm.service.BaseDao;
import com.westcatr.rd.boot.orm.service.JoinInfoService;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.boot.sso.util.AuthUtil;
import com.westcatr.rd.car.business.demand.entity.DemandUsedCarApplicationPhoneInfo;
import com.westcatr.rd.car.business.demand.entity.DemandUsedCarInfo;
import com.westcatr.rd.car.business.demand.mapper.DemandUsedCarInfoMapper;
import com.westcatr.rd.car.business.demand.pojo.dto.GetPhoneNumberReturnDto;
import com.westcatr.rd.car.business.demand.pojo.dto.GetUserPhoneAuditDto;
import com.westcatr.rd.car.business.demand.pojo.query.DemandUsedCarApplicationPhoneInfoQuery;
import com.westcatr.rd.car.business.demand.pojo.query.DemandUsedCarInfoQuery;
import com.westcatr.rd.car.business.demand.pojo.vo.DemandUsedCarApplicationPhoneInfoVO;
import com.westcatr.rd.car.business.demand.pojo.vo.DemandUsedCarInfoVO;
import com.westcatr.rd.car.business.demand.service.DemandUsedCarApplicationPhoneInfoService;
import com.westcatr.rd.car.business.demand.service.DemandUsedCarInfoService;
import com.westcatr.rd.car.business.supplier.entity.SupplierInfo;
import com.westcatr.rd.car.business.supplier.service.SupplierInfoService;
import com.westcatr.rd.car.enums.DemandstatusEnum;
import com.westcatr.rd.car.enums.RoleBusEnum;
import com.westcatr.rd.car.enums.SupplierQualificationStatusEnum;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.log4j.Log4j2;

/**
 * <p>
 * 二手车需求信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Log4j2
@Service
public class DemandUsedCarInfoServiceImpl extends ServiceImpl<DemandUsedCarInfoMapper, DemandUsedCarInfo>
        implements DemandUsedCarInfoService {

    @Autowired
    private BaseDao baseDao;

    @Autowired
    private JoinInfoService joinInfoService;

    @Autowired
    private DemandUsedCarApplicationPhoneInfoService applicationPhoneInfoService;

    @Autowired
    private SupplierInfoService supplierInfoService;

    @Override
    public IPage<DemandUsedCarInfo> entityPage(DemandUsedCarInfoQuery query) {
        return this.page(query.page(), new WrapperFactory<DemandUsedCarInfo>().create(query));
    }

    @Override
    public DemandUsedCarInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(DemandUsedCarInfo param) {

        // 获取所有有效的二手车供应商
        List<SupplierInfo> usedCarSuppliers = supplierInfoService.list(
                new LambdaQueryWrapper<SupplierInfo>()
                        .eq(SupplierInfo::getSupplierType, RoleBusEnum.USED_CAR_SUPPLIERS.getRoleName())
                        .eq(SupplierInfo::getStatusInfo, SupplierQualificationStatusEnum.AUTHENTICATED.getCode()));
        if (CollUtil.isNotEmpty(usedCarSuppliers)) {
            usedCarSuppliers.forEach(x -> {
                // 先保存二手车信息
                DemandUsedCarInfo demandUsedCarInfo = new DemandUsedCarInfo();
                BeanUtils.copyProperties(param, demandUsedCarInfo);
                demandUsedCarInfo.setCreateUserId(AuthUtil.getUserIdE());
                demandUsedCarInfo.setSupplierId(x.getId());
                this.save(demandUsedCarInfo);
            });
        } else {
            throw new IRuntimeException("当前系统没有有效的二手车供应商，无法进行此操作");
        }
        return true;
    }

    @Override
    public boolean updateEntity(DemandUsedCarInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }

    @Override
    public GetPhoneNumberReturnDto mobilePhoneNumberTheUserNeed(Long id) {
        GetPhoneNumberReturnDto returnDto = new GetPhoneNumberReturnDto();
        DemandUsedCarInfo demandInfo = this.getById(id);
        if (demandInfo == null) {
            returnDto.setResultStatus(false);
            returnDto.setInfo("需求不存在");
            return returnDto;
        }

        // 检查是否已同意状态
        if (demandInfo.getStatusInfo().equals(DemandstatusEnum.HAS_AGREED.getCode())) {
            String sql = "select contact_phone from demand_used_car_info where id=" + id;
            Map<String, Object> map = baseDao.selectOneBySql(sql, null);
            if (ObjectUtil.isNotNull(map)) {
                returnDto.setResultStatus(true);
                returnDto.setInfo(MapUtil.getStr(map, "contact_phone"));
                return returnDto;
            }
        }

        // 创建申请记录
        DemandUsedCarApplicationPhoneInfo applicationPhoneInfo = applicationPhoneInfoService.getOne(
                new LambdaQueryWrapper<DemandUsedCarApplicationPhoneInfo>()
                        .eq(DemandUsedCarApplicationPhoneInfo::getDemandId, id)
                        .eq(DemandUsedCarApplicationPhoneInfo::getSupplierId, demandInfo.getSupplierId())
                        .last("limit 1"));

        if (ObjectUtil.isNull(applicationPhoneInfo)) {
            applicationPhoneInfo = new DemandUsedCarApplicationPhoneInfo();
            applicationPhoneInfo.setDemandId(id);
            applicationPhoneInfo.setSupplierId(demandInfo.getSupplierId());

            SupplierInfo supplierInfo = supplierInfoService.getById(demandInfo.getSupplierId());
            if (supplierInfo != null) {
                applicationPhoneInfo.setTitleInfo("供应商【" + supplierInfo.getCompanyName() + "】需要获取您的联系方式");
            } else {
                applicationPhoneInfo.setTitleInfo("您的二手车需求【" + demandInfo.getDemandTitle() + "】，供应商需要获取您的联系方式");
            }

            applicationPhoneInfoService.save(applicationPhoneInfo);
        }

        returnDto.setResultStatus(false);
        returnDto.setInfo("已经发起获取联系方式的申请，等待用户同意！");
        return returnDto;
    }

    @Override
    public boolean ignoreNeeds(Long id) {
        IUser iUser = AuthUtil.getUserE();
        if (ObjectUtil.isNotNull(iUser.getExtendData().getLong("supplierId"))) {
            Long supplierId = iUser.getExtendData().getLong("supplierId");
            joinInfoService.add(5, supplierId, id);
        } else {
            throw new IRuntimeException("当前用户不属于供应商，无法进行此操作");
        }
        return true;
    }

    @Transactional
    @Override
    public boolean getUserPhoneNumber(GetUserPhoneAuditDto dto) {
        if (dto == null || dto.getId() == null) {
            return false;
        }

        DemandUsedCarApplicationPhoneInfo applicationPhoneInfo = applicationPhoneInfoService.getById(dto.getId());
        applicationPhoneInfo.setRemarks(dto.getRemarks());

        if (applicationPhoneInfoService.updateById(applicationPhoneInfo)) {
            DemandUsedCarInfo demandInfo = this.getById(applicationPhoneInfo.getDemandId());
            if (ObjectUtil.isNotNull(demandInfo)) {
                demandInfo.setStatusInfo(dto.isResult() ? DemandstatusEnum.HAS_AGREED.getCode()
                        : DemandstatusEnum.HAS_REJECTED.getCode());
                return this.updateById(demandInfo);
            } else {
                throw new IRuntimeException("未找到对应的申请记录，请联系管理员或者请忽略这条信息！");
            }
        }
        return true;
    }

    @Override
    public IPage<DemandUsedCarApplicationPhoneInfoVO> getPhoneVoPage(DemandUsedCarApplicationPhoneInfoQuery query) {
        query.setTimeSort(null);
        IUser iUser = AuthUtil.getUserE();

        if (ObjectUtil.isNotNull(iUser.getExtendData().getLong("supplierId"))) {
            Long supplierId = iUser.getExtendData().getLong("supplierId");
            query.setSupplierId(supplierId);
        } else {
            query.setCreateUserId(iUser.getId());
        }

        AssociationQuery<DemandUsedCarApplicationPhoneInfoVO> associationQuery = new AssociationQuery<>(
                DemandUsedCarApplicationPhoneInfoVO.class);

        QueryWrapper<DemandUsedCarApplicationPhoneInfoVO> wrapper = new WrapperFactory<DemandUsedCarApplicationPhoneInfoVO>()
                .create(query);

        if (StrUtil.isNotEmpty(query.getKeyword())) {
            wrapper.like("title_info", query.getKeyword())
                    .or()
                    .like("duci.demand_title", query.getKeyword());
        }

        // 清除原有的排序条件，避免出现两个 ORDER BY 子句
        wrapper.getExpression().getOrderBy().clear();
        // 使用标准的 SQL CASE 语句替换 FIELD 函数
        wrapper.last(
                "ORDER BY CASE duci.status_info WHEN 0 THEN 0 WHEN 2 THEN 1 WHEN 1 THEN 2 ELSE 3 END, update_time desc");

        IPage<DemandUsedCarApplicationPhoneInfoVO> iPage = associationQuery.voPage(query, wrapper);
        if (CollUtil.isNotEmpty(iPage.getRecords())) {
            iPage.getRecords().forEach(x -> {
                x.setDemandInfo(new AssociationQuery<>(DemandUsedCarInfoVO.class).getVo(x.getDemandId()));
            });
        }
        return iPage;
    }

    @Override
    public IPage<DemandUsedCarInfoVO> myVoPage(DemandUsedCarInfoQuery query) {
        if (query.getBeginTime() != null) {
            query.setBeginTime(DateUtil.beginOfDay(query.getBeginTime()));
        }
        if (query.getEndTime() != null) {
            query.setEndTime(DateUtil.endOfDay(query.getEndTime()));
        }

        AssociationQuery<DemandUsedCarInfoVO> associationQuery = new AssociationQuery<>(DemandUsedCarInfoVO.class);
        IUser iUser = AuthUtil.getUserE();
        QueryWrapper<DemandUsedCarInfoVO> wrapper = new WrapperFactory<DemandUsedCarInfoVO>().create(query);

        if (ObjectUtil.isNotNull(iUser.getExtendData().getLong("supplierId"))) {
            Long supplierId = iUser.getExtendData().getLong("supplierId");
            List<JoinInfo> joinInfos = joinInfoService.list(
                    new LambdaQueryWrapper<JoinInfo>()
                            .eq(JoinInfo::getId1, supplierId)
                            .eq(JoinInfo::getJoinCode, 5));

            wrapper.notIn(CollUtil.isNotEmpty(joinInfos), "id",
                    joinInfos.stream().map(JoinInfo::getId2).toList());
            wrapper.eq("supplier_id", supplierId);

            // 清除原有的排序条件，避免出现两个 ORDER BY 子句
            wrapper.getExpression().getOrderBy().clear();
            // 使用标准的 SQL CASE 语句替换 FIELD 函数
            wrapper.last(
                    "ORDER BY CASE status_info WHEN 0 THEN 0 WHEN 2 THEN 1 WHEN 1 THEN 2 ELSE 3 END, update_time desc");
        }

        IPage<DemandUsedCarInfoVO> iPage = associationQuery.voPage(query, wrapper);

        if (CollUtil.isNotEmpty(iPage.getRecords())) {
            iPage.getRecords().forEach(x -> {
                if (!x.getStatusInfo().equals(DemandstatusEnum.TO_BE_CONFIRMED.getCode())
                        || CollUtil.isNotEmpty(x.getDemandApplicationPhoneInfos())) {
                    x.setTfShowGetPhone(false);
                }

            });
        }
        return iPage;
    }

    @Override
    public IPage<DemandUsedCarInfoVO> myPublishedRequirements(DemandUsedCarInfoQuery query) {
        List<DemandUsedCarInfoVO> result = new ArrayList<>();
        query.setCreateUserId(AuthUtil.getUserIdE());
        List<JoinInfo> joinInfos = joinInfoService.list(
                new LambdaQueryWrapper<JoinInfo>()
                        .eq(JoinInfo::getJoinCode, 5));
        QueryWrapper<DemandUsedCarInfoVO> queryWrapper = new WrapperFactory<DemandUsedCarInfoVO>().create(query);
        if (CollUtil.isNotEmpty(joinInfos)) {
            queryWrapper.notIn("id", joinInfos.stream().map(JoinInfo::getId2).distinct().toList());
        }
        List<DemandUsedCarInfoVO> vos = new AssociationQuery<>(DemandUsedCarInfoVO.class).voList(query, queryWrapper);
        if (CollUtil.isNotEmpty(vos)) {
            Map<String, List<DemandUsedCarInfoVO>> resultMap = this.groupByFieldsAndSortByCreateTime(vos);
            if (!resultMap.isEmpty()) {
                // 遍历
                resultMap.forEach((k, v) -> {
                    DemandUsedCarInfoVO demandInfoVO = v.get(0);
                    // 获取v里面所有SupplierInfo 返回为一个list<SupplierInfo>
                    List<SupplierInfo> supplierInfos = v.stream()
                            .map(vo -> {
                                SupplierInfo supplierInfo = vo.getSupplierInfo();
                                if (supplierInfo != null) {
                                    // 创建新的对象并设置属性
                                    SupplierInfo newSupplierInfo = new SupplierInfo();
                                    BeanUtils.copyProperties(supplierInfo, newSupplierInfo);
                                    newSupplierInfo.setHandleTime(vo.getUpdateTime());
                                    newSupplierInfo.setDemandStatus(vo.getStatusInfo());
                                    return newSupplierInfo;
                                }
                                return null;
                            })
                            .filter(ObjectUtil::isNotNull)
                            .toList();
                    if (CollUtil.isNotEmpty(supplierInfos)) {
                        demandInfoVO.setSupplierInfosByCrateUser(supplierInfos);
                    }
                    result.add(demandInfoVO);
                });
            }
        }
        // 实现分页
        IPage<DemandUsedCarInfoVO> page = new Page<>(query.getPage(), query.getSize());
        if (query.getSize() != null && query.getPage() != null) {
            int startIndex = (query.getPage() - 1) * query.getSize();
            int endIndex = Math.min(startIndex + query.getSize(), result.size());

            if (startIndex < result.size()) {
                page.setRecords(result.subList(startIndex, endIndex));
            } else {
                page.setRecords(new ArrayList<>());
            }
            page.setTotal(result.size());
        } else {
            page.setRecords(result);
            page.setTotal(result.size());
        }
        return page;
    }

    private Map<String, List<DemandUsedCarInfoVO>> groupByFieldsAndSortByCreateTime(
            List<DemandUsedCarInfoVO> demandList) {
        // 先进行分组
        Map<String, List<DemandUsedCarInfoVO>> groupedMap = demandList.stream()
                .collect(Collectors.groupingBy(
                        item -> item.getDemandTitle() + "_"
                                + item.getDescriptionInfo() + "_"
                                + item.getOldCarModel()));

        // 创建按创建时间倒序排序的TreeMap
        TreeMap<String, List<DemandUsedCarInfoVO>> sortedMap = new TreeMap<>((k1, k2) -> {
            Date time1 = groupedMap.get(k1).get(0).getCreateTime();
            Date time2 = groupedMap.get(k2).get(0).getCreateTime();
            return time2.compareTo(time1); // 倒序排序
        });

        // 将分组结果放入排序的map中
        sortedMap.putAll(groupedMap);

        return sortedMap;
    }
}
