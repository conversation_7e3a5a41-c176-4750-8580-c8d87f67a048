package com.westcatr.rd.car.business.car.listener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.westcatr.rd.car.business.car.entity.CarVehicleModelInfo;
import com.westcatr.rd.car.business.car.entity.CarVehicleModelPartsInfo;
import com.westcatr.rd.car.business.car.pojo.dto.CarVehicleModelPartsExcelDto;
import com.westcatr.rd.car.business.car.service.CarVehicleModelInfoService;

import lombok.extern.slf4j.Slf4j;

/**
 * 车型配件信息表--配件 Excel 导入监听器
 *
 * <AUTHOR>
 */
@Slf4j
public class CarVehicleModelPartsInfoListener extends AnalysisEventListener<CarVehicleModelPartsExcelDto> {

    /**
     * 每隔5000条存储数据库
     */
    private static final int BATCH_COUNT = 5000;

    /**
     * 按车型分组的配件列表
     */
    private final Map<String, List<CarVehicleModelPartsInfo>> modelPartsMap = new HashMap<>();

    private final Map<String, CarVehicleModelInfo> modelInfoMap = new HashMap<>();

    /**
     * 已存在的车型信息缓存 key:modelCode
     */
    private Map<String, CarVehicleModelInfo> existingModelMap;

    /**
     * 已存在的配件信息缓存 key:modelCode_partCode
     */
    private Set<String> existingPartsSet;

    /**
     * 批处理回调接口
     */
    private final BatchInsertCallback batchInsertCallback;

    private final CarVehicleModelInfoService carVehicleModelInfoService;

    public CarVehicleModelPartsInfoListener(BatchInsertCallback batchInsertCallback,
            CarVehicleModelInfoService carVehicleModelInfoService) {
        this.batchInsertCallback = batchInsertCallback;
        this.carVehicleModelInfoService = carVehicleModelInfoService;
        // 初始化时加载已存在的数据
        initExistingData();
    }

    /**
     * 初始化已存在的数据
     */
    private void initExistingData() {
        // 加载所有车型信息
        List<CarVehicleModelInfo> existingModels = carVehicleModelInfoService.list();
        existingModelMap = existingModels.stream()
                .collect(Collectors.toMap(CarVehicleModelInfo::getModelCode, model -> model, (k1, k2) -> k1));

        // 加载所有配件信息的唯一标识
        LambdaQueryWrapper<CarVehicleModelPartsInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(CarVehicleModelPartsInfo::getModelCode, CarVehicleModelPartsInfo::getPartCode);
        existingPartsSet = batchInsertCallback.getExistingParts().stream()
                .map(parts -> parts.getModelCode() + "_" + parts.getPartCode())
                .collect(Collectors.toSet());
    }

    @Override
    public void invoke(CarVehicleModelPartsExcelDto data, AnalysisContext context) {
        String modelCode = data.getModelCode();
        String partCode = data.getPartCode();

        // 检查配件是否已存在
        if (existingPartsSet.contains(modelCode + "_" + partCode)) {
            return;
        }

        // 处理车型信息
        if (!existingModelMap.containsKey(modelCode) && !modelInfoMap.containsKey(modelCode)) {
            CarVehicleModelInfo modelInfo = new CarVehicleModelInfo();
            modelInfo.setModelCode(modelCode);
            modelInfo.setModelName(data.getModelName());
            modelInfo.setVin(data.getVinCode());
            modelInfo.setVehicleClass(data.getVehicleClass());
            modelInfoMap.put(modelCode, modelInfo);
        }

        // 处理配件信息
        CarVehicleModelPartsInfo entity = new CarVehicleModelPartsInfo();
        entity.setModelCode(modelCode);
        entity.setSubGroupCode(data.getSubGroupCode());
        entity.setSubGroupName(data.getSubGroupName());
        entity.setPartCode(partCode);
        entity.setPartName(data.getPartName());
        entity.setOriginalPrice(data.getOriginalPrice());
        entity.setDealerPrice(data.getDealerPrice());
        entity.setRemarks(data.getRemarks());
        entity.setModelName(data.getModelName());
        entity.setTypeInfo(data.getTypeInfo());

        // 按车型分组存储配件
        modelPartsMap.computeIfAbsent(modelCode, k -> new ArrayList<>()).add(entity);

        // 如果某个车型的配件数量达到阈值，就保存该车型的所有配件
        if (modelPartsMap.get(modelCode).size() >= BATCH_COUNT) {
            saveModelData(modelCode);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 保存所有剩余的数据
        for (String modelCode : new ArrayList<>(modelPartsMap.keySet())) {
            saveModelData(modelCode);
        }
        log.info("所有数据解析完成！");
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) {
        log.error("解析Excel异常", exception);
        if (exception instanceof ExcelDataConvertException) {
            ExcelDataConvertException excelException = (ExcelDataConvertException) exception;
            String errorMessage = String.format("第%d行，第%d列解析失败，单元格内容：%s，原因：%s",
                    excelException.getRowIndex() + 1,
                    excelException.getColumnIndex() + 1,
                    excelException.getCellData().getStringValue(),
                    exception.getMessage());
            throw new IRuntimeException(errorMessage);
        }
        throw new IRuntimeException("Excel解析异常：" + exception.getMessage());
    }

    /**
     * 保存指定车型的数据
     */
    private void saveModelData(String modelCode) {
        List<CarVehicleModelPartsInfo> modelParts = modelPartsMap.get(modelCode);
        if (modelParts == null || modelParts.isEmpty()) {
            return;
        }

        // 如果是新车型，先保存车型信息
        if (modelInfoMap.containsKey(modelCode)) {
            CarVehicleModelInfo modelInfo = modelInfoMap.get(modelCode);
            carVehicleModelInfoService.save(modelInfo);
            existingModelMap.put(modelCode, modelInfo);
            modelInfoMap.remove(modelCode);
        }

        // 设置车型ID
        CarVehicleModelInfo modelInfo = existingModelMap.get(modelCode);
        if (modelInfo != null) {
            modelParts.forEach(parts -> parts.setVehicleModelId(modelInfo.getId()));
        }

        // 保存配件信息
        log.info("保存{}车型的{}条配件数据", modelCode, modelParts.size());
        batchInsertCallback.doSaveBatch(modelParts);

        // 更新配件缓存
        modelParts.forEach(parts -> existingPartsSet.add(parts.getModelCode() + "_" + parts.getPartCode()));

        // 清理已保存的数据
        modelPartsMap.remove(modelCode);
    }

    /**
     * 批量插入回调接口
     */
    public interface BatchInsertCallback {
        /**
         * 执行批量保存
         * 
         * @param dataList 数据列表
         */
        void doSaveBatch(List<CarVehicleModelPartsInfo> dataList);

        /**
         * 获取已存在的配件信息
         * 
         * @return 配件信息列表
         */
        List<CarVehicleModelPartsInfo> getExistingParts();
    }

    public static class IRuntimeException extends RuntimeException {
        public IRuntimeException(String message) {
            super(message);
        }
    }
}