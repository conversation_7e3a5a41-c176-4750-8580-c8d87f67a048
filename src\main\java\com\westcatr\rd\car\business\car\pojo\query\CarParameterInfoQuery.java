package com.westcatr.rd.car.business.car.pojo.query;

import com.westcatr.rd.boot.orm.dto.TimeDTO;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 车辆—参数信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "车辆—参数信息表查询对象")
public class CarParameterInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @QueryCondition
    private Long id;

    @Schema(description = "关联车辆ID")
    @QueryCondition
    private Long carId;

    @Schema(description = "参考价(元)")
    @QueryCondition
    private BigDecimal referencePrice;

    @Schema(description = "优惠信息")
    @QueryCondition
    private String discountInfo;

    @Schema(description = "厂商指导价(元)")
    @QueryCondition
    private BigDecimal manufacturerPrice;

    @Schema(description = "厂商")
    @QueryCondition
    private String manufacturer;

    @Schema(description = "级别")
    @QueryCondition
    private String vehicleLevel;

    @Schema(description = "能源类型")
    @QueryCondition
    private String energyType;

    @Schema(description = "环保标准")
    @QueryCondition
    private String emissionStandard;

    @Schema(description = "上市时间")
    @QueryCondition
    private Date releaseDate;

    @Schema(description = "WLTC纯电续航里程(km)")
    @QueryCondition
    private Integer wltcRange;

    @Schema(description = "CLTC纯电续航里程(km)")
    @QueryCondition
    private Integer cltcRange;

    @Schema(description = "电池快充时间(小时)")
    @QueryCondition
    private BigDecimal fastChargeTime;

    @Schema(description = "电池慢充时间(小时)")
    @QueryCondition
    private BigDecimal slowChargeTime;

    @Schema(description = "电池快充电量范围(%)")
    @QueryCondition
    private String fastChargeRange;

    @Schema(description = "最大功率(kW)")
    @QueryCondition
    private Integer maxPower;

    @Schema(description = "最大扭矩(N·m)")
    @QueryCondition
    private Integer maxTorque;

    @Schema(description = "变速箱")
    @QueryCondition
    private String transmission;

    @Schema(description = "车身结构")
    @QueryCondition
    private String bodyStructure;

    @Schema(description = "发动机")
    @QueryCondition
    private String engine;

    @Schema(description = "电动机(Ps)")
    @QueryCondition
    private Integer electricMotorPs;

    @Schema(description = "长*宽*高(mm)")
    @QueryCondition
    private String dimensions;

    @Schema(description = "官方0-100km/h加速(s)")
    @QueryCondition
    private BigDecimal acceleration;

    @Schema(description = "最高车速(km/h)")
    @QueryCondition
    private Integer maxSpeed;

    @Schema(description = "WLTC综合油耗(L/100km)")
    @QueryCondition
    private BigDecimal wltcFuelConsumption;

    @Schema(description = "最低荷电状态油耗(L/100km)")
    @QueryCondition
    private BigDecimal minChargeStateFuelConsumption;

    @Schema(description = "整车质保")
    @QueryCondition
    private String warranty;

    @Schema(description = "整备质量(kg)")
    @QueryCondition
    private Integer curbWeight;

    @Schema(description = "最大满载质量(kg)")
    @QueryCondition
    private Integer maxPayload;

    @Schema(description = "准拖挂车总质量(kg)")
    @QueryCondition
    private Integer maxTowingMass;

    @Schema(description = "长度(mm)")
    @QueryCondition
    private Integer length;

    @Schema(description = "宽度(mm)")
    @QueryCondition
    private Integer width;

    @Schema(description = "高度(mm)")
    @QueryCondition
    private Integer height;

    @Schema(description = "轴距(mm)")
    @QueryCondition
    private Integer wheelbase;

    @Schema(description = "前轮距(mm)")
    @QueryCondition
    private Integer frontTrack;

    @Schema(description = "后轮距(mm)")
    @QueryCondition
    private Integer rearTrack;

    @Schema(description = "满载最小离地间隙(mm)")
    @QueryCondition
    private BigDecimal groundClearance;

    @Schema(description = "接近角(°)")
    @QueryCondition
    private BigDecimal approachAngle;

    @Schema(description = "离去角(°)")
    @QueryCondition
    private BigDecimal departureAngle;

    @Schema(description = "纵向通过角(°)")
    @QueryCondition
    private BigDecimal rampBreakoverAngle;

    @Schema(description = "最大爬坡角度(°)")
    @QueryCondition
    private BigDecimal maxClimbAngle;

    @Schema(description = "最小转弯半径(m)")
    @QueryCondition
    private BigDecimal turningRadius;

    @Schema(description = "最大涉水深度(mm)")
    @QueryCondition
    private Integer maxWadingDepth;

    @Schema(description = "油箱容积(L)")
    @QueryCondition
    private BigDecimal fuelTankCapacity;

    @Schema(description = "前备厢容积(L)")
    @QueryCondition
    private BigDecimal frontTrunkVolume;

    @Schema(description = "后备厢容积(L)")
    @QueryCondition
    private BigDecimal rearTrunkVolume;

    @Schema(description = "风阻系数(Cd)")
    @QueryCondition
    private BigDecimal dragCoefficient;

    @Schema(description = "发动机型号")
    @QueryCondition
    private String engineModel;

    @Schema(description = "排量(mL)")
    @QueryCondition
    private Integer displacementMl;

    @Schema(description = "排量(L)")
    @QueryCondition
    private BigDecimal displacementL;

    @Schema(description = "进气形式")
    @QueryCondition
    private String intakeForm;

    @Schema(description = "发动机布局")
    @QueryCondition
    private String engineLayout;

    @Schema(description = "气缸排列形式")
    @QueryCondition
    private String cylinderArrangement;

    @Schema(description = "气缸数(个)")
    @QueryCondition
    private Integer cylinderCount;

    @Schema(description = "每缸气门数(个)")
    @QueryCondition
    private Integer valvePerCylinder;

    @Schema(description = "配气机构")
    @QueryCondition
    private String valveMechanism;

    @Schema(description = "最大马力(Ps)")
    @QueryCondition
    private Integer maxHorsepower;

    @Schema(description = "最大功率转速(rpm)")
    @QueryCondition
    private Integer maxRpm;

    @Schema(description = "最大扭矩转速(rpm)")
    @QueryCondition
    private Integer torqueRpm;

    @Schema(description = "燃油标号")
    @QueryCondition
    private String fuelLabel;

    @Schema(description = "供油方式")
    @QueryCondition
    private String fuelSupplyMode;

    @Schema(description = "电池能量(kWh)")
    @QueryCondition
    private BigDecimal batteryCapacity;

    @Schema(description = "电池能量密度(Wh/kg)")
    @QueryCondition
    private BigDecimal batteryDensity;

    @Schema(description = "创建时间")
    @QueryCondition
    private Date createTime;

    @Schema(description = "更新时间")
    @QueryCondition
    private Date updateTime;
}
