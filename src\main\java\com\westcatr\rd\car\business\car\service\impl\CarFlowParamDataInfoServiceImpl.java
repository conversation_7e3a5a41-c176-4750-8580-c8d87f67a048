package com.westcatr.rd.car.business.car.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.car.business.car.pojo.query.CarFlowParamDataInfoQuery;
import com.westcatr.rd.car.business.car.entity.CarFlowParamDataInfo;
import com.westcatr.rd.car.business.car.mapper.CarFlowParamDataInfoMapper;
import com.westcatr.rd.car.business.car.service.CarFlowParamDataInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 新增修改车辆参数流程信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Service
public class CarFlowParamDataInfoServiceImpl extends ServiceImpl<CarFlowParamDataInfoMapper, CarFlowParamDataInfo> implements CarFlowParamDataInfoService {

    @Override
    public IPage<CarFlowParamDataInfo> entityPage(CarFlowParamDataInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<CarFlowParamDataInfo>().create(query));
    }

    @Override
    public CarFlowParamDataInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(CarFlowParamDataInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(CarFlowParamDataInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }
}

