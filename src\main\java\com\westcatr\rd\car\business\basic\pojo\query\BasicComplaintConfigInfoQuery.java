package com.westcatr.rd.car.business.basic.pojo.query;

import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import com.westcatr.rd.boot.orm.dto.TimeDTO;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 基础信息--投诉配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="基础信息--投诉配置表查询对象")
public class BasicComplaintConfigInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "投诉邮箱")
    @QueryCondition
    private String complaintEmail;

    @Schema(description = "投诉电话")
    @QueryCondition
    private String complaintPhone;

    @Schema(description = "主键ID")
    @QueryCondition
    private Long id;
}
