package com.westcatr.rd.car.business.car.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.car.business.car.entity.CarVehicleModelInfo;
import com.westcatr.rd.car.business.car.pojo.query.CarVehicleModelInfoQuery;
import com.westcatr.rd.car.business.car.pojo.vo.CarVehicleBiddingModelVO;
import com.westcatr.rd.car.business.car.pojo.vo.CarVehicleModelInfoVO;

/**
 * <p>
 * 车型信息表—配件 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
public interface CarVehicleModelInfoService extends IService<CarVehicleModelInfo> {

    IPage<CarVehicleModelInfo> entityPage(CarVehicleModelInfoQuery query);

    CarVehicleModelInfo getEntityById(Long id);

    boolean saveEntity(CarVehicleModelInfo param);

    boolean updateEntity(CarVehicleModelInfo param);

    boolean removeEntityById(Long id);

    IPage<CarVehicleModelInfoVO> myVoPage(CarVehicleModelInfoQuery query);

    /**
     * 获取竞价配件库车型信息表分页数据
     * 只返回包含竞价配件的车型信息
     * 
     * @param query 查询条件
     * @return 分页数据
     */
    IPage<CarVehicleModelInfoVO> myBiddingVoPage(CarVehicleModelInfoQuery query);

    /**
     * 获取竞价配件库车型信息表分页数据（使用专用VO）
     * 只返回包含竞价配件的车型信息
     * 
     * @param query 查询条件
     * @return 分页数据
     */
    IPage<CarVehicleBiddingModelVO> getBiddingModelVoPage(CarVehicleModelInfoQuery query);

    /**
     * 删除竞价报价配件和工时
     */
    boolean removeBiddingPartsAndLabor(Long id);
}
