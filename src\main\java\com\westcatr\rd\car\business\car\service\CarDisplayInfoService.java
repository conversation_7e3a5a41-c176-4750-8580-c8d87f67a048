package com.westcatr.rd.car.business.car.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.car.business.car.entity.CarDisplayInfo;
import com.westcatr.rd.car.business.car.pojo.dto.CarModelYearDto;
import com.westcatr.rd.car.business.car.pojo.query.CarDisplayInfoQuery;
import com.westcatr.rd.car.business.car.pojo.query.CarInfoQuery;
import com.westcatr.rd.car.business.car.pojo.vo.CarInfoVO;
import com.westcatr.rd.car.business.openapi.pojo.dto.BannerReturnDto;

import java.util.List;

/**
 * <p>
 * 车辆—信息展示表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
public interface CarDisplayInfoService extends IService<CarDisplayInfo> {

    IPage<CarDisplayInfo> entityPage(CarDisplayInfoQuery query);

    CarDisplayInfo getEntityById(Long id);

    boolean saveEntity(CarDisplayInfo param);

    boolean updateEntity(CarDisplayInfo param);

    boolean removeEntityById(Long id);

    boolean setUpCarouselDisplay(CarDisplayInfo carDisplayInfo);

    /**
     * 获取app置顶车辆图片地址
     *
     * @return
     */
    List<String> getTopOfLineImgUrls();

    /**
     * 获取app置顶车辆图片地址
     *
     * @return
     */
    List<BannerReturnDto> getTopOfLineImgUrls(String urlFirst);

    /**
     * 获取app下面的车型列表展示
     *
     * @param query
     * @return
     */
    IPage<CarInfoVO> getCarInfoVoPage(CarInfoQuery query);

    /**
     * 按照年份获取车型和试驾信息
     *
     * @param carId
     * @return
     */
    List<CarModelYearDto> getCarModelYearDtoList(Long carId);


    /**
     * 购车推荐车辆列表展示
     *
     * @param query
     * @return
     */
    IPage<CarInfoVO> listPurchasedVehicles(CarInfoQuery query);

    /**
     * 设置车辆排序
     *
     * @param carDisplayInfo
     * @return
     */
    boolean setSortInfoDisplay(CarDisplayInfo carDisplayInfo);
}
