package com.westcatr.rd.car.business.demand.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.car.business.demand.entity.DemandInfo;
import com.westcatr.rd.car.business.demand.pojo.dto.GetPhoneNumberReturnDto;
import com.westcatr.rd.car.business.demand.pojo.dto.GetUserPhoneAuditDto;
import com.westcatr.rd.car.business.demand.pojo.query.DemandApplicationPhoneInfoQuery;
import com.westcatr.rd.car.business.demand.pojo.query.DemandInfoQuery;
import com.westcatr.rd.car.business.demand.pojo.vo.DemandApplicationPhoneInfoVO;
import com.westcatr.rd.car.business.demand.pojo.vo.DemandInfoVO;
import com.westcatr.rd.car.business.demand.service.DemandInfoService;
import com.westcatr.rd.car.common.annotation.AuditLog; // Import AuditLog
import com.westcatr.rd.car.enums.OperationTypeEnum; // Import OperationTypeEnum

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * DemandInfo 控制器
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Validated
@Tag(name = "需求—基本信息表接口", description = "需求—基本信息表接口")
@Slf4j
@RestController
public class DemandInfoController {

    @Autowired
    private DemandInfoService demandInfoService;

    @Operation(summary = "获取需求—基本信息表分页数据")
    @PostMapping("/demandInfo/page")
    public IResult<IPage<DemandInfo>> getDemandInfoPage(@RequestBody DemandInfoQuery query) {
        return IResult.ok(demandInfoService.entityPage(query));
    }

    @Operation(summary = "获取需求—基本信息表数据")
    @PostMapping("/demandInfo/get")
    public IResult<DemandInfo> getDemandInfoById(@RequestBody @Id ID id) {
        return IResult.ok(demandInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增需求—基本信息表数据")
    @PostMapping("/demandInfo/add")
    @AuditLog(operationType = OperationTypeEnum.ADD, description = "新增购车需求: 需求ID={#param.id}")
    public IResult addDemandInfo(@RequestBody @Validated(Insert.class) DemandInfo param) {
        // 注意：如果 saveEntity 返回的是包含新生成ID的对象，AuditLogAspect可以尝试获取
        return IResult.auto(demandInfoService.saveEntity(param));
    }

    @Operation(summary = "更新需求—基本信息表数据")
    @PostMapping("/demandInfo/update")
    @AuditLog(operationType = OperationTypeEnum.UPDATE, description = "更新购车需求: ID={#param.id}", businessIdParamName = "param.id")
    public IResult updateDemandInfoById(@RequestBody @Validated(Update.class) DemandInfo param) {
        return IResult.auto(demandInfoService.updateEntity(param));
    }

    @Operation(summary = "删除需求—基本信息表数据")
    @PostMapping("/demandInfo/delete")
    @AuditLog(operationType = OperationTypeEnum.DELETE, description = "删除购车需求: ID(s)={#id.id}", businessIdParamName = "id.id") // 注意:
                                                                                                                               // businessId可能是逗号分隔的
    public IResult deleteDemandInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            demandInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取需求—基本信息表VO分页数据")
    @PostMapping("/demandInfo/voPage")
    public IResult<IPage<DemandInfoVO>> getDemandInfoVoPage(@RequestBody DemandInfoQuery query) {
        return IResult.ok(demandInfoService.myVoPage(query));
    }

    @Operation(summary = "获取需求—基本信息表VO数据")
    @PostMapping("/demandInfo/getVo")
    public IResult<DemandInfoVO> getDemandInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<DemandInfoVO> associationQuery = new AssociationQuery<>(DemandInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    @Operation(summary = "获取手机号码")
    @PostMapping("/demandInfo/getMobilePhoneNumber")
    public IResult<GetPhoneNumberReturnDto> getMobilePhoneNumber(Long id) {
        return IResult.ok(demandInfoService.mobilePhoneNumberTheUserNeed(id));
    }

    @Operation(summary = "忽略需求")
    @PostMapping("/demandInfo/ignoreNeeds")
    @AuditLog(operationType = OperationTypeEnum.UPDATE, description = "忽略购车需求: ID={#id.id}", businessIdParamName = "id.id")
    public IResult ignoreNeeds(@RequestBody @Id ID id) {
        return IResult.auto(demandInfoService.ignoreNeeds(id.longId()));
    }

    @Operation(summary = "用户是否同意供应商获取获取手机号码")
    @PostMapping("/demandInfo/getUserPhoneNumber")
    @AuditLog(operationType = OperationTypeEnum.UPDATE, description = "处理用户获取手机号审核: 需求ID={#getUserPhoneAuditDto.demandId}, 状态={#getUserPhoneAuditDto.status}", businessIdParamName = "getUserPhoneAuditDto.demandId")
    public IResult getUserPhoneNumber(@RequestBody GetUserPhoneAuditDto getUserPhoneAuditDto) {
        return IResult.auto(demandInfoService.getUserPhoneNumber(getUserPhoneAuditDto));
    }

    @Operation(summary = "供应商-我的需求")
    @PostMapping("/demandInfo/getPhoneVoPage")
    public IResult<IPage<DemandApplicationPhoneInfoVO>> getPhoneVoPage(
            @RequestBody DemandApplicationPhoneInfoQuery query) {
        return IResult.ok(demandInfoService.getPhoneVoPage(query));
    }

    @Operation(summary = "我发布的需求")
    @PostMapping("/demandInfo/myPublishedRequirements")
    public IResult<IPage<DemandInfoVO>> myPublishedRequirements(@RequestBody DemandInfoQuery query) {
        return IResult.ok(demandInfoService.myPublishedRequirements(query));
    }

}
