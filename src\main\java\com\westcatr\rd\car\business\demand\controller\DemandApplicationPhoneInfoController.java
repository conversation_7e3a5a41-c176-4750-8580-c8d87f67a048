package com.westcatr.rd.car.business.demand.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.boot.web.excel.pojo.ExcelExportParam;
import com.westcatr.rd.boot.web.excel.service.IExcelExportService;
import com.westcatr.rd.car.business.demand.entity.DemandApplicationPhoneInfo;
import com.westcatr.rd.car.business.demand.pojo.query.DemandApplicationPhoneInfoQuery;
import com.westcatr.rd.car.business.demand.pojo.vo.DemandApplicationPhoneInfoVO;
import com.westcatr.rd.car.business.demand.service.DemandApplicationPhoneInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import static cn.hutool.core.util.StrUtil.COMMA;

/**
 * DemandApplicationPhoneInfo 控制器
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Validated
@Tag(name = "需求—供应商申请获取用户电话信息表接口", description = "需求—供应商申请获取用户电话信息表接口")
@Slf4j
@RestController
public class DemandApplicationPhoneInfoController {

    @Autowired
    private DemandApplicationPhoneInfoService demandApplicationPhoneInfoService;
    @Autowired
    private IExcelExportService iExcelExportService;

    @Operation(summary = "获取需求—供应商申请获取用户电话信息表分页数据")
    @PostMapping("/demandApplicationPhoneInfo/page")
    public IResult<IPage<DemandApplicationPhoneInfo>> getDemandApplicationPhoneInfoPage(@RequestBody DemandApplicationPhoneInfoQuery query) {
        return IResult.ok(demandApplicationPhoneInfoService.entityPage(query));
    }

    @Operation(summary = "获取需求—供应商申请获取用户电话信息表数据")
    @PostMapping("/demandApplicationPhoneInfo/get")
    public IResult<DemandApplicationPhoneInfo> getDemandApplicationPhoneInfoById(@RequestBody @Id ID id) {
        return IResult.ok(demandApplicationPhoneInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增需求—供应商申请获取用户电话信息表数据")
    @PostMapping("/demandApplicationPhoneInfo/add")
    public IResult addDemandApplicationPhoneInfo(@RequestBody @Validated(Insert.class) DemandApplicationPhoneInfo param) {
        return IResult.auto(demandApplicationPhoneInfoService.saveEntity(param));
    }

    @Operation(summary = "更新需求—供应商申请获取用户电话信息表数据")
    @PostMapping("/demandApplicationPhoneInfo/update")
    public IResult updateDemandApplicationPhoneInfoById(@RequestBody @Validated(Update.class) DemandApplicationPhoneInfo param) {
        return IResult.auto(demandApplicationPhoneInfoService.updateEntity(param));
    }

    @Operation(summary = "删除需求—供应商申请获取用户电话信息表数据")
    @PostMapping("/demandApplicationPhoneInfo/delete")
    public IResult deleteDemandApplicationPhoneInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            demandApplicationPhoneInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取需求—供应商申请获取用户电话信息表VO分页数据")
    @PostMapping("/demandApplicationPhoneInfo/voPage")
    public IResult<IPage<DemandApplicationPhoneInfoVO>> getDemandApplicationPhoneInfoVoPage(@RequestBody DemandApplicationPhoneInfoQuery query) {
        AssociationQuery<DemandApplicationPhoneInfoVO> associationQuery = new AssociationQuery<>(DemandApplicationPhoneInfoVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取需求—供应商申请获取用户电话信息表VO数据")
    @PostMapping("/demandApplicationPhoneInfo/getVo")
    public IResult<DemandApplicationPhoneInfoVO> getDemandApplicationPhoneInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<DemandApplicationPhoneInfoVO> associationQuery = new AssociationQuery<>(DemandApplicationPhoneInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    @Operation(summary = "导出需求—供应商申请获取用户电话信息表数据")
    @PostMapping("/demandApplicationPhoneInfo/export")
    public void export(@RequestBody ExcelExportParam<DemandApplicationPhoneInfoQuery> query) {
        iExcelExportService.exportExcel("需求—供应商申请获取用户电话信息表数据", DemandApplicationPhoneInfoVO.class, query, false);
    }

}
