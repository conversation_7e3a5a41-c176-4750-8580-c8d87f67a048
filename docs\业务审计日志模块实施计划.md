# 业务审计日志模块实施计划

## 核心思路

采用 **Spring AOP + 自定义注解** 的方式实现业务审计日志功能，以达到低侵入性、易于维护和扩展、业务与日志解耦的目的。

## 详细实施步骤

### 1. 定义 `@AuditLog` 注解

在 `com.westcatr.rd.car.common.annotation` 包下创建（如果不存在则新建包）。

```java
package com.westcatr.rd.car.common.annotation;

import com.westcatr.rd.car.enums.OperationTypeEnum;
import java.lang.annotation.*;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface AuditLog {

    /**
     * 操作类型
     */
    OperationTypeEnum operationType();

    /**
     * 事件描述模板 (支持SpEL表达式)
     * 例如: "新增用户：{username}" 或 "修改车辆信息：ID={#dto.id}"
     */
    String description() default "";

    /**
     * 业务ID参数名 (用于UPDATE/DELETE操作，指定方法参数中代表业务主键的参数名)
     * 例如: "id" 或 "dto.id"
     */
    String businessIdParamName() default "";
}
```

### 2. 定义 `OperationTypeEnum` 枚举

在 `com.westcatr.rd.car.enums` 包下创建。

```java
package com.westcatr.rd.car.enums;

// 可以根据实际需要调整枚举值
public enum OperationTypeEnum {
    ADD("新增"),
    UPDATE("修改"),
    DELETE("删除"),
    QUERY("查询"),
    IMPORT("导入"),
    EXPORT("导出"),
    UPLOAD("上传"),
    DOWNLOAD("下载"),
    LOGIN("登录"),
    LOGOUT("登出"),
    BACKUP("备份"),
    OTHER("其他");

    private final String description;

    OperationTypeEnum(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}

```

### 3. 创建 `AuditLogInfo` 实体类

在 `com.westcatr.rd.car.business.audit.entity` 包下创建（如果不存在则新建包）。

```java
package com.westcatr.rd.car.business.audit.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("audit_log")
@Schema(name="AuditLogInfo对象", description="业务审计日志表")
public class AuditLogInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "用户唯一ID")
    private String userId;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "操作时间")
    private LocalDateTime operationTime;

    @Schema(description = "操作IP地址")
    private String ipAddress;

    @Schema(description = "操作类型")
    private String operationType; // 存储 OperationTypeEnum 的 name() 或 description

    @Schema(description = "事件描述")
    private String eventDescription;

    @Schema(description = "业务唯一标识 (例如被操作数据的ID)")
    private String businessId;

    @Schema(description = "事件结果 (成功/失败)")
    private String result;

    @Schema(description = "失败时的错误信息")
    private String errorMessage;

    @Schema(description = "请求参数 (JSON格式)")
    private String requestParams; // 建议 TEXT 类型

    @Schema(description = "操作的类名")
    private String className;

    @Schema(description = "操作的方法名")
    private String methodName;

}

```

### 4. 创建 `AuditLogMapper` 和 `AuditLogService`

*   创建 Mapper 接口 `com.westcatr.rd.car.business.audit.mapper.AuditLogMapper`，继承 `BaseMapper<AuditLogInfo>`。
*   创建 Service 接口 `com.westcatr.rd.car.business.audit.service.AuditLogService`，继承 `IService<AuditLogInfo>`。
*   创建 Service 实现类 `com.westcatr.rd.car.business.audit.service.impl.AuditLogServiceImpl`，实现 `AuditLogService`，并实现日志保存逻辑 `saveLog(AuditLogInfo auditLogInfo)`。

### 5. 创建 `IpUtil` 工具类 (可选但推荐)

在 `com.westcatr.rd.car.utils` 包下创建，用于获取真实客户端 IP。

```java
package com.westcatr.rd.car.utils;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.util.StringUtils;

public class IpUtil {

    private static final String UNKNOWN = "unknown";
    private static final String[] IP_HEADER_CANDIDATES = {
            "X-Forwarded-For",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_X_FORWARDED_FOR",
            "HTTP_X_FORWARDED",
            "HTTP_X_CLUSTER_CLIENT_IP",
            "HTTP_CLIENT_IP",
            "HTTP_FORWARDED_FOR",
            "HTTP_FORWARDED",
            "HTTP_VIA",
            "REMOTE_ADDR",
            "X-Real-IP" // Nginx自定义头
    };

    public static String getRealIp(HttpServletRequest request) {
        if (request == null) {
            return UNKNOWN;
        }
        for (String header : IP_HEADER_CANDIDATES) {
            String ip = request.getHeader(header);
            if (StringUtils.hasText(ip) && !UNKNOWN.equalsIgnoreCase(ip)) {
                // X-Forwarded-For可能包含多个IP，取第一个非unknown的
                if ("X-Forwarded-For".equalsIgnoreCase(header)) {
                    String[] ips = ip.split(",");
                    for (String segment : ips) {
                        if (StringUtils.hasText(segment) && !UNKNOWN.equalsIgnoreCase(segment.trim())) {
                            return segment.trim();
                        }
                    }
                }
                return ip;
            }
        }
        // 如果上述都取不到，最后尝试getRemoteAddr
        String remoteAddr = request.getRemoteAddr();
        // 本地回环地址处理
        return "0:0:0:0:0:0:0:1".equals(remoteAddr) || "127.0.0.1".equals(remoteAddr) ? "127.0.0.1" : remoteAddr;
    }
}
```

### 6. 创建 `AuditLogAspect` AOP 切面

在 `com.westcatr.rd.car.configs.aspect` 包下创建（如果不存在则新建包）。

**核心逻辑要点:**

*   使用 `@Aspect`, `@Component` 注解。
*   注入 `AuditLogService`。
*   定义切点 `@Pointcut("@annotation(com.westcatr.rd.car.common.annotation.AuditLog)")`。
*   创建 `@Around` 通知方法。
*   **方法内:**
    *   获取 `HttpServletRequest`: `HttpContextUtil.getHttpServletRequest()`。
    *   获取真实 IP: `IpUtil.getRealIp(request)`。
    *   获取用户: `IUser user = AuthUtil.getUserE();` (注意处理 user 为 null 的情况)。
    *   获取用户 ID 和用户名: `user.getId()`, `user.getUsername()`。
    *   获取方法签名、目标方法、`@AuditLog` 注解实例、方法参数。
    *   使用 `try-catch-finally` 结构执行 `joinPoint.proceed()`。
    *   **成功时:**
        *   组装 `AuditLogInfo` 对象，设置 `result = "成功"`。
        *   使用 SpEL 解析 `description` 模板。
        *   根据 `operationType` 和 `businessIdParamName` 获取 `businessId` (新增时可能从返回值获取)。
    *   **失败时:**
        *   组装 `AuditLogInfo` 对象，设置 `result = "失败"`。
        *   记录 `errorMessage`。
    *   **Finally 中 (或成功/失败后):**
        *   调用 `auditLogService.saveLog(auditLogInfo)` **异步**保存日志。
    *   如果发生异常，在 catch 块中记录日志后，需要 `throw e;` 重新抛出，不影响全局异常处理。

### 7. 配置异步执行

*   在启动类或配置类上添加 `@EnableAsync`。
*   在 `AuditLogServiceImpl` 的 `saveLog` 方法或整个类上添加 `@Async`。
*   (可选) 配置 `ThreadPoolTaskExecutor` Bean。

### 8. 数据库表设计 (`audit_log`)

```sql
CREATE TABLE `audit_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint DEFAULT NULL COMMENT '用户唯一ID',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户名',
  `operation_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `ip_address` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作IP地址',
  `operation_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作类型',
  `event_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '事件描述',
  `business_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务唯一标识',
  `result` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '事件结果 (成功/失败)',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '失败时的错误信息',
  `request_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '请求参数 (JSON格式)',
  `class_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作的类名',
  `method_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作的方法名',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_operation_type` (`operation_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='业务审计日志表';
```
*注意: 字段类型和长度可根据实际情况调整。`event_description`, `error_message`, `request_params` 使用 `TEXT` 类型。为常用查询字段添加了索引。*

### 9. 应用注解

在需要记录审计日志的 Controller 方法上添加 `@AuditLog` 注解。

```java
@RestController
@RequestMapping("/car")
public class CarController {

    @Autowired
    private CarService carService;

    @PostMapping("/add")
    @AuditLog(operationType = OperationTypeEnum.ADD, description = "新增车辆信息：车牌号={#dto.plateNumber}")
    public Result<Void> addCar(@RequestBody CarAddDto dto) {
        // ... 业务逻辑
        return Result.success();
    }

    @PutMapping("/update")
    @AuditLog(operationType = OperationTypeEnum.UPDATE, description = "修改车辆信息：ID={#dto.id}", businessIdParamName = "dto.id")
    public Result<Void> updateCar(@RequestBody CarUpdateDto dto) {
        // ... 业务逻辑
        return Result.success();
    }

    @DeleteMapping("/delete/{id}")
    @AuditLog(operationType = OperationTypeEnum.DELETE, description = "删除车辆信息：ID={#id}", businessIdParamName = "id")
    public Result<Void> deleteCar(@PathVariable Long id) {
        // ... 业务逻辑
        return Result.success();
    }
}
```

## Mermaid 流程图

```mermaid
sequenceDiagram
    participant Client
    participant Controller
    participant AuditLogAspect
    participant AuthService
    participant HttpContextUtil
    participant IpUtil
    participant AuditLogService
    participant Database

    Client->>+Controller: 发起业务请求 (e.g., /car/add)
    Controller->>+AuditLogAspect: 方法调用被拦截 (Around Start)
    AuditLogAspect->>+HttpContextUtil: getHttpServletRequest()
    HttpContextUtil-->>-AuditLogAspect: HttpServletRequest req
    AuditLogAspect->>+IpUtil: getRealIp(req)
    IpUtil-->>-AuditLogAspect: 真实 IP 地址
    AuditLogAspect->>+AuthService: AuthUtil.getUserE()
    AuthService-->>-AuditLogAspect: IUser 对象 (或 null)
    AuditLogAspect->>AuditLogAspect: 获取用户ID, 用户名
    AuditLogAspect->>AuditLogAspect: 获取 @AuditLog 注解信息
    AuditLogAspect->>AuditLogAspect: 获取方法参数
    AuditLogAspect->>+Controller: 执行目标方法 proceed()
    Controller-->>-AuditLogAspect: 方法执行结果 (或异常)
    AuditLogAspect->>AuditLogAspect: (成功) 解析业务 ID, 生成描述
    AuditLogAspect->>AuditLogAspect: (失败) 记录错误信息
    AuditLogAspect->>AuditLogAspect: 组装 AuditLogInfo 对象
    AuditLogAspect-)>>+AuditLogService: saveLog(auditLogInfo) (异步调用)
    AuditLogService->>+Database: 保存日志记录
    Database-->>-AuditLogService: 保存成功
    AuditLogService--)-AuditLogAspect: 异步方法返回
    AuditLogAspect-->>-Client: 返回业务响应