package com.westcatr.rd.car.business.fanfan.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * 泛泛平台HTTP客户端配置
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@Configuration
public class FanFanHttpClientConfig {

    /**
     * 创建 RestTemplate 实例
     *
     * @return RestTemplate 实例
     */
    @Bean("fanFanRestTemplate")
    public RestTemplate restTemplate() {
        return new RestTemplate(clientHttpRequestFactory());
    }

    /**
     * 创建 ClientHttpRequestFactory 实例
     *
     * @return ClientHttpRequestFactory 实例
     */
    private ClientHttpRequestFactory clientHttpRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        // 设置连接超时时间，单位为毫秒
        factory.setConnectTimeout(5000);
        // 设置读取超时时间，单位为毫秒
        factory.setReadTimeout(10000);
        return factory;
    }
}
