package com.westcatr.rd.car.business.demand.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Update;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 需求—当前登录用户查看车辆次数信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("demand_browse_info")
@Schema(description = "需求—当前登录用户查看车辆次数信息表")
public class DemandBrowseInfo extends Model<DemandBrowseInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField("car_id")
    private Long carId;

    @TableField("car_model_id")
    private Long carModelId;

    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("user_id")
    private Long userId;

    public DemandBrowseInfo(Long carId, Long carModelId, Long userId) {
        this.carId = carId;
        this.carModelId = carModelId;
        this.userId = userId;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
