package com.westcatr.rd.car.business.car.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 车辆—信息展示表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("car_display_info")
@Schema(description = "车辆—信息展示表")
public class CarDisplayInfo extends Model<CarDisplayInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @NotNull(message = "不能为空", groups = { Insert.class, Update.class })
    @TableField("car_id")
    private Long carId;

    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "是否轮播展示")
    @TableField("is_carousel")
    private Integer isCarousel;

    @TableField("sort_num")
    @Pattern(regexp = "^$|^[0-9]{1,4}$", message = "只能输入1-4位数字")
    private String sortNum;

    @TableField("car_id_info")
    private String carIdInfo;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
