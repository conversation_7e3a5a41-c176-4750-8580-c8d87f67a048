---
description:
globs:
alwaysApply: false
---
# 项目架构概览

本项目是一个汽车业务管理系统，采用前后端分离架构，包含三个主要部分。

## 后端项目 (e_car_new_back)

后端采用 Java 开发，基于 Spring Boot 框架，使用 Maven 构建，项目配置文件是 [pom.xml](mdc:pom.xml)。

### 核心模块:

- **car**: 车辆基本信息管理
- **testdrive**: 试驾服务管理
- **repair**: 维修服务
- **usedcar**: 二手车交易
- **supplier**: 供应商管理
- **openapi**: 对外开放接口

### 架构模式:

后端遵循标准 MVC 模式，每个业务模块按照以下结构组织:
- controller: 处理 HTTP 请求
- entity: 数据库实体映射
- mapper: 数据库访问层 (MyBatis)
- service: 业务逻辑层
- pojo/dto: 数据传输对象

## 管理端前端 (e-admin)

管理端前端基于 React + Ant Design Pro 框架开发，配置文件是 [e-admin/package.json](mdc:e-admin/package.json)。

### 主要功能模块:

- **cars**: 车辆管理
- **maintenance**: 维修保养管理
- **procurement**: 采购管理
- **system**: 系统配置与管理
- **user**: 用户管理

### 重要组件:

前端使用组件化开发方式，常用组件位于 `src/components` 目录。

## 客户端/移动端前端 (e-app)

客户端前端基于 Vue 3 + Vite 开发，配置文件是 [e-app/package.json](mdc:e-app/package.json)。

### 主要功能:

- 车辆展示与选择
- 试驾申请
- 需求发布
- 二手车交易

## 开发规范

1. **代码风格**: 
   - 后端遵循阿里巴巴 Java 开发规范
   - 前端使用 ESLint + Prettier 规范代码风格

2. **命名规范**:
   - Java 类采用 PascalCase 命名
   - 方法和变量采用 camelCase 命名
   - 数据库表和字段使用下划线命名法
   - 前端组件采用 PascalCase 命名

3. **API 规范**:
   - RESTful API 设计
   - 统一响应格式
   - 版本控制

4. **Git 工作流**:
   - 功能分支 (feature branches)
   - Pull Request 代码审查
   - 语义化版本控制
