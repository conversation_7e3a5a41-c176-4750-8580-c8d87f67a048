package com.westcatr.rd.car.business.audit.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.car.business.audit.entity.AuditLogInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 业务审计日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "业务审计日志表VO对象")
public class AuditLogVO extends AuditLogInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
