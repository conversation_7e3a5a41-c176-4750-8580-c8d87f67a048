package com.westcatr.rd.car.business.demand.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.car.business.demand.pojo.query.DemandUsedCarApplicationPhoneInfoQuery;
import com.westcatr.rd.car.business.demand.entity.DemandUsedCarApplicationPhoneInfo;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * <p>
 * 需求—二手车供应商申请获取用户电话信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public interface DemandUsedCarApplicationPhoneInfoService extends IService<DemandUsedCarApplicationPhoneInfo> {

    IPage<DemandUsedCarApplicationPhoneInfo> entityPage(DemandUsedCarApplicationPhoneInfoQuery query);

    DemandUsedCarApplicationPhoneInfo getEntityById(Long id);

    boolean saveEntity(DemandUsedCarApplicationPhoneInfo param);

    boolean updateEntity(DemandUsedCarApplicationPhoneInfo param);

    boolean removeEntityById(Long id);
}
