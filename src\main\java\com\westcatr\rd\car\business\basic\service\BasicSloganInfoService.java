package com.westcatr.rd.car.business.basic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.car.business.basic.entity.BasicSloganInfo;
import com.westcatr.rd.car.business.basic.pojo.query.BasicSloganInfoQuery;

/**
 * <p>
 * 基础信息—奖品宣传语配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
public interface BasicSloganInfoService extends IService<BasicSloganInfo> {

    IPage<BasicSloganInfo> entityPage(BasicSloganInfoQuery query);

    BasicSloganInfo getEntityById(Long id);

    boolean saveEntity(BasicSloganInfo param);

    boolean updateEntity(BasicSloganInfo param);

    boolean removeEntityById(Long id);

    /**
     * 获取审计日志大小信息
     * 
     * @return 审计日志大小信息
     */
    String getAuditLogSizeInfo();
}
