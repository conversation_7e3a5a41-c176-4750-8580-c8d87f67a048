package com.westcatr.rd.car.business.basic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.car.business.basic.entity.BasicEvaluationLabelInfo;
import com.westcatr.rd.car.business.basic.mapper.BasicEvaluationLabelInfoMapper;
import com.westcatr.rd.car.business.basic.pojo.dto.TypeInfoDto;
import com.westcatr.rd.car.business.basic.pojo.query.BasicEvaluationLabelInfoQuery;
import com.westcatr.rd.car.business.basic.service.BasicEvaluationLabelInfoService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 基本信息—评价标签表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@Service
public class BasicEvaluationLabelInfoServiceImpl extends ServiceImpl<BasicEvaluationLabelInfoMapper, BasicEvaluationLabelInfo> implements BasicEvaluationLabelInfoService {

    @Override
    public IPage<BasicEvaluationLabelInfo> entityPage(BasicEvaluationLabelInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<BasicEvaluationLabelInfo>().create(query));
    }

    @Override
    public BasicEvaluationLabelInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(BasicEvaluationLabelInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(BasicEvaluationLabelInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }

    @Override
    public TypeInfoDto getAll() {
        TypeInfoDto typeInfoDto = new TypeInfoDto();

        List<BasicEvaluationLabelInfo> basicEvaluationLabelInfos = this.list(new LambdaQueryWrapper<>(BasicEvaluationLabelInfo.class).eq(BasicEvaluationLabelInfo::getDisplayStatus, 1).eq(BasicEvaluationLabelInfo::getModuleInfo, "车辆评价"));
        TypeInfoDto.Datas datas = new TypeInfoDto.Datas();
        datas.setAdvantages(basicEvaluationLabelInfos.parallelStream().filter(x -> "好评".equals(x.getLabelType())).map(BasicEvaluationLabelInfo::getLabelContent).distinct().toList());
        datas.setDisadvantages(basicEvaluationLabelInfos.parallelStream().filter(x -> "差评".equals(x.getLabelType())).map(BasicEvaluationLabelInfo::getLabelContent).distinct().toList());
        typeInfoDto.setCarDatas(datas);
        Collections.shuffle(basicEvaluationLabelInfos);
        typeInfoDto.setRandomCar(basicEvaluationLabelInfos.stream()
                .limit(8)
                .map(BasicEvaluationLabelInfo::getLabelContent)
                .collect(Collectors.toList()));

        List<BasicEvaluationLabelInfo> basicEvaluationLabelInfos1 = this.list(new LambdaQueryWrapper<>(BasicEvaluationLabelInfo.class).eq(BasicEvaluationLabelInfo::getDisplayStatus, 1).eq(BasicEvaluationLabelInfo::getModuleInfo, "服务评价"));
        TypeInfoDto.Datas datas1 = new TypeInfoDto.Datas();
        datas1.setAdvantages(basicEvaluationLabelInfos1.parallelStream().filter(x -> "好评".equals(x.getLabelType())).map(BasicEvaluationLabelInfo::getLabelContent).distinct().toList());
        datas1.setDisadvantages(basicEvaluationLabelInfos1.parallelStream().filter(x -> "差评".equals(x.getLabelType())).map(BasicEvaluationLabelInfo::getLabelContent).distinct().toList());
        typeInfoDto.setSupplierDatas(datas1);
        Collections.shuffle(basicEvaluationLabelInfos1);
        typeInfoDto.setRandomSupplier(basicEvaluationLabelInfos1.stream()
                .limit(8)
                .map(BasicEvaluationLabelInfo::getLabelContent)
                .collect(Collectors.toList()));
        return typeInfoDto;
    }
}

