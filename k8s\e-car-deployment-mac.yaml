apiVersion: apps/v1
kind: Deployment
metadata:
  name: e-car-back-mac
  namespace: default
  labels:
    app: e-car-back-mac
spec:
  replicas: 1
  selector:
    matchLabels:
      app: e-car-back-mac
  template:
    metadata:
      labels:
        app: e-car-back-mac
    spec:
      containers:
      - name: e-car-back
        image: e_car_new_back:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 7331
        env:
        - name: SPRING_CONFIG_IMPORT
          value: "optional:configtree:/etc/config/"
        volumeMounts:
        - name: logs-volume
          mountPath: /app/logs
        - name: uploads-volume
          mountPath: /app/uploads
        - name: config-volume
          mountPath: /etc/config
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 7331
          initialDelaySeconds: 120
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 7331
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            cpu: "200m"
            memory: "512Mi"
          limits:
            cpu: "1000m"
            memory: "2Gi"
      volumes:
      - name: logs-volume
        emptyDir: {}
      - name: uploads-volume
        emptyDir: {}
      - name: config-volume
        configMap:
          name: e-car-config-mac
      restartPolicy: Always