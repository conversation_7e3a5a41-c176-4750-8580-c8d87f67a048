package com.westcatr.rd.car.business.car.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.car.business.car.entity.CarDisplayInfo;
import com.westcatr.rd.car.business.car.entity.CarInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 车辆—信息展示表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "车辆—信息展示表VO对象")
public class CarDisplayInfoVO extends CarDisplayInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    @Schema(description = "车辆简介")
    @JoinSelect(joinClass = CarInfo.class, mainId = "carId")
    private CarInfo carInfo;
}
