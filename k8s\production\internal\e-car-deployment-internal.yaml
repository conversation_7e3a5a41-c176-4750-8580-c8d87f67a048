apiVersion: apps/v1
kind: Deployment
metadata:
  name: e-car-back-internal
  namespace: default
  labels:
    app: e-car-back-internal
    environment: production
    network: internal
spec:
  replicas: 2
  selector:
    matchLabels:
      app: e-car-back-internal
  template:
    metadata:
      labels:
        app: e-car-back-internal
        environment: production
        network: internal
    spec:
      containers:
      - name: e-car-back
        image: e_car_new_back:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8080
        # 环境变量配置
        env:
        - name: SPRING_CONFIG_IMPORT
          value: "optional:configtree:/etc/config/"
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: MYSQL_HOST
          value: *************
        - name: MYSQL_PORT
          value: 13306
        - name: MYSQL_USER
          value: root
        - name: MYSQL_PASSWORD
          value: Yhblsqt@wyy888
        - name: MYSQL_DATABASE
          value: e_car_db
        # 从ConfigMap加载所有配置
        envFrom:
        - configMapRef:
            name: e-car-config-internal
        # 健康检查
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 120
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        # 资源限制
        resources:
          requests:
            cpu: "500m"
            memory: "1Gi"
          limits:
            cpu: "2000m"
            memory: "4Gi"
        # 挂载卷
        volumeMounts:
        - name: logs-volume
          mountPath: /app/logs
        - name: uploads-volume
          mountPath: /app/uploads
        - name: config-volume
          mountPath: /etc/config
      volumes:
      - name: logs-volume
        emptyDir: {}
      - name: uploads-volume
        persistentVolumeClaim:
          claimName: e-car-uploads-pvc-internal
      - name: config-volume
        configMap:
          name: e-car-config-internal
      # 重启策略
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: e-car-back-service-internal
  namespace: default
  labels:
    environment: production
    network: internal
spec:
  selector:
    app: e-car-back-internal
  ports:
    - protocol: TCP
      port: 7330
      targetPort: 8080
      nodePort: 30330
  type: NodePort
---
# 持久化存储卷声明
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: e-car-uploads-pvc-internal
  namespace: default
  labels:
    environment: production
    network: internal
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
