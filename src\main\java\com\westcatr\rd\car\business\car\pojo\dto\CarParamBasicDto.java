package com.westcatr.rd.car.business.car.pojo.dto;


import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 */
public class CarParamBasicDto {

    @Schema(description = "品牌车型")
    private String brandModel;


    @Schema(description = "年款")
    private Integer yearModel;

    @Schema(description = "配置名称")
    private String configName;

    @Schema(description = "车型价格")
    @TableField("price")
    private Double price;
}
