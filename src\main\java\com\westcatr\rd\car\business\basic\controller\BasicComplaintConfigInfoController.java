package com.westcatr.rd.car.business.basic.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.boot.web.excel.pojo.ExcelExportParam;
import com.westcatr.rd.boot.web.excel.service.IExcelExportService;
import com.westcatr.rd.car.business.basic.entity.BasicComplaintConfigInfo;
import com.westcatr.rd.car.business.basic.pojo.query.BasicComplaintConfigInfoQuery;
import com.westcatr.rd.car.business.basic.pojo.vo.BasicComplaintConfigInfoVO;
import com.westcatr.rd.car.business.basic.service.BasicComplaintConfigInfoService;
import com.westcatr.rd.car.common.annotation.AuditLog;
import com.westcatr.rd.car.enums.OperationTypeEnum;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * BasicComplaintConfigInfo 控制器
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Validated
@Tag(name = "投诉配置表接口", description = "投诉配置表接口")
@Slf4j
@RestController
public class BasicComplaintConfigInfoController {

    @Autowired
    private BasicComplaintConfigInfoService basicComplaintConfigInfoService;
    @Autowired
    private IExcelExportService iExcelExportService;

    @Operation(summary = "获取投诉配置表分页数据")
    @PostMapping("/basicComplaintConfigInfo/page")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询投诉配置表分页数据")
    public IResult<IPage<BasicComplaintConfigInfo>> getBasicComplaintConfigInfoPage(
            @RequestBody BasicComplaintConfigInfoQuery query) {
        return IResult.ok(basicComplaintConfigInfoService.entityPage(query));
    }

    @Operation(summary = "获取投诉配置表数据")
    @PostMapping("/basicComplaintConfigInfo/get")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询投诉配置表数据: ID={#id.id}")
    public IResult<BasicComplaintConfigInfo> getBasicComplaintConfigInfoById(@RequestBody @Id ID id) {
        return IResult.ok(basicComplaintConfigInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增投诉配置表数据")
    @PostMapping("/basicComplaintConfigInfo/add")
    @AuditLog(operationType = OperationTypeEnum.ADD, description = "新增投诉配置表数据: ID={#param.id}")
    public IResult addBasicComplaintConfigInfo(@RequestBody @Validated(Insert.class) BasicComplaintConfigInfo param) {
        return IResult.auto(basicComplaintConfigInfoService.saveEntity(param));
    }

    @Operation(summary = "更新投诉配置表数据")
    @PostMapping("/basicComplaintConfigInfo/update")
    @AuditLog(operationType = OperationTypeEnum.UPDATE, description = "更新投诉配置表数据: ID={#param.id}")
    public IResult updateBasicComplaintConfigInfoById(
            @RequestBody @Validated(Update.class) BasicComplaintConfigInfo param) {
        return IResult.auto(basicComplaintConfigInfoService.updateEntity(param));
    }

    @Operation(summary = "删除投诉配置表数据")
    @PostMapping("/basicComplaintConfigInfo/delete")
    @AuditLog(operationType = OperationTypeEnum.DELETE, description = "删除投诉配置表数据: ID={#id.id}")
    public IResult deleteBasicComplaintConfigInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            basicComplaintConfigInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取投诉配置表VO分页数据")
    @PostMapping("/basicComplaintConfigInfo/voPage")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询投诉配置表VO分页数据")
    public IResult<IPage<BasicComplaintConfigInfoVO>> getBasicComplaintConfigInfoVoPage(
            @RequestBody BasicComplaintConfigInfoQuery query) {
        AssociationQuery<BasicComplaintConfigInfoVO> associationQuery = new AssociationQuery<>(
                BasicComplaintConfigInfoVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取投诉配置表VO数据")
    @PostMapping("/basicComplaintConfigInfo/getVo")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询投诉配置表VO数据: ID={#id.id}")
    public IResult<BasicComplaintConfigInfoVO> getBasicComplaintConfigInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<BasicComplaintConfigInfoVO> associationQuery = new AssociationQuery<>(
                BasicComplaintConfigInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    @Operation(summary = "导出投诉配置表数据")
    @PostMapping("/basicComplaintConfigInfo/export")
    @AuditLog(operationType = OperationTypeEnum.EXPORT, description = "导出投诉配置表数据")
    public void export(@RequestBody ExcelExportParam<BasicComplaintConfigInfoQuery> query) {
        iExcelExportService.exportExcel("投诉配置表数据", BasicComplaintConfigInfoVO.class, query, false);
    }

}
