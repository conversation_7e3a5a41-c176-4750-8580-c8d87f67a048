package com.westcatr.rd.car.business.basic.entity;

import java.io.Serializable;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.web.excel.annotation.ExcelField;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 基础信息—奖品宣传语配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("basic_slogan_info")
@Schema(description = "基础信息—奖品宣传语配置表")
public class BasicSloganInfo extends Model<BasicSloganInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @ExcelField(name = "主键ID")
    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "宣传语")
    @ExcelField(name = "宣传语")
    @Length(max = 255, message = "宣传语长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("slogan_info")
    private String sloganInfo;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
