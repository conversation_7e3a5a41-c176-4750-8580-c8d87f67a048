# E出行维修模块PC端与泛泛平台技术对接文档

## 📋 文档概述

本文档是E出行系统与泛泛平台维修订单业务的完整技术对接文档，包含接口规范、安全认证、业务流程、数据格式等所有技术细节。

---

## 🔐 第一部分：安全认证方案

### 1.1 OpenAPI国密认证机制

所有接口均采用OpenAPI国密方案进行认证，使用SM2/SM3/SM4国密算法套件。

#### 认证流程
```
1. 客户端生成请求数据
2. 使用SM4算法加密请求体
3. 使用SM3私钥对请求进行数字签名
4. 发送加密后的请求到服务端
5. 服务端验证签名并解密数据
6. 处理业务逻辑并返回加密响应
```

#### 认证参数配置

**泛泛平台**:
- **应用ID**: `fanfan_system`
- **应用密钥**: `FF-3EC294C7-9CD6-BE47-5DA3-07C21F9C92FE`

#### 加密参数
- **SM4密钥**: `31323334353637383930616263646566`
- **SM4初始向量**: `66656463626130393837363534333231`
- **请求有效期**: 300秒

### 1.2 请求头格式

```http
Content-Type: application/json
X-App-Id: {应用ID}
X-Timestamp: {时间戳}
X-Nonce: {随机数}
X-Signature: {签名}
```
### 1.3 数据加密

**请求加密**:
```
1. 将JSON请求体转换为字符串
2. 使用SM4算法加密（CBC模式）
3. Base64编码加密结果
```

**响应解密**:
```
1. Base64解码响应数据
2. 使用SM4算法解密
3. 解析JSON数据
```

### 1.4 环境说明

- 开发环境：`https://5j71c34d-7331.asse.devtunnels.ms/app` 

---

## 📊 第二部分：业务流程与状态管理

### 2.1 完整业务流程

根据维修业务流程图，完整的维修流程如下：

```
开始 → 创建订单 → 报价 → 报价审批 → 认质认价/竞价报价 → 维修 → 清单审批 → 验收 → 对账 → 结束
```

### 2.2 三个独立状态管理

系统使用三个独立的状态字段来管理维修流程：

#### 2.2.1 维修状态 (`vehicle_status`)
```
待报价 → 报价审批 → 认价中/竞价中 → 报价驳回/认价驳回 → 待维修 → 清单审批 → 清单驳回 → 待验收 → 验收驳回 → 验收整改 → 已完成
```

#### 2.2.2 认质认价状态 (`approval_status`)
```
草稿中 → 审批中 → 已驳回/已通过
```

#### 2.2.3 竞价报价状态 (`bidding_status`)
```
草稿中 → 竞价中 → 审批中 → 已完成
```

### 2.3 异步状态更新机制

- 维修完成后，E出行系统设置状态为"待验收"
- 等待泛泛平台的验收确认回调
- 收到确认回调后，更新为最终状态
- 确保数据最终一致性

---

## 🚗 第三部分：E出行系统提供的接口

### 3.1 业务接口（6个）

#### 3.1.1 创建维修订单

- **接口地址**: `https://ecar.example.com/openApi/fanfan/repair/createOrder`
- **请求方法**: POST
- **功能说明**: 泛泛平台发起创建维修订单

**请求参数（解密后）**:
```json
{
  "repairOrderCode": "FF202501200001",
  "vehicleInfo": {
    "plateNumber": "京A12345",
    "vehicleModel": "比亚迪秦PLUS",
    "vinCode": "LGWEF4A59LG123456"
  },
  "supplierInfo": {
    "supplierId": 456,
    "supplierName": "XX维修厂"
  },
  "estimatedAmount": 5000.00,
  "repairContent": "刹车系统维修",
  "urgencyLevel": "普通"
}
```

**响应参数（解密后）**:
```json
{
  "success": true,
  "message": "维修订单创建成功",
  "data": "FF202501200001"
}
```

#### 3.1.2 更新维修订单状态

- **接口地址**: `https://ecar.example.com/openApi/fanfan/repair/updateStatus`
- **请求方法**: POST
- **功能说明**: 更新维修订单状态

**请求参数（解密后）**:
```json
{
  "repairOrderCode": "FF202501200001",
  "newStatus": "待维修",
  "statusReason": "报价审批通过",
  "updateTime": "2025-01-20 14:30:00"
}
```

#### 3.1.3 查询维修订单状态

- **接口地址**: `https://ecar.example.com/openApi/fanfan/repair/queryStatus`
- **请求方法**: POST
- **功能说明**: 查询维修订单当前状态

**请求参数（解密后）**:
```json
{
  "repairOrderCode": "FF202501200001"
}
```

**响应参数（解密后）**:
```json
{
  "success": true,
  "data": {
    "repairOrderCode": "FF202501200001",
    "vehicleStatus": "待验收",
    "approvalStatus": "已通过",
    "biddingStatus": "已完成",
    "statementStatus": "未对账",
    "updateTime": "2025-01-20 15:30:00"
  }
}
```

#### 3.1.4 提交维修结果

- **接口地址**: `https://ecar.example.com/openApi/fanfan/repair/submitResult`
- **请求方法**: POST
- **功能说明**: 提交维修完成结果

**请求参数（解密后）**:
```json
{
  "repairOrderCode": "FF202501200001",
  "repairResult": {
    "actualAmount": 4800.00,
    "actualLaborAmount": 1800.00,
    "actualPartsAmount": 3000.00,
    "completedTime": "2025-01-20 15:30:00",
    "repairContent": "更换刹车片、机油保养",
    "repairImages": ["图片URL1", "图片URL2"]
  },
  "partsUsed": [
    {
      "partsName": "刹车片",
      "partsCode": "BP001",
      "quantity": 4,
      "unitPrice": 150.00,
      "totalPrice": 600.00
    }
  ],
  "laborHours": [
    {
      "laborName": "刹车片更换",
      "laborCode": "LH001",
      "hours": 2.0,
      "unitPrice": 100.00,
      "totalPrice": 200.00
    }
  ]
}
```

#### 3.1.5 提交对账单

- **接口地址**: `https://ecar.example.com/openApi/fanfan/repair/submitStatement`
- **请求方法**: POST
- **功能说明**: 向泛泛平台提交对账单

**请求参数（解密后）**:
```json
{
  "statementCode": "ST202501001",
  "statementMonth": "2025-01",
  "supplierInfo": {
    "supplierId": 456,
    "supplierName": "XX维修厂",
    "contactPerson": "张三",
    "contactPhone": "***********",
    "bankAccount": "6228480402564890018",
    "bankName": "中国农业银行北京分行"
  },
  "totalAmount": 50000.00,
  "repairOrders": [
    {
      "repairOrderCode": "FF202501200001",
      "plateNumber": "京A12345",
      "repairAmount": 4800.00,
      "completedTime": "2025-01-20 15:30:00"
    }
  ]
}
```

#### 3.1.6 查询对账单状态

- **接口地址**: `https://ecar.example.com/openApi/fanfan/repair/queryStatement`
- **请求方法**: POST
- **功能说明**: 查询对账单状态

**请求参数（解密后）**:
```json
{
  "statementCode": "ST202501001"
}
```

### 3.2 回调接口（4个）

#### 3.2.1 维修订单状态更新回调

- **接口地址**: `https://ecar.example.com/openApi/repair/callback/statusUpdate`
- **请求方法**: POST
- **功能说明**: 接收泛泛平台的维修订单状态更新通知

**请求参数**:
```
repairOrderCode: 维修工单号
newStatus: 新状态
statusReason: 状态变更原因（可选）
```

#### 3.2.2 维修完成确认回调

- **接口地址**: `https://ecar.example.com/openApi/repair/callback/completionConfirm`
- **请求方法**: POST
- **功能说明**: 接收泛泛平台的维修完成确认通知

**请求参数**:
```
repairOrderCode: 维修工单号
confirmResult: 确认结果（通过/驳回）
confirmReason: 确认原因（可选）
```

#### 3.2.3 对账单状态更新回调

- **接口地址**: `https://ecar.example.com/openApi/repair/callback/statementStatusUpdate`
- **请求方法**: POST
- **功能说明**: 接收泛泛平台的对账单状态更新通知

**请求参数**:
```
statementCode: 对账单号
newStatus: 新状态
statusReason: 状态变更原因（可选）
```

#### 3.2.4 对账单确认回调

- **接口地址**: `https://ecar.example.com/openApi/repair/callback/statementConfirm`
- **请求方法**: POST
- **功能说明**: 接收泛泛平台的对账单确认通知

**请求参数**:
```
statementCode: 对账单号
confirmResult: 确认结果（通过/驳回）
confirmReason: 确认原因（可选）
```

---

## 🏢 第四部分：泛泛平台需要提供的接口

### 4.1 接收维修订单提交

- **接口地址**: `https://fanfan.example.com/api/repair/order/submit`
- **请求方法**: POST
- **功能说明**: 接收E出行系统提交的维修订单完成信息

**请求参数（解密后）**:
```json
{
  "repairOrderCode": "FF202501200001",
  "supplierInfo": {
    "supplierId": 456,
    "supplierName": "XX维修厂",
    "contactPerson": "张三",
    "contactPhone": "***********",
    "address": "北京市朝阳区XX路XX号"
  },
  "vehicleInfo": {
    "plateNumber": "京A12345",
    "vehicleModel": "比亚迪秦PLUS",
    "vinCode": "LGWEF4A59LG123456"
  },
  "repairResult": {
    "actualAmount": 4800.00,
    "actualLaborAmount": 1800.00,
    "actualPartsAmount": 3000.00,
    "completedTime": "2025-01-20 15:30:00",
    "repairContent": "更换刹车片、机油保养",
    "repairImages": ["图片URL1", "图片URL2"]
  },
  "partsUsed": [
    {
      "partsName": "刹车片",
      "partsCode": "BP001",
      "quantity": 4,
      "unitPrice": 150.00,
      "totalPrice": 600.00
    }
  ],
  "laborHours": [
    {
      "laborName": "刹车片更换",
      "laborCode": "LH001",
      "hours": 2.0,
      "unitPrice": 100.00,
      "totalPrice": 200.00
    }
  ]
}
```

**响应参数（解密后）**:
```json
{
  "success": true,
  "message": "维修订单接收成功",
  "data": {
    "orderId": "泛泛平台订单ID",
    "status": "待验收"
  }
}
```

### 4.2 维修订单状态查询

- **接口地址**: `https://fanfan.example.com/api/repair/order/status`
- **请求方法**: POST
- **功能说明**: 查询维修订单在泛泛平台的处理状态

**请求参数（解密后）**:
```json
{
  "repairOrderCode": "FF202501200001"
}
```

**响应参数（解密后）**:
```json
{
  "success": true,
  "data": {
    "repairOrderCode": "FF202501200001",
    "status": "已完成",
    "processTime": "2025-01-20 16:00:00",
    "remark": "验收通过"
  }
}
```

### 4.3 提交对账单

- **接口地址**: `https://fanfan.example.com/api/statement/submit`
- **请求方法**: POST
- **功能说明**: 向泛泛平台提交月度对账单

**请求参数（解密后）**:
```json
{
  "statementCode": "ST202501001",
  "statementMonth": "2025-01",
  "supplierInfo": {
    "supplierId": 456,
    "supplierName": "XX维修厂",
    "contactPerson": "张三",
    "contactPhone": "***********",
    "bankAccount": "6228480402564890018",
    "bankName": "中国农业银行北京分行"
  },
  "totalAmount": 50000.00,
  "orderCount": 25,
  "repairOrders": [
    {
      "repairOrderCode": "FF202501200001",
      "plateNumber": "京A12345",
      "repairAmount": 4800.00,
      "completedTime": "2025-01-20 15:30:00"
    }
  ]
}
```

**响应参数（解密后）**:
```json
{
  "success": true,
  "message": "对账单提交成功",
  "data": {
    "statementId": "泛泛平台对账单ID",
    "status": "审核中"
  }
}
```

### 4.4 对账单状态查询

- **接口地址**: `https://fanfan.example.com/api/statement/status`
- **请求方法**: POST
- **功能说明**: 查询对账单审核状态

**请求参数（解密后）**:
```json
{
  "statementCode": "ST202501001"
}
```

**响应参数（解密后）**:
```json
{
  "success": true,
  "data": {
    "statementCode": "ST202501001",
    "status": "已确认",
    "confirmTime": "2025-01-25 10:00:00",
    "confirmAmount": 50000.00,
    "remark": "对账无误"
  }
}
```

---

## 🔄 第五部分：数据流程与时序图

### 5.1 维修订单完整流程

```mermaid
sequenceDiagram
    participant FF as 泛泛平台
    participant EC as E出行系统
    participant SF as 维修厂

    FF->>EC: 1. 创建维修订单 (createOrder)
    EC->>SF: 2. 分配维修任务
    SF->>EC: 3. 报价
    EC->>FF: 4. 状态更新（报价完成）
    FF->>EC: 5. 审批结果回调 (statusUpdate)
    SF->>EC: 6. 维修完成
    EC->>FF: 7. 提交维修结果 (order/submit)
    FF->>EC: 8. 验收确认回调 (completionConfirm)
    EC->>FF: 9. 提交对账单 (statement/submit)
    FF->>EC: 10. 对账确认回调 (statementConfirm)
```

### 5.2 对账流程

```
E出行生成对账单 → 提交到泛泛平台 → 泛泛平台审核 → 确认回调通知 → E出行更新对账状态
```

### 5.3 异步状态同步机制

```
E出行状态变更 → 推送到泛泛平台 → 泛泛平台处理 → 回调确认 → E出行更新最终状态
```

---

## 📋 第六部分：接口清单总结

### 6.1 接口总览

| 系统 | 接口类型 | 数量 | 说明 |
|------|----------|------|------|
| E出行系统 | 业务接口 | 6个 | 供泛泛平台调用 |
| E出行系统 | 回调接口 | 4个 | 接收泛泛平台回调 |
| 泛泛平台 | 业务接口 | 4个 | 供E出行系统调用 |
| **总计** | **所有接口** | **14个** | **完整对接方案** |

### 6.2 业务流程对应关系

#### 维修订单流程
```
1. 泛泛平台 → E出行: 创建维修订单 (createOrder)
2. E出行 → 泛泛平台: 状态更新通知 (状态变更时)
3. 泛泛平台 → E出行: 状态更新回调 (statusUpdate)
4. E出行 → 泛泛平台: 提交维修结果 (submitResult → order/submit)
5. 泛泛平台 → E出行: 维修完成确认回调 (completionConfirm)
```

#### 对账流程
```
1. E出行 → 泛泛平台: 提交对账单 (submitStatement → statement/submit)
2. 泛泛平台 → E出行: 对账单状态更新回调 (statementStatusUpdate)
3. 泛泛平台 → E出行: 对账单确认回调 (statementConfirm)
```

---

## ⚠️ 第七部分：重要说明与技术要求

### 7.1 异步状态更新机制

- 维修完成后，E出行系统不会立即将状态设置为"已完成"
- 而是设置为"待验收"，等待泛泛平台的验收确认回调
- 只有收到泛泛平台的确认回调后，才会更新为最终状态

### 7.2 三状态并行管理

- 维修状态、认质认价状态、竞价报价状态可以并行进行
- 每个状态都有独立的流转逻辑
- 状态变更需要及时同步给泛泛平台

### 7.3 数据一致性保证

- 所有状态变更都需要通过回调机制确认
- 支持重试机制，确保数据最终一致性
- 提供查询接口用于状态核对

### 7.4 安全性要求

- 严格按照OpenAPI国密方案进行数据加密和签名验证
- 所有接口调用都需要进行身份认证
- 敏感数据传输过程中全程加密

### 7.5 错误处理

- 提供详细的错误码和错误信息
- 区分业务错误和系统错误
- 支持错误重试和降级处理