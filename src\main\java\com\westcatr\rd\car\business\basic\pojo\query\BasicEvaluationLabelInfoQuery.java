package com.westcatr.rd.car.business.basic.pojo.query;

import com.westcatr.rd.boot.orm.dto.TimeDTO;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 基本信息—评价标签表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "基本信息—评价标签表查询对象")
public class BasicEvaluationLabelInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @QueryCondition
    private Long id;

    @Schema(description = "所属模块")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String moduleInfo;

    @Schema(description = "标签类型")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String labelType;

    @Schema(description = "标签内容")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String labelContent;

    @Schema(description = "展示状态（0：不展示、1：展示）")
    @QueryCondition
    private Integer displayStatus;

    @QueryCondition
    private Date createTime;

    @QueryCondition
    private Date updateTime;
}
