package com.westcatr.rd.car.business.basic.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.boot.web.excel.pojo.ExcelExportParam;
import com.westcatr.rd.boot.web.excel.service.IExcelExportService;
import com.westcatr.rd.car.business.basic.entity.BasicEvaluationLabelInfo;
import com.westcatr.rd.car.business.basic.pojo.dto.TypeInfoDto;
import com.westcatr.rd.car.business.basic.pojo.query.BasicEvaluationLabelInfoQuery;
import com.westcatr.rd.car.business.basic.pojo.vo.BasicEvaluationLabelInfoVO;
import com.westcatr.rd.car.business.basic.service.BasicEvaluationLabelInfoService;
import com.westcatr.rd.car.common.annotation.AuditLog;
import com.westcatr.rd.car.enums.OperationTypeEnum;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * BasicEvaluationLabelInfo 控制器
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@Validated
@Tag(name = "基本信息—评价标签表接口", description = "基本信息—评价标签表接口")
@Slf4j
@RestController
public class BasicEvaluationLabelInfoController {

    @Autowired
    private BasicEvaluationLabelInfoService basicEvaluationLabelInfoService;
    @Autowired
    private IExcelExportService iExcelExportService;

    @Operation(summary = "获取基本信息—评价标签表分页数据")
    @PostMapping("/basicEvaluationLabelInfo/page")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询评价标签表分页数据")
    public IResult<IPage<BasicEvaluationLabelInfo>> getBasicEvaluationLabelInfoPage(
            @RequestBody BasicEvaluationLabelInfoQuery query) {
        return IResult.ok(basicEvaluationLabelInfoService.entityPage(query));
    }

    @Operation(summary = "获取基本信息—评价标签表数据")
    @PostMapping("/basicEvaluationLabelInfo/get")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询评价标签表数据: ID={#id.id}")
    public IResult<BasicEvaluationLabelInfo> getBasicEvaluationLabelInfoById(@RequestBody @Id ID id) {
        return IResult.ok(basicEvaluationLabelInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增基本信息—评价标签表数据")
    @PostMapping("/basicEvaluationLabelInfo/add")
    @AuditLog(operationType = OperationTypeEnum.ADD, description = "新增评价标签表数据: ID={#param.id}")
    public IResult addBasicEvaluationLabelInfo(@RequestBody @Validated(Insert.class) BasicEvaluationLabelInfo param) {
        return IResult.auto(basicEvaluationLabelInfoService.saveEntity(param));
    }

    @Operation(summary = "更新基本信息—评价标签表数据")
    @PostMapping("/basicEvaluationLabelInfo/update")
    @AuditLog(operationType = OperationTypeEnum.UPDATE, description = "更新评价标签表数据: ID={#param.id}")
    public IResult updateBasicEvaluationLabelInfoById(
            @RequestBody @Validated(Update.class) BasicEvaluationLabelInfo param) {
        return IResult.auto(basicEvaluationLabelInfoService.updateEntity(param));
    }

    @Operation(summary = "删除基本信息—评价标签表数据")
    @PostMapping("/basicEvaluationLabelInfo/delete")
    @AuditLog(operationType = OperationTypeEnum.DELETE, description = "删除评价标签表数据: ID={#id.id}")
    public IResult deleteBasicEvaluationLabelInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            basicEvaluationLabelInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取基本信息—评价标签表VO分页数据")
    @PostMapping("/basicEvaluationLabelInfo/voPage")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询评价标签表VO分页数据")
    public IResult<IPage<BasicEvaluationLabelInfoVO>> getBasicEvaluationLabelInfoVoPage(
            @RequestBody BasicEvaluationLabelInfoQuery query) {
        AssociationQuery<BasicEvaluationLabelInfoVO> associationQuery = new AssociationQuery<>(
                BasicEvaluationLabelInfoVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取基本信息—评价标签表VO数据")
    @PostMapping("/basicEvaluationLabelInfo/getVo")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询评价标签表VO数据: ID={#id.id}")
    public IResult<BasicEvaluationLabelInfoVO> getBasicEvaluationLabelInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<BasicEvaluationLabelInfoVO> associationQuery = new AssociationQuery<>(
                BasicEvaluationLabelInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    @Operation(summary = "导出基本信息—评价标签表数据")
    @PostMapping("/basicEvaluationLabelInfo/export")
    @AuditLog(operationType = OperationTypeEnum.EXPORT, description = "导出评价标签表数据")
    public void export(@RequestBody ExcelExportParam<BasicEvaluationLabelInfoQuery> query) {
        iExcelExportService.exportExcel("基本信息—评价标签表数据", BasicEvaluationLabelInfoVO.class, query, false);
    }

    @Operation(summary = "导出基本信息—按类型获取车辆评价所有标签")
    @PostMapping("/basicEvaluationLabelInfo/getAllInfosByType")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "按类型获取车辆评价所有标签")
    public IResult<TypeInfoDto> getAllInfosByType() {
        return IResult.ok(basicEvaluationLabelInfoService.getAll());
    }

}
