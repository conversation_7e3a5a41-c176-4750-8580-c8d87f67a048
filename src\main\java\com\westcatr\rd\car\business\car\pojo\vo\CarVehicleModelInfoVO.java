package com.westcatr.rd.car.business.car.pojo.vo;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.car.business.car.entity.CarVehicleModelInfo;
import com.westcatr.rd.car.business.car.entity.CarVehicleModelPartsInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 车型信息表—配件
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "车型信息表—配件VO对象")
public class CarVehicleModelInfoVO extends CarVehicleModelInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "配件")
    @JoinSelect(joinClass = CarVehicleModelPartsInfo.class, relationId = "vehicle_model_id")
    private List<CarVehicleModelPartsInfo> carVehicleModelPartsInfos;

    @Schema(description = "配件数量")
    @TableField(exist = false)
    private long partsCount;
}
