package com.westcatr.rd.car.business.basic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.car.business.basic.entity.BasicEvaluationLabelInfo;
import com.westcatr.rd.car.business.basic.pojo.dto.TypeInfoDto;
import com.westcatr.rd.car.business.basic.pojo.query.BasicEvaluationLabelInfoQuery;

/**
 * <p>
 * 基本信息—评价标签表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
public interface BasicEvaluationLabelInfoService extends IService<BasicEvaluationLabelInfo> {

    IPage<BasicEvaluationLabelInfo> entityPage(BasicEvaluationLabelInfoQuery query);

    BasicEvaluationLabelInfo getEntityById(Long id);

    boolean saveEntity(BasicEvaluationLabelInfo param);

    boolean updateEntity(BasicEvaluationLabelInfo param);

    boolean removeEntityById(Long id);

    TypeInfoDto getAll();
}
