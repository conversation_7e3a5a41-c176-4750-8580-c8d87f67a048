# E出行车辆选型/维修模块对接文档

## 1. 概述

本文档描述了使用国密算法（SM3、SM4）实现的OpenAPI接口安全方案，用于保障系统间数据交互的安全性。该方案采用SM4对称加密算法对业务数据进行加密，使用SM3哈希算法进行签名验证，确保数据的机密性、完整性和不可抵赖性。

## 2. 接入流程

### 2.1 申请接入

认证需要的信息如下：

- `appId`：etravel
- `appSecret`：3EC294C7-9CD6-BE47-5DA3-07C21F9C92FE
- `sm4Key`：31323334353637383930616263646566
- `sm4Iv`：66656463626130393837363534333231

### 2.2 环境说明

- 开发环境：`https://5j71c34d-7331.asse.devtunnels.ms/app`

### 2.3 防重放攻击说明

- 单个请求5秒有效，超过5秒请求将被拒绝

## 3. 接口规范

### 3.1 请求格式

所有请求必须使用POST方法（除登录接口外），Content-Type为`application/json`，请求体格式如下：

```json
{
  "appId": "第三方系统的应用ID",
  "data": "使用SM4加密后的业务数据（Base64编码）",
  "timestamp": "请求时间戳（毫秒）",
  "nonce": "随机字符串（防重放攻击）",
  "signature": "请求签名"
}
```

### 3.2 响应格式

响应体格式如下：

```json
{
  "appId": "第三方系统的应用ID",
  "data": "使用SM4加密后的业务数据（Base64编码）",
  "timestamp": "响应时间戳（毫秒）",
  "nonce": "随机字符串（防重放攻击）",
  "signature": "响应签名"
}
```

#### 响应示例（解密前）

```json
{
  "appId": "test_system",
  "data": "U2FsdGVkX1+8aQyAIgdcVfGXc1CUPGxRnwPKQF/Ks2i+Lp+JKUMzOXkKnrWUmOI9XzR5qGLx6fT8YZ1zRRjJ6QEfVr9rODtKhH8bYPDezHU=",
  "timestamp": "1711509402000",
  "nonce": "a1b2c3d4e5f6",
  "signature": "7e8f9a0b1c2d3e4f5a6b7c8d9e0f1a2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9"
}
```

解密后的业务数据（data字段）格式如下，这是标准的IResult返回格式，所有接口都使用此格式返回数据：

```json
{
  "success": true,
  "message": "操作成功",
  "status": 200,
  "timestamp": 1711509402000,
  "data": "业务数据"
}
```

### 3.3 状态码说明

| 状态码 | 说明 |
| ------ | ---- |
| 0 | 成功 |
| 401 | 未授权（AppID无效） |
| 403 | 禁止访问（签名无效） |
| 408 | 请求超时（时间戳过期） |
| 429 | 请求过于频繁（防重放攻击） |
| 500 | 服务器内部错误 |

### 3.4 错误响应处理

系统对不同类型的错误有不同的响应处理方式：

#### 3.4.1 鉴权类错误（未加密）

当请求存在以下问题时，系统会返回**未加密的**错误响应：

- AppID无效或不存在
- 签名验证失败
- 时间戳过期（超过5分钟）
- 重放攻击（相同nonce重复使用）

错误响应格式示例：

```json
{
  "code": 403,
  "message": "签名验证失败",
  "timestamp": "1711509402000",
  "nonce": "a1b2c3d4e5f6"
}
```

> 注意：鉴权类错误响应中不包含data和signature字段，因为此时无法安全地执行加密操作。

#### 3.4.2 业务逻辑错误（加密）

当请求通过鉴权验证，但在业务处理过程中出错时，系统会返回**加密的**错误响应：

- 请求参数不符合要求
- 业务处理失败
- 权限不足等

错误响应格式示例（加密前）：

```json
{
  "appId": "test_system",
  "data": "使用SM4加密后的错误信息（Base64编码）",
  "timestamp": "1711509402000",
  "nonce": "a1b2c3d4e5f6",
  "signature": "7e8f9a0b1c2d3e4f5a6b7c8d9e0f1a2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9"
}
```

解密后的错误信息格式（data字段解密后）：

```json
{
  "success": false,
  "message": "参数格式不正确",
  "status": 400,
  "timestamp": 1711509402000,
  "data": null
}
```

> 提示：第三方系统在处理响应时，应当先判断响应是否包含data字段，如果包含则需要解密处理；如果不包含，则直接根据code和message处理错误。

## 4. 安全机制

### 4.1 数据加密流程

1. 业务数据序列化为JSON字符串
2. 使用SM4算法和约定的密钥、IV对JSON字符串进行加密，采用CTR模式
3. 将加密后的二进制数据进行Base64编码，得到`data`字段值

CTR模式（计数器模式）是一种流密码模式，具有以下优势：
- 无需填充，输入数据长度等于输出长度
- 允许并行处理，加速加解密过程
- 避免了CBC模式可能受到的填充预言攻击
- 随机访问性强，支持对密文的任意位置解密，无需从头开始

### 4.2 签名计算流程

签名计算公式：`signature = SM3(appId + appSecret + timestamp + nonce + data)`

步骤：
1. 将`appId`、`appSecret`、`timestamp`、`nonce`、`data`按顺序拼接
2. 使用SM3算法计算哈希值
3. 将哈希值转换为十六进制字符串，得到`signature`

### 4.3 请求有效期

为防止重放攻击，请求的时间戳与服务器时间的差值不能超过5分钟，否则请求将被拒绝。

### 4.4 防重放攻击

每个请求必须使用唯一的`nonce`随机字符串，服务器会缓存已处理的`nonce`值，拒绝重复的请求。

### 4.5 无参数接口说明

对于不需要传递业务参数的接口（如获取顶部图片链接接口），在构建请求时：
1. `data`字段应传入空字符串`""`进行加密
2. 签名计算时仍需要包含加密后的空字符串值
3. 请求其他字段（appId、timestamp、nonce、signature）保持不变

## 5. 接口列表

### 5.1 获取Banner图片

- 接口地址：`/openApi/getBannerImg`
- 请求方法：POST
- 请求参数：

```json
{
  "url": "Banner图片URL"
}
```

- 响应参数（解密后）：

```json
{
  "success": true,
  "message": "操作成功",
  "status": 200,
  "timestamp": 1711509402000,
  "data": [
    {
      "id": "Banner ID",
      "title": "Banner标题",
      "imgUrl": "图片URL",
      "linkUrl": "链接URL",
      "sort": 排序值
    }
  ]
}
```

### 5.2 更新用户状态

- 接口地址：`/openApi/updateUser`
- 请求方法：POST
- 请求参数：

```json
{
  "jlToken": "吉络平台的Token",
  "statusInfo": "用户状态信息",
  "userExpirationTime": "用户过期时间（yyyy-MM-dd HH:mm:ss）"
}
```

- 响应参数（解密后）：

```json
{
  "success": true,
  "message": "操作成功",
  "status": 200,
  "timestamp": 1711509402000,
  "data": true
}
```

### 5.3 获取顶部图片链接

- 接口地址：`/openApi/getTopOfLineImgUrls`
- 请求方法：POST
- 请求参数：无
- 响应参数（解密后）：

```json
{
  "success": true,
  "message": "操作成功",
  "status": 200,
  "timestamp": 1711509402000,
  "data": [
    "图片URL1",
    "图片URL2",
    "..."
  ]
}
```

### 5.4 获取流程列表

- 接口地址：`/openApi/secure/myFlowList`
- 请求方法：POST
- 请求参数：

```json
{
  "page": 1,
  "size": 10,
  "processKey": "流程标识",
  "processName": "流程名称",
  "category": "流程分类",
  "status": "状态",
  "userId": "用户ID",
  "params": {},
  "tfMystart": false,
  "startUserId": "发起人ID",
  "moduleName": "模块来源",
  "startDateStart": "2025-01-01 00:00:00",
  "startDateStartEnd": "2025-04-24 23:59:59",
  "receiveDateStart": "2025-01-01 00:00:00",
  "receiveDateEnd": "2025-04-24 23:59:59",
  "queryType": "查询类型",
  "taskName": "任务名称",
  "companyName": "供应商名称",
  "supplierType": "供应商类型",
  "infoTitle": "资讯标题"
}
```

- 响应参数（解密后）：

```json
{
  "success": true,
  "message": "操作成功",
  "status": 200,
  "timestamp": 1711509402000,
  "data": {
    "records": [
      {
        "id": 1,
        "busId": 123,
        "processId": "流程实例ID",
        "taskId": "任务ID",
        "taskName": "任务名称",
        "status": "进行中/已完成",
        "createTimeFlow": "2025-04-20 10:00:00",
        "runNowTaskName": "当前任务名称",
        "approvalUserIdsFlow": ["用户ID"],
        "companyName": "公司名称",
        "supplierType": "供应商类型",
        "startTime": "2025-04-20 10:00:00",
        "endTime": "2025-04-20 10:00:00",
        "statusInfo": 0
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

> 注意：根据 `moduleName` 参数的不同，返回的数据结构会有所差异：
> - `新增车辆信息审批`：返回车辆相关信息
> - `供应商资质审批`：返回供应商相关信息
> - `新增资讯审批`：返回资讯相关信息

### 5.5 用户退出登录

- 接口地址：`/openApi/jlLogout`
- 请求方法：POST
- 请求参数：

```json
{
  "jlToken": "吉络平台的Token"
}
```

- 响应参数（解密后）：

```json
{
  "success": true,
  "message": "操作成功",
  "status": 200,
  "timestamp": 1711509402000,
  "data": null
}
```

## 6. 代码示例

### 6.1 Java示例

```java
import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.Security;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

public class OpenApiClient {
    private static final String ALGORITHM = "SM4/CTR/NoPadding";
    private final String appId;
    private final String appSecret;
    private final byte[] sm4Key;
    private final byte[] sm4Iv;
    
    static {
        // 注册BouncyCastle提供者
        if (Security.getProvider("BC") == null) {
            Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
        }
    }
    
    public OpenApiClient(String appId, String appSecret, String sm4Key, String sm4Iv) {
        this.appId = appId;
        this.appSecret = appSecret;
        this.sm4Key = hexToBytes(sm4Key);
        this.sm4Iv = hexToBytes(sm4Iv);
    }
    
    public String encrypt(String data) throws Exception {
        Cipher cipher = Cipher.getInstance(ALGORITHM, "BC");
        SecretKeySpec keySpec = new SecretKeySpec(sm4Key, "SM4");
        javax.crypto.spec.IvParameterSpec ivSpec = new javax.crypto.spec.IvParameterSpec(sm4Iv);
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
        byte[] encrypted = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encrypted);
    }
    
    public String decrypt(String encryptedData) throws Exception {
        byte[] dataBytes = Base64.getDecoder().decode(encryptedData);
        Cipher cipher = Cipher.getInstance(ALGORITHM, "BC");
        SecretKeySpec keySpec = new SecretKeySpec(sm4Key, "SM4");
        javax.crypto.spec.IvParameterSpec ivSpec = new javax.crypto.spec.IvParameterSpec(sm4Iv);
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
        byte[] decrypted = cipher.doFinal(dataBytes);
        return new String(decrypted, StandardCharsets.UTF_8);
    }
    
    public String calculateSignature(String timestamp, String nonce, String data) throws Exception {
        String content = appId + appSecret + timestamp + nonce + (data == null ? "" : data);
        MessageDigest digest = MessageDigest.getInstance("SM3", "BC");
        byte[] hash = digest.digest(content.getBytes(StandardCharsets.UTF_8));
        return bytesToHex(hash);
    }
    
    public Map<String, Object> createRequest(String businessData) throws Exception {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String nonce = generateNonce();
        String encryptedData = encrypt(businessData);
        String signature = calculateSignature(timestamp, nonce, encryptedData);
        
        Map<String, Object> request = new HashMap<>();
        request.put("appId", appId);
        request.put("data", encryptedData);
        request.put("timestamp", timestamp);
        request.put("nonce", nonce);
        request.put("signature", signature);
        
        return request;
    }
    
    /**
     * 创建无参数接口的请求
     * 便捷方法，用于处理不需要传递业务参数的接口
     */
    public Map<String, Object> createEmptyRequest() throws Exception {
        return createRequest(""); // 传入空字符串
    }
    
    private String generateNonce() {
        return Long.toHexString(System.nanoTime());
    }
    
    private byte[] hexToBytes(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }
    
    private String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }
}
```

### 6.2 请求示例

```java
// 初始化客户端
OpenApiClient client = new OpenApiClient(
    "test_system",
    "test_secret_key",
    "1234567890abcdef1234567890abcdef",
    "1234567890abcdef1234567890abcdef"
);

// 示例1：获取Banner图片
String bannerData = "{\"url\":\"http://example.com/banner\"}";
Map<String, Object> bannerRequest = client.createRequest(bannerData);
// 发送请求到 /openApi/getBannerImg

// 示例2：更新用户状态
String updateUserData = "{\"jlToken\":\"jwt_token_from_jl_platform\",\"statusInfo\":\"DORMANCY\",\"userExpirationTime\":\"2025-04-30 23:59:59\"}";
Map<String, Object> updateUserRequest = client.createRequest(updateUserData);
// 发送请求到 /openApi/updateUser

// 示例3：获取顶部图片链接（不需要参数的接口）
Map<String, Object> topImgRequest = client.createEmptyRequest(); // 使用便捷方法
// 发送请求到 /openApi/getTopOfLineImgUrls

// 示例4：获取流程列表
String flowListData = "{\"page\":1,\"size\":10,\"processKey\":\"\",\"processName\":\"\",\"category\":\"\",\"status\":\"\",\"userId\":\"\",\"params\":{},\"tfMystart\":false,\"startUserId\":\"\",\"moduleName\":\"\",\"startDateStart\":\"\",\"startDateStartEnd\":\"\",\"receiveDateStart\":\"\",\"receiveDateEnd\":\"\",\"queryType\":\"\",\"taskName\":\"\",\"companyName\":\"\",\"supplierType\":\"\",\"infoTitle\":\"\"}";
Map<String, Object> flowListRequest = client.createRequest(flowListData);
// 发送请求到 /openApi/secure/myFlowList

// 示例5：用户退出登录
String logoutData = "{\"jlToken\":\"jwt_token_from_jl_platform\"}";
Map<String, Object> logoutRequest = client.createRequest(logoutData);
// 发送请求到 /openApi/jlLogout
```

### 6.3 完整请求示例（使用HttpClient）

```java
// 以更新用户状态为例
String businessData = "{\"jlToken\":\"jwt_token_from_jl_platform\",\"statusInfo\":\"DORMANCY\",\"userExpirationTime\":\"2025-04-30 23:59:59\"}";

// 创建加密请求
Map<String, Object> request = client.createRequest(businessData);
String requestJson = new ObjectMapper().writeValueAsString(request);

// 发送请求到更新用户状态接口
HttpClient httpClient = HttpClients.createDefault();
HttpPost httpPost = new HttpPost("http://api.example.com/openApi/updateUser");
httpPost.setHeader("Content-Type", "application/json");
httpPost.setEntity(new StringEntity(requestJson, ContentType.APPLICATION_JSON));

// 执行请求
HttpResponse response = httpClient.execute(httpPost);
String responseJson = EntityUtils.toString(response.getEntity());

// 解析响应
JsonNode responseNode = new ObjectMapper().readTree(responseJson);
String encryptedData = responseNode.get("data").asText();

// 解密响应数据
String decryptedData = client.decrypt(encryptedData);
System.out.println("解密后的响应数据: " + decryptedData);

// 解析IResult格式的响应
JsonNode resultNode = new ObjectMapper().readTree(decryptedData);
boolean success = resultNode.get("success").asBoolean();
String message = resultNode.get("message").asText();
int status = resultNode.get("status").asInt();
JsonNode data = resultNode.get("data");

System.out.println("操作是否成功: " + success);
System.out.println("响应消息: " + message);
System.out.println("状态码: " + status);
System.out.println("业务数据: " + data);
```

### 6.4 无参数接口请求示例

```java
// 以获取顶部图片链接为例（无需请求参数）
// 创建加密请求，使用便捷方法
Map<String, Object> request = client.createEmptyRequest();
String requestJson = new ObjectMapper().writeValueAsString(request);

// 发送请求到获取顶部图片链接接口
HttpClient httpClient = HttpClients.createDefault();
HttpPost httpPost = new HttpPost("http://api.example.com/openApi/getTopOfLineImgUrls");
httpPost.setHeader("Content-Type", "application/json");
httpPost.setEntity(new StringEntity(requestJson, ContentType.APPLICATION_JSON));

// 执行请求及后续处理步骤与上例相同
HttpResponse response = httpClient.execute(httpPost);
String responseJson = EntityUtils.toString(response.getEntity());

// 解析响应
JsonNode responseNode = new ObjectMapper().readTree(responseJson);
String encryptedData = responseNode.get("data").asText();

// 解密响应数据
String decryptedData = client.decrypt(encryptedData);

// 解析响应内容（获取图片URL列表）
JsonNode resultNode = new ObjectMapper().readTree(decryptedData);
boolean success = resultNode.get("success").asBoolean();
JsonNode data = resultNode.get("data");
// data是一个URL字符串数组
```

## 7. 常见问题

### 7.1 签名验证失败

可能原因：
- `appId`或`appSecret`不正确
- 请求参数顺序错误
- 时间戳格式错误
- 数据加密错误

### 7.2 解密失败

可能原因：
- SM4密钥或IV不正确
- 加密算法或模式不匹配
- Base64编码错误

### 7.3 请求超时

可能原因：
- 网络延迟
- 服务器负载过高
- 请求时间戳与服务器时间差异过大

### 7.4 无参数接口的处理

问题：调用无参数接口时，请求失败或签名验证不通过。

可能原因：
- `data`字段传入了null值而不是空字符串
- 签名计算时没有包含加密后的空字符串
- 使用了GET请求而不是POST请求

解决方法：
- 对于无参数接口，始终使用空字符串`""`而不是null作为业务数据
- 使用`createEmptyRequest()`方法创建请求
- 确保使用POST方法发送请求

---

文档版本：v1.0
更新日期：2025-03-27
