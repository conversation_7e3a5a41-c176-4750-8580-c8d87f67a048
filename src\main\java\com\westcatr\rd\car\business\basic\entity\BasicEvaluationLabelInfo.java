package com.westcatr.rd.car.business.basic.entity;

import java.io.Serializable;
import java.util.Date;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.web.excel.annotation.ExcelField;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 基本信息—评价标签表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("basic_evaluation_label_info")
@Schema(description = "基本信息—评价标签表")
public class BasicEvaluationLabelInfo extends Model<BasicEvaluationLabelInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "所属模块")
    @ExcelField(name = "所属模块")
    @Length(max = 255, message = "所属模块长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("module_info")
    private String moduleInfo;

    @Schema(description = "标签类型")
    @ExcelField(name = "标签类型")
    @Length(max = 255, message = "标签类型长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("label_type")
    private String labelType;

    @Schema(description = "标签内容")
    @ExcelField(name = "标签内容")
    @Length(max = 255, message = "标签内容长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("label_content")
    private String labelContent;

    @Schema(description = "展示状态（0：不展示、1：展示）")
    @ExcelField(name = "展示状态（0：不展示、1：展示）")
    @TableField("display_status")
    private Integer displayStatus;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
