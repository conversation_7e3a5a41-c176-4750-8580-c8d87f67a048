package com.westcatr.rd.car.business.demand.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.car.business.demand.pojo.query.DemandApplicationPhoneInfoQuery;
import com.westcatr.rd.car.business.demand.entity.DemandApplicationPhoneInfo;
import com.westcatr.rd.car.business.demand.mapper.DemandApplicationPhoneInfoMapper;
import com.westcatr.rd.car.business.demand.service.DemandApplicationPhoneInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 需求—供应商申请获取用户电话信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Service
public class DemandApplicationPhoneInfoServiceImpl extends ServiceImpl<DemandApplicationPhoneInfoMapper, DemandApplicationPhoneInfo> implements DemandApplicationPhoneInfoService {

    @Override
    public IPage<DemandApplicationPhoneInfo> entityPage(DemandApplicationPhoneInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<DemandApplicationPhoneInfo>().create(query));
    }

    @Override
    public DemandApplicationPhoneInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(DemandApplicationPhoneInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(DemandApplicationPhoneInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }
}

