package com.westcatr.rd.car.business.basic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.car.business.basic.pojo.query.BasicComplaintConfigInfoQuery;
import com.westcatr.rd.car.business.basic.entity.BasicComplaintConfigInfo;
import com.westcatr.rd.car.business.basic.mapper.BasicComplaintConfigInfoMapper;
import com.westcatr.rd.car.business.basic.service.BasicComplaintConfigInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 投诉配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Service
public class BasicComplaintConfigInfoServiceImpl extends ServiceImpl<BasicComplaintConfigInfoMapper, BasicComplaintConfigInfo> implements BasicComplaintConfigInfoService {

    @Override
    public IPage<BasicComplaintConfigInfo> entityPage(BasicComplaintConfigInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<BasicComplaintConfigInfo>().create(query));
    }

    @Override
    public BasicComplaintConfigInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(BasicComplaintConfigInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(BasicComplaintConfigInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }
}

