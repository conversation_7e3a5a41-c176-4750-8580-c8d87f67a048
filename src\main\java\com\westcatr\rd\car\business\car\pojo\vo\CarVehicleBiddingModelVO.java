package com.westcatr.rd.car.business.car.pojo.vo;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 竞价车型信息表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "竞价车型信息表VO")
public class CarVehicleBiddingModelVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    @Schema(description = "主键ID")
    private Long id;

    @TableField(exist = false)
    @Schema(description = "车型名称")
    private String modelName;

    @TableField(exist = false)
    @Schema(description = "竞价配件数量")
    private Long biddingPartsCount;

    @TableField(exist = false)
    @Schema(description = "竞价工时数量")
    private Long biddingHoursCount;

    @TableField(exist = false)
    @Schema(description = "创建时间")
    private Date createTime;

    @TableField(exist = false)
    @Schema(description = "更新时间")
    private Date updateTime;
}
