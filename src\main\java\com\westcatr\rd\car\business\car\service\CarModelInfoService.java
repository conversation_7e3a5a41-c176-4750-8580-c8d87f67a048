package com.westcatr.rd.car.business.car.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.car.business.car.entity.CarModelInfo;
import com.westcatr.rd.car.business.car.pojo.dto.CarYearInfoDto;
import com.westcatr.rd.car.business.car.pojo.query.CarModelInfoQuery;
import com.westcatr.rd.car.business.car.pojo.vo.CarModelInfoVO;

/**
 * <p>
 * 车辆—车型信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
public interface CarModelInfoService extends IService<CarModelInfo> {

    IPage<CarModelInfo> entityPage(CarModelInfoQuery query);

    CarModelInfo getEntityById(Long id);

    boolean saveEntity(CarModelInfo param);

    boolean updateEntity(CarModelInfo param);

    boolean removeEntityById(Long id);

    CarModelInfoVO getMyVo(Long id);

    IPage<CarModelInfoVO> getMyVoPage(CarModelInfoQuery query);

    /**
     * 获取某个车辆的所有年款信息
     *
     * @param carId
     * @return
     */
    CarYearInfoDto getCarModelInfoYearModelList(Long carId);

    /**
     * 获取单个（试驾用）
     *
     * @param id
     * @return
     */
    CarModelInfoVO testDriveGetMyVo(Long id);

    /**
     * 获取审核中的车型信息
     *
     * @param query
     * @return
     */
    IPage<CarModelInfoVO> getMyVoPageAuditing(CarModelInfoQuery query);
}
