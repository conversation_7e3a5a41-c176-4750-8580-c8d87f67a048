<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.car.business.car.mapper.CarParameterInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.car.business.car.entity.CarParameterInfo">
        <id column="id" property="id" />
        <result column="car_id" property="carId" />
        <result column="reference_price" property="referencePrice" />
        <result column="discount_info" property="discountInfo" />
        <result column="manufacturer_price" property="manufacturerPrice" />
        <result column="manufacturer" property="manufacturer" />
        <result column="vehicle_level" property="vehicleLevel" />
        <result column="energy_type" property="energyType" />
        <result column="emission_standard" property="emissionStandard" />
        <result column="release_date" property="releaseDate" />
        <result column="wltc_range" property="wltcRange" />
        <result column="cltc_range" property="cltcRange" />
        <result column="fast_charge_time" property="fastChargeTime" />
        <result column="slow_charge_time" property="slowChargeTime" />
        <result column="fast_charge_range" property="fastChargeRange" />
        <result column="max_power" property="maxPower" />
        <result column="max_torque" property="maxTorque" />
        <result column="transmission" property="transmission" />
        <result column="body_structure" property="bodyStructure" />
        <result column="engine" property="engine" />
        <result column="electric_motor_ps" property="electricMotorPs" />
        <result column="dimensions" property="dimensions" />
        <result column="acceleration" property="acceleration" />
        <result column="max_speed" property="maxSpeed" />
        <result column="wltc_fuel_consumption" property="wltcFuelConsumption" />
        <result column="min_charge_state_fuel_consumption" property="minChargeStateFuelConsumption" />
        <result column="warranty" property="warranty" />
        <result column="curb_weight" property="curbWeight" />
        <result column="max_payload" property="maxPayload" />
        <result column="max_towing_mass" property="maxTowingMass" />
        <result column="length" property="length" />
        <result column="width" property="width" />
        <result column="height" property="height" />
        <result column="wheelbase" property="wheelbase" />
        <result column="front_track" property="frontTrack" />
        <result column="rear_track" property="rearTrack" />
        <result column="ground_clearance" property="groundClearance" />
        <result column="approach_angle" property="approachAngle" />
        <result column="departure_angle" property="departureAngle" />
        <result column="ramp_breakover_angle" property="rampBreakoverAngle" />
        <result column="max_climb_angle" property="maxClimbAngle" />
        <result column="turning_radius" property="turningRadius" />
        <result column="max_wading_depth" property="maxWadingDepth" />
        <result column="fuel_tank_capacity" property="fuelTankCapacity" />
        <result column="front_trunk_volume" property="frontTrunkVolume" />
        <result column="rear_trunk_volume" property="rearTrunkVolume" />
        <result column="drag_coefficient" property="dragCoefficient" />
        <result column="engine_model" property="engineModel" />
        <result column="displacement_ml" property="displacementMl" />
        <result column="displacement_l" property="displacementL" />
        <result column="intake_form" property="intakeForm" />
        <result column="engine_layout" property="engineLayout" />
        <result column="cylinder_arrangement" property="cylinderArrangement" />
        <result column="cylinder_count" property="cylinderCount" />
        <result column="valve_per_cylinder" property="valvePerCylinder" />
        <result column="valve_mechanism" property="valveMechanism" />
        <result column="max_horsepower" property="maxHorsepower" />
        <result column="max_rpm" property="maxRpm" />
        <result column="torque_rpm" property="torqueRpm" />
        <result column="fuel_label" property="fuelLabel" />
        <result column="fuel_supply_mode" property="fuelSupplyMode" />
        <result column="battery_capacity" property="batteryCapacity" />
        <result column="battery_density" property="batteryDensity" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, car_id, reference_price, discount_info, manufacturer_price, manufacturer, vehicle_level, energy_type, emission_standard, release_date, wltc_range, cltc_range, fast_charge_time, slow_charge_time, fast_charge_range, max_power, max_torque, transmission, body_structure, engine, electric_motor_ps, dimensions, acceleration, max_speed, wltc_fuel_consumption, min_charge_state_fuel_consumption, warranty, curb_weight, max_payload, max_towing_mass, length, width, height, wheelbase, front_track, rear_track, ground_clearance, approach_angle, departure_angle, ramp_breakover_angle, max_climb_angle, turning_radius, max_wading_depth, fuel_tank_capacity, front_trunk_volume, rear_trunk_volume, drag_coefficient, engine_model, displacement_ml, displacement_l, intake_form, engine_layout, cylinder_arrangement, cylinder_count, valve_per_cylinder, valve_mechanism, max_horsepower, max_rpm, torque_rpm, fuel_label, fuel_supply_mode, battery_capacity, battery_density, create_time, update_time
    </sql>

</mapper>
