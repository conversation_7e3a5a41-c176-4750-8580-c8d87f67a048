package com.westcatr.rd.car.business.car.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.westcatr.rd.boot.core.exception.IRuntimeException;
import com.westcatr.rd.car.business.car.pojo.dto.CarExcelParameterDto;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
public class CarParameterInfoListener extends AnalysisEventListener<CarExcelParameterDto> {

    private final List<CarExcelParameterDto> dataList = new ArrayList<>();

    // 标志位，用于判断是否已经读取了第一条数据
    private boolean firstRowRead = false;

    @Override
    public void invoke(CarExcelParameterDto data, AnalysisContext context) {
        // 如果已经读取了第一条数据，则跳过后续数据
        if (firstRowRead) {
            return;
        }

        // 读取并保存第一条数据
        dataList.add(data);
        firstRowRead = true;
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 如果没有读取到数据
        if (dataList.isEmpty()) {
            throw new IRuntimeException("没有数据！");
        }

        // 输出读取到的第一条数据
        System.out.println("读取到的第一条数据：" + dataList.get(0));

        // 提示用户填写了一条数据即可
        System.out.println("提示：请只填写一条数据，其他数据已被忽略！");
    }
}
