package com.westcatr.rd.car.business.demand.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.car.business.demand.pojo.query.DemandUsedCarApplicationPhoneInfoQuery;
import com.westcatr.rd.car.business.demand.entity.DemandUsedCarApplicationPhoneInfo;
import com.westcatr.rd.car.business.demand.mapper.DemandUsedCarApplicationPhoneInfoMapper;
import com.westcatr.rd.car.business.demand.service.DemandUsedCarApplicationPhoneInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 需求—二手车供应商申请获取用户电话信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Service
public class DemandUsedCarApplicationPhoneInfoServiceImpl extends ServiceImpl<DemandUsedCarApplicationPhoneInfoMapper, DemandUsedCarApplicationPhoneInfo> implements DemandUsedCarApplicationPhoneInfoService {

    @Override
    public IPage<DemandUsedCarApplicationPhoneInfo> entityPage(DemandUsedCarApplicationPhoneInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<DemandUsedCarApplicationPhoneInfo>().create(query));
    }

    @Override
    public DemandUsedCarApplicationPhoneInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(DemandUsedCarApplicationPhoneInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(DemandUsedCarApplicationPhoneInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }
}

