package com.westcatr.rd.car.business.car.pojo.vo;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.car.business.car.entity.CarVehicleModelInfo;
import com.westcatr.rd.car.business.car.entity.CarVehicleModelPartsInfo;
import com.westcatr.rd.car.business.repair.entity.CarVehicleModelLaborHourInfo;
import com.westcatr.rd.car.business.supplier.entity.SupplierInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 车型配件信息表--配件
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "车型配件信息表--配件VO对象")
public class CarVehicleModelPartsInfoVO extends CarVehicleModelPartsInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    @Schema(description = "车型名称")
    @JoinSelect(joinClass = CarVehicleModelInfo.class, mainId = "vehicleModelId", field = "model_name")
    private String modelName;

    // 绑定工时数
    @Schema(description = "绑定工时数")
    @TableField(exist = false)
    private Long biddingHoursCount;

    @TableField(exist = false)
    @JoinSelect(joinClass = CarVehicleModelLaborHourInfo.class, relationId = "parts_id")
    private List<CarVehicleModelLaborHourInfo> carVehicleModelLaborHourInfos;

    // 供应商信息
    @Schema(description = "供应商信息")
    @TableField(exist = false)
    @JoinSelect(joinClass = SupplierInfo.class, mainId = "supplierId", field = "company_name")
    private SupplierInfo supplierInfo;
}
