package com.westcatr.rd.car.business.car.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.boot.sso.util.AuthUtil;
import com.westcatr.rd.car.business.basic.pojo.dto.TypeInfoDto;
import com.westcatr.rd.car.business.basic.service.BasicEvaluationLabelInfoService;
import com.westcatr.rd.car.business.car.entity.CarInfo;
import com.westcatr.rd.car.business.car.entity.CarModelInfo;
import com.westcatr.rd.car.business.car.mapper.CarModelInfoMapper;
import com.westcatr.rd.car.business.car.pojo.dto.CarYearInfoDto;
import com.westcatr.rd.car.business.car.pojo.query.CarModelInfoQuery;
import com.westcatr.rd.car.business.car.pojo.vo.CarInfoVO;
import com.westcatr.rd.car.business.car.pojo.vo.CarModelInfoVO;
import com.westcatr.rd.car.business.car.service.CarInfoService;
import com.westcatr.rd.car.business.car.service.CarModelInfoService;
import com.westcatr.rd.car.business.demand.entity.DemandBrowseInfo;
import com.westcatr.rd.car.business.demand.service.DemandBrowseInfoService;
import com.westcatr.rd.car.business.param.entity.CarParamBasicInfo1;
import com.westcatr.rd.car.business.param.entity.CarParamEngineInfo3;
import com.westcatr.rd.car.business.param.entity.CarParamMotorInfo4;
import com.westcatr.rd.car.business.param.service.CarParamBasicInfo1Service;
import com.westcatr.rd.car.business.param.service.CarParamEngineInfo3Service;
import com.westcatr.rd.car.business.param.service.CarParamMotorInfo4Service;
import com.westcatr.rd.car.business.testdrive.entity.TestDriveEvaluationInfo;
import com.westcatr.rd.car.enums.CarStatusEnum;
import com.westcatr.rd.car.enums.RoleBusEnum;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <p>
 * 车辆—车型信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Service
public class CarModelInfoServiceImpl extends ServiceImpl<CarModelInfoMapper, CarModelInfo>
        implements CarModelInfoService {

    @Autowired
    private DemandBrowseInfoService demandBrowseInfoService;

    @Autowired
    private CarParamBasicInfo1Service carParamBasicInfo1Service;

    @Autowired
    private CarParamEngineInfo3Service carParamEngineInfo3Service;

    @Autowired
    private CarInfoService carInfoService;

    @Autowired
    private BasicEvaluationLabelInfoService basicEvaluationLabelInfoService;

    @Autowired
    private CarParamMotorInfo4Service carParamMotorInfo4Service;

    @Override
    public IPage<CarModelInfo> entityPage(CarModelInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<CarModelInfo>().create(query));
    }

    @Override
    public CarModelInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(CarModelInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(CarModelInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }

    public static List<String> getFormattedList(List<String> a, List<String> b) {
        // 去重 a 的元素
        List<String> uniqueA = new ArrayList<>(a.stream().distinct().toList());

        // 创建统计 map 记录 b 中每个字符串的出现次数
        Map<String, Integer> countMap = new HashMap<>();
        for (String str : b) {
            countMap.put(str, countMap.getOrDefault(str, 0) + 1);
        }

        // 构造结果，带上次数信息
        List<Map.Entry<String, Integer>> resultWithCount = uniqueA.stream()
                .map(str -> Map.entry(str, countMap.getOrDefault(str, 0))).sorted((e1, e2) -> {
                    int compareCount = Integer.compare(e2.getValue(), e1.getValue()); // 按次数降序
                    return compareCount != 0 ? compareCount : e1.getKey().compareTo(e2.getKey()); // 次数相同按字典顺序
                }).toList();

        // 按次数降序排序，次数相同按字符串字典顺序

        // 格式化为字符串列表
        return resultWithCount.stream()
                .map(entry -> entry.getKey() + "(" + entry.getValue() + ")")
                .collect(Collectors.toList());
    }

    private CarModelInfoVO getMyVoTfTestDrive(Long id, Boolean tfTestDrive) {
        AssociationQuery<CarModelInfoVO> associationQuery = new AssociationQuery<>(CarModelInfoVO.class);
        CarModelInfoVO vo = associationQuery.getVo(id);
        CarInfoVO carInfoVO = carInfoService.getMyVo(vo.getCarId());
        // 这里判断如果是用户试驾去获取车辆的话，只去获取可以试驾的车辆
        List<CarModelInfo> carModelInfos = new ArrayList<>();
        if (CollUtil.isNotEmpty(carInfoVO.getCarModelInfos()) && tfTestDrive != null && tfTestDrive) {
            carModelInfos.addAll(carInfoVO.getCarModelInfos().stream().filter(CarModelInfo::getIsTestDrive).toList());
            carInfoVO.setCarModelInfos(carModelInfos);
        }
        if (ObjectUtil.isNotNull(carInfoVO)) {
            vo.setCarInfoByModel(carInfoVO);
        }
        if (CollUtil.isNotEmpty(vo.getTestDriveEvaluationInfos())) {
            TypeInfoDto typeInfoDto = basicEvaluationLabelInfoService.getAll();
            List<TestDriveEvaluationInfo> testDriveEvaluationInfos = vo.getTestDriveEvaluationInfos();
            vo.setTestDriveAdvantages(getFormattedList(typeInfoDto.getCarDatas().getAdvantages(),
                    testDriveEvaluationInfos.stream()
                            .map(TestDriveEvaluationInfo::getAdvantageInfo)
                            .filter(Objects::nonNull)
                            .flatMap(advantage -> Arrays.stream(advantage.split(",")))
                            .map(String::trim)
                            .toList()));
            vo.setTestDriveDisadvantages(getFormattedList(typeInfoDto.getCarDatas().getDisadvantages(),
                    testDriveEvaluationInfos.stream()
                            .map(TestDriveEvaluationInfo::getDisadvantageInfo)
                            .filter(Objects::nonNull)
                            .flatMap(advantage -> Arrays.stream(advantage.split(",")))
                            .map(String::trim)
                            .toList()));
            vo.setTestDriveRating(
                    testDriveEvaluationInfos.stream()
                            .filter(Objects::nonNull)
                            .map(info -> info.getScoreInfo() == null ? 0 : info.getScoreInfo())
                            .mapToDouble(Double::doubleValue)
                            .average()
                            .orElse(0));
        }
        demandBrowseInfoService.saveEntity(new DemandBrowseInfo(
                vo.getCarId(),
                vo.getId(),
                AuthUtil.getUserE().getId()));
        return vo;
    }

    @Override
    public CarModelInfoVO getMyVo(Long id) {
        return getMyVoTfTestDrive(id, null);
    }

    @Override
    public IPage<CarModelInfoVO> getMyVoPage(CarModelInfoQuery query) {
        AssociationQuery<CarModelInfoVO> associationQuery = new AssociationQuery<>(CarModelInfoVO.class);
        // 如果是车辆管理员，那么就看不到草稿中的车型
        if (AuthUtil.getUserE().getRoleIds().contains(RoleBusEnum.CAR_ADMIN.getRoleId())) {
            List<CarInfo> carInfos = carInfoService.list(new LambdaQueryWrapper<>(CarInfo.class)
                    .eq(CarInfo::getStatusInfo, CarStatusEnum.ALREADY_THROUGH.getCode())
                    .eq(CarInfo::getListingStatus, 1));
            if (CollUtil.isNotEmpty(carInfos)) {
                query.setCarIds(carInfos.stream().map(CarInfo::getId).distinct().toList());
            } else {
                query.setId(-1L);
            }
        }
        IPage<CarModelInfoVO> iPage = associationQuery.voPage(query);
        if (CollUtil.isNotEmpty(iPage.getRecords())) {
            iPage.getRecords().forEach(x -> {
                x.setCarInfoByModel(carInfoService.getMyVo(x.getCarId()));
                if (CollUtil.isNotEmpty(x.getTestDriveEvaluationInfos())) {
                    // 设置车辆试驾的平均分
                    x.setTestDriveRating(
                            x.getTestDriveEvaluationInfos().stream()
                                    .filter(Objects::nonNull)
                                    .map(info -> info.getScoreInfo() == null ? 0 : info.getScoreInfo())
                                    .mapToDouble(Double::doubleValue)
                                    .average()
                                    .orElse(0D));
                    x.setTestDriveCount(x.getTestDriveEvaluationInfos().size());
                }
            });
        }
        return iPage;
    }

    @Override
    public CarYearInfoDto getCarModelInfoYearModelList(Long carId) {
        List<CarModelInfo> carModelInfos = this
                .list(new LambdaQueryWrapper<>(CarModelInfo.class).eq(CarModelInfo::getCarId, carId));
        CarYearInfoDto carYearInfoDto = new CarYearInfoDto();
        CarInfo carInfo = carInfoService.getById(carId);
        if (ObjectUtil.isNotNull(carInfo)) {
            carYearInfoDto.setBrandModel(carInfo.getBrandModel());
            carYearInfoDto.setLogoUrl(carInfo.getLogoUrl());
        }
        if (CollUtil.isNotEmpty(carModelInfos)) {
            List<Integer> yearInfos = new ArrayList<>();
            List<String> energyTypes = new ArrayList<>();
            List<String> basicCltcRanges = new ArrayList<>();
            List<String> maxHorsepowerPss = new ArrayList<>();
            List<String> guidePrices = new ArrayList<>();
            List<String> basicCategorys = new ArrayList<>();
            List<Double> basicTopSpeeds = new ArrayList<>();
            carModelInfos.forEach(x -> {
                yearInfos.add(x.getYearModel());
                CarParamBasicInfo1 carParamBasicInfo1 = carParamBasicInfo1Service
                        .getOne(new LambdaQueryWrapper<>(CarParamBasicInfo1.class)
                                .eq(CarParamBasicInfo1::getCarModelId, x.getId())
                                .eq(CarParamBasicInfo1::getCarId, carId).orderByDesc(CarParamBasicInfo1::getCreateTime)
                                .last("limit 1"));
                if (ObjectUtil.isNotNull(carParamBasicInfo1)) {
                    if (StrUtil.isNotBlank(carParamBasicInfo1.getBasicEnergyType())) {
                        energyTypes.add(carParamBasicInfo1.getBasicEnergyType());
                    }
                    if (StrUtil.isNotBlank(carParamBasicInfo1.getBasicCltcRange())) {
                        basicCltcRanges.add(carParamBasicInfo1.getBasicCltcRange());
                    }
                    if (StrUtil.isNotBlank(carParamBasicInfo1.getBasicManufacturerGuidePrice())) {
                        guidePrices.add(carParamBasicInfo1.getBasicManufacturerGuidePrice());
                    }
                    if (StrUtil.isNotBlank(carParamBasicInfo1.getBasicCategory())) {
                        basicCategorys.add(carParamBasicInfo1.getBasicCategory());
                    }
                    if (StrUtil.isNotBlank(carParamBasicInfo1.getBasicTopSpeed())) {
                        basicTopSpeeds.add(Convert.toDouble(carParamBasicInfo1.getBasicTopSpeed(), null));
                    }
                }
                CarParamEngineInfo3 carParamEngineInfo3 = carParamEngineInfo3Service
                        .getOne(new LambdaQueryWrapper<>(CarParamEngineInfo3.class)
                                .eq(CarParamEngineInfo3::getCarModelId, x.getId())
                                .eq(CarParamEngineInfo3::getCarId, carId)
                                .orderByDesc(CarParamEngineInfo3::getCreateTime).last("limit 1"));
                if (ObjectUtil.isNotNull(carParamEngineInfo3)) {
                    maxHorsepowerPss.add(carParamEngineInfo3.getMaxHorsepowerPs());
                }
                CarParamMotorInfo4 carParamMotorInfo4 = carParamMotorInfo4Service
                        .getOne(new LambdaQueryWrapper<>(CarParamMotorInfo4.class)
                                .eq(CarParamMotorInfo4::getCarModelId, x.getId())
                                .eq(CarParamMotorInfo4::getCarId, carId).orderByDesc(CarParamMotorInfo4::getCreateTime)
                                .last("limit 1"));
                if (ObjectUtil.isNotNull(carParamMotorInfo4)) {
                    if (StrUtil.isNotBlank(carParamMotorInfo4.getCltcRangeKm())) {
                        basicCltcRanges.add(carParamMotorInfo4.getCltcRangeKm());
                    }
                }
            });
            // yearinfo去重倒序排列
            carYearInfoDto.setYearInfos(yearInfos.stream().distinct().sorted(Comparator.reverseOrder()).toList());
            // 剩下的都去重连接字符串
            carYearInfoDto.setEnergyType(StrUtil.join(",", energyTypes.stream().distinct().toList()));
            carYearInfoDto.setBasicCltcRange(StrUtil.join(",", basicCltcRanges.stream().distinct().toList()));
            carYearInfoDto.setMaxHorsepowerPs(StrUtil.join(",", maxHorsepowerPss.stream().distinct().toList()));
            carYearInfoDto.setBasicCategory(StrUtil.join(",", basicCategorys.stream().distinct().toList()));
            carYearInfoDto.setGuidePrice(StrUtil.join(",", guidePrices.stream().distinct().toList()));
            if (CollUtil.isNotEmpty(basicTopSpeeds)) {
                carYearInfoDto.setBasicTopSpeed(basicTopSpeeds.stream().max(Double::compareTo).orElse(null));
            }
        }
        return carYearInfoDto;
    }

    @Override
    public CarModelInfoVO testDriveGetMyVo(Long id) {
        return getMyVoTfTestDrive(id, true);
    }

    @Override
    public IPage<CarModelInfoVO> getMyVoPageAuditing(CarModelInfoQuery query) {
        QueryWrapper<CarModelInfoVO> wrapper = new WrapperFactory<CarModelInfoVO>().create(query);
        wrapper.ne("status_info", CarStatusEnum.DRAFT.getCode());
        AssociationQuery<CarModelInfoVO> associationQuery = new AssociationQuery<>(CarModelInfoVO.class);
        IPage<CarModelInfoVO> iPage = associationQuery.voPage(query, wrapper);
        return iPage;
    }
}
