package com.westcatr.rd.car.business.car.pojo.query;

import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import com.westcatr.rd.boot.orm.dto.TimeDTO;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 新增修改车辆参数流程信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="新增修改车辆参数流程信息表查询对象")
public class CarFlowParamDataInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID=1L;

    @QueryCondition
    private Long id;

    @QueryCondition
    private Long carModelId;

    @QueryCondition
    private String flowId;

    @QueryCondition
    private Long createUserId;

    @Schema(description = "业务数据json")
    @QueryCondition
    private String busJson;

    @QueryCondition
    private Date createTime;

    @QueryCondition
    private Date updateTime;
}
