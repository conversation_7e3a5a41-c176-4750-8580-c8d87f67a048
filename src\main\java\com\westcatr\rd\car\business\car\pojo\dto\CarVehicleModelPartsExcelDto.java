package com.westcatr.rd.car.business.car.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 车型配件信息表--配件 Excel DTO
 *
 * <AUTHOR>
 */
@Data
public class CarVehicleModelPartsExcelDto {

    @Schema(description = "车型名称")
    @ColumnWidth(30)
    @ExcelProperty(value = "车型名称", index = 0)
    private String modelName;

    @Schema(description = "车辆级别")
    @ColumnWidth(30)
    @ExcelProperty(value = "车辆级别", index = 1)
    private String vehicleClass;

    @Schema(description = "VIN码")
    @ColumnWidth(30)
    @ExcelProperty(value = "VIN码", index = 2)
    private String vinCode;

    @Schema(description = "车型编码")
    @ColumnWidth(30)
    @ExcelProperty(value = "车型编码", index = 3)
    private String modelCode;

    @Schema(description = "子组编码")
    @ColumnWidth(30)
    @ExcelProperty(value = "子组编码", index = 4)
    private String subGroupCode;

    @Schema(description = "子组名称")
    @ColumnWidth(30)
    @ExcelProperty(value = "子组名称", index = 5)
    private String subGroupName;

    @Schema(description = "类别")
    @ColumnWidth(30)
    @ExcelProperty(value = "类别", index = 6)
    private String typeInfo;

    @Schema(description = "配件编码")
    @ColumnWidth(30)
    @ExcelProperty(value = "配件编码", index = 7)
    private String partCode;

    @Schema(description = "配件名称")
    @ColumnWidth(30)
    @ExcelProperty(value = "配件名称", index = 8)
    private String partName;

    @Schema(description = "原厂价格")
    @ColumnWidth(20)
    @ExcelProperty(value = "原厂价格", index = 9)
    private Double originalPrice;

    @Schema(description = "4S店价格")
    @ColumnWidth(20)
    @ExcelProperty(value = "4S店价格", index = 10)
    private Double dealerPrice;

    @Schema(description = "备注")
    @ColumnWidth(40)
    @ExcelProperty(value = "备注", index = 11)
    private String remarks;
}