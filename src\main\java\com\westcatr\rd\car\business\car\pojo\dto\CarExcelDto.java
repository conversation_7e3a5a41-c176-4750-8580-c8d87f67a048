package com.westcatr.rd.car.business.car.pojo.dto;

import org.hibernate.validator.constraints.Length;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.westcatr.rd.car.common.easyexcel.ExcelSelected;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CarExcelDto {

    @ColumnWidth(30)
    @ExcelProperty(value = "品牌车型", index = 0)
    private String carBrandModel;

    @Schema(description = "车辆简介")
    @ColumnWidth(40)
    @ExcelProperty(value = "车辆简介", index = 1)
    @Length(max = 2000, message = "车辆简介长度不能超过2000")
    private String introduction;

    @ColumnWidth(10)
    @ExcelProperty(value = "年款", index = 2)
    private Integer yearModel;

    @ColumnWidth(30)
    @ExcelProperty(value = "配置名称", index = 3)
    private String configName;

    @ColumnWidth(10)
    @ExcelSelected(source = { "是", "否" })
    @ExcelProperty(value = "是否支持试驾", index = 4)
    private String testDrive;

    @Schema(description = "车型价格")
    @ColumnWidth(20)
    @ExcelProperty(value = "价格（万元）", index = 5)
    private Double price;
}
