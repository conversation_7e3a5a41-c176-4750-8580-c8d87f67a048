package com.westcatr.rd.car.business.audit.pojo.vo;

import java.util.HashMap;
import java.util.Map;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author: Roo
 * @Date: 2025-03-31
 * @Detail: 集骆系统日志请求VO
 */
@Data
public class JiLuoSysLogRequestVO {

    /** 用户ID */
    @Schema(description = "用户ID不能为空")
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /** 用户名称 */
    @Schema(description = "用户名称不能为空")
    @NotBlank(message = "用户名称不能为空")
    private String userName;

    /** IP地址 */
    @Schema(description = "IP地址不能为空")
    @NotBlank(message = "IP地址不能为空")
    private String ip;

    /** 操作类型 */
    @Schema(description = "操作类型不能为空")
    @NotBlank(message = "操作类型不能为空")
    private String operateType;

    /** 日志类型 */
    @Schema(description = "日志类型不能为空")
    @NotBlank(message = "日志类型不能为空")
    private String logType;

    /** 日志描述 */
    @Schema(description = "日志描述不能为空")
    @NotBlank(message = "日志描述不能为空")
    private String logDesc;

    /** 结果 */
    @Schema(description = "结果 (0/失败 1/成功) ")
    @NotNull(message = "结果不能为空")
    private Integer result;

    /** 请求参数 */
    private String params;

    /** 类名 */
    private String clazz;

    /** 操作的方法名 */
    private String method;

    /**
     * 转换为Map对象
     */
    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("userId", userId);
        map.put("userName", userName);
        map.put("ip", ip);
        map.put("operateType", operateType);
        map.put("logType", logType);
        map.put("logDesc", logDesc);
        map.put("result", result);
        map.put("params", params);
        map.put("clazz", clazz);
        map.put("method", method);
        return map;
    }
}