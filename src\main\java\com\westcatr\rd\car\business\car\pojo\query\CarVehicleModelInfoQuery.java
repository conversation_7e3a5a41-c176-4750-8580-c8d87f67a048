package com.westcatr.rd.car.business.car.pojo.query;

import java.io.Serializable;
import java.util.Date;

import com.westcatr.rd.boot.orm.dto.TimeDTO;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition.Condition;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 车型信息表—配件
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "车型信息表—配件查询对象")
public class CarVehicleModelInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @QueryCondition
    private Long id;

    @Schema(description = "VIN码")
    @QueryCondition(condition = Condition.LIKE)
    private String vin;

    @Schema(description = "车型编码")
    @QueryCondition(condition = Condition.LIKE)
    private String modelCode;

    @Schema(description = "车型名称")
    @QueryCondition(condition = Condition.LIKE)
    private String modelName;

    @Schema(description = "车身类型")
    @QueryCondition
    private String bodyType;

    @Schema(description = "发动机类型")
    @QueryCondition
    private String engineType;

    @Schema(description = "发动机排量")
    @QueryCondition
    private String engineDisplacement;

    @Schema(description = "变速器类型")
    @QueryCondition
    private String transmissionType;

    @Schema(description = "燃料类型")
    @QueryCondition
    private String fuelType;

    @Schema(description = "创建时间")
    @QueryCondition
    private Date createTime;

    @Schema(description = "更新时间")
    @QueryCondition
    private Date updateTime;
}
