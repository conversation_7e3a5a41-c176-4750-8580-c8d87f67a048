package com.westcatr.rd.car.business.car.excel;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.westcatr.rd.boot.core.exception.IRuntimeException;
import com.westcatr.rd.car.business.car.pojo.dto.CarExcelDto;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class CarInfoListener extends AnalysisEventListener<CarExcelDto> {

    private final List<CarExcelDto> dataList = new ArrayList<>();

    @Override
    public void invoke(CarExcelDto data, AnalysisContext context) {
        // 每解析一行数据会调用一次
        if (data.getCarBrandModel() == null) {
            throw new IRuntimeException("第" + context.getCurrentRowNum() + "行【品牌车型】为空");
        }
        if (data.getYearModel() == null) {
            throw new IRuntimeException("第" + context.getCurrentRowNum() + "行【年款】字段为空");
        }
        if (data.getConfigName() == null) {
            throw new IRuntimeException("第" + context.getCurrentRowNum() + "行【配置名称】字段为空");
        }
        // 验证车辆简介
        if (data.getIntroduction() != null && data.getIntroduction().length() > 2000) {
            throw new IRuntimeException("第" + context.getCurrentRowNum() + "行【车辆简介】字段长度不能超过2000");
        }
        dataList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // carInfos = BeanUtil.copyToList(dataList, CarInfo.class);
        // 所有数据解析完成后调用
        System.out.println("数据解析完成，总行数：" + dataList.size());
    }

}
