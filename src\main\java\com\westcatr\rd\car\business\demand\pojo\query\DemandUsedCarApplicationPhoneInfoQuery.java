package com.westcatr.rd.car.business.demand.pojo.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.westcatr.rd.boot.orm.association.annotation.JoinExpression;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "二手车需求-供应商申请获取用户电话信息表查询对象")
public class DemandUsedCarApplicationPhoneInfoQuery extends PageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @QueryCondition
    private Long demandId;

    @QueryCondition(field = "demand_used_car_application_phone_info.supplier_id")
    private Long supplierId;

    @Schema(description = "标题")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String titleInfo;

    @QueryCondition
    private String remarks;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "开始时间")
    @QueryCondition(condition = QueryCondition.Condition.GE, field = "demand_used_car_application_phone_info.create_time")
    private Date beginTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "结束时间")
    @QueryCondition(condition = QueryCondition.Condition.LE, field = "demand_used_car_application_phone_info.create_time")
    private Date endTime;

    @Schema(description = "时间排序，1倒叙，0正序")
    private Integer timeSort;

    @Schema(description = "id排序，1倒叙，0正序")
    private Integer idSort;

    @JoinExpression(value = "join demand_used_car_info di on di.id=@m.demand_id")
    @QueryCondition(condition = QueryCondition.Condition.EQ, field = "di.create_user_id")
    private Long createUserId;

    @Schema(description = "需求标题")
    @JoinExpression(value = "join demand_used_car_info duci on duci.id=@m.demand_id", allJoin = true)
    private Boolean joinDemandTable = true;

    @Schema(description = "关键字查询")
    private String keyword;
}
