package com.westcatr.rd.car.business.car.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.core.exception.IRuntimeException;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.service.BaseDao;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.car.business.car.entity.CarParameterInfo;
import com.westcatr.rd.car.business.car.excel.CarParameterInfoListener;
import com.westcatr.rd.car.business.car.mapper.CarParameterInfoMapper;
import com.westcatr.rd.car.business.car.pojo.dto.CarExcelParameterDto;
import com.westcatr.rd.car.business.car.pojo.query.CarParameterInfoQuery;
import com.westcatr.rd.car.business.car.service.CarParameterInfoService;
import com.westcatr.rd.car.common.ExcelResponseHelper;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 车辆—参数信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Service
public class CarParameterInfoServiceImpl extends ServiceImpl<CarParameterInfoMapper, CarParameterInfo>
        implements CarParameterInfoService {

    private static final String FIELD_NAME_KEY = "name";
    private static final String FIELD_VALUE_KEY = "value";

    private static final String ENTITY_PACKAGE = "com.westcatr.rd.car.business.param.entity";

    @Data
    public static class VerificationParameter {
        private String key;
        private String keyCnName;
        private String keyModuleName;

        // 构造函数
        public VerificationParameter(String key, String keyCnName, String keyModuleName) {
            this.key = key;
            this.keyCnName = keyCnName;
            this.keyModuleName = keyModuleName;
        }
    }

    private static final List<VerificationParameter> VERIFICATION_PARAM = List.of(
            new VerificationParameter("basicCategory", "车辆级别", "车辆参数--基本信息"),
            new VerificationParameter("basicEnergyType", "能源类型", "车辆参数--基本信息"),
            new VerificationParameter("basicManufacturerGuidePrice", "厂商指导价（万元）", "车辆参数--基本信息"),
            new VerificationParameter("basicCltcRange", "CLTC纯电续航里程(km)", "车辆参数--基本信息"),
            new VerificationParameter("basicTopSpeed", "最高车速(km/h)", "车辆参数--基本信息"),
            new VerificationParameter("energyType", "能源类型", "车辆参数--发动机参数"),
            new VerificationParameter("maxHorsepowerPs", "最大马力(Ps)", "车辆参数--发动机参数"));

    /**
     * 检验车辆参数那些必传入的参数
     *
     * @param data
     * @param list
     * @return
     */
    public static void validateMap(Map<String, Object> data, List<VerificationParameter> list) {
        // 遍历 list 中的每一个 T 对象
        for (VerificationParameter item : list) {
            // 检查 data 中是否包含 key
            if (!data.containsKey(item.getKey())) {
                throw new IRuntimeException(
                        "模块【" + item.getKeyModuleName() + "】，的参数【" + item.getKeyCnName() + "】为空，请检查！");
            }

            // 检查对应的值是否为空
            if (data.get(item.getKey()) == null || data.get(item.getKey()).toString().isEmpty()) {
                System.out.println("模块【" + item.getKeyModuleName() + "】，的参数【" + item.getKeyCnName() + "】的值为空，请检查！");
            }
        }
    }

    @Autowired
    private BaseDao baseDao;

    @Override
    public IPage<CarParameterInfo> entityPage(CarParameterInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<CarParameterInfo>().create(query));
    }

    @Override
    public CarParameterInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(CarParameterInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(CarParameterInfo param) {
        return this.saveOrUpdate(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }

    @Override
    public void exportTemplate() {
        HttpServletResponse httpServletResponse = ExcelResponseHelper.getExcelResponse("车辆参数信息模板");
        try {
            // 使用 EasyExcel 写入数据到响应流
            EasyExcel.write(httpServletResponse.getOutputStream(), CarExcelParameterDto.class)
                    .sheet("车辆参数信息")
                    .doWrite(Collections.emptyList());
        } catch (IOException e) {
            throw new IRuntimeException("导出模板失败");
        }
    }

    @Override
    public CarParameterInfo importExcel(Long carId, Long carModelId, MultipartFile file) throws IOException {
        if (file.isEmpty()) {
            throw new IRuntimeException("文件不能为空！");
        }
        // 创建监听器实例
        CarParameterInfoListener listener = new CarParameterInfoListener();
        // 解析文件
        EasyExcel.read(file.getInputStream(), CarExcelParameterDto.class, listener).sheet().doRead();
        // 获取解析后的数据
        CarExcelParameterDto firstRow = listener.getDataList().get(0);
        CarParameterInfo carParameterInfo = this.getOne(new LambdaQueryWrapper<>(CarParameterInfo.class)
                .eq(CarParameterInfo::getCarId, carId).eq(CarParameterInfo::getCarId, carModelId).last("limit 1"));
        if (ObjectUtil.isNull(carParameterInfo)) {
            carParameterInfo = new CarParameterInfo();
            carParameterInfo.setCarId(carId);
            carParameterInfo.setCarModelId(carModelId);
        }
        BeanUtil.copyProperties(firstRow, carParameterInfo);
        return carParameterInfo;
    }

    @Override
    public Map<String, Map<String, String>> collectBusinessDataSorted(Long carModelId) throws Exception {
        Map<String, Map<String, String>> result = new LinkedHashMap<>();
        List<Class<?>> entityClasses = getAllEntities();

        // 排序实体类（假设实体类名中包含数字，按数字升序排序）
        entityClasses.sort(Comparator.comparingInt(e -> extractNumberFromClassName(e.getSimpleName())));
        for (Class<?> entityClass : entityClasses) {
            try {
                QueryWrapper<?> queryWrapper = createQueryWrapper(entityClass, carModelId);
                Object businessData = baseDao.selectOne(entityClass, queryWrapper);
                if (ObjectUtil.isNotNull(businessData)) {
                    Map<String, Map<String, String>> filteredData = filterFieldsWithDescription(businessData);
                    result.putAll(filteredData);
                } else {
                    Field[] fields = entityClass.getDeclaredFields();
                    for (Field field : fields) {
                        // 跳过不需要的字段
                        if (isExcludedField(field)) {
                            continue;
                        }
                        // 获取 @Schema 注解的 description 和 @ExcelField 注解的 name
                        Schema schemaAnnotation = field.getAnnotation(Schema.class);
                        String description = schemaAnnotation != null ? schemaAnnotation.description() : "";
                        String fieldName = field.getName();
                        Map<String, String> chineseName = Map.of("name", description);
                        Map<String, Map<String, String>> oneFiled = Map.of(fieldName, chineseName);
                        result.putAll(oneFiled);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 过滤字段
     */
    public Map<String, Map<String, String>> filterFieldsWithDescription(Object businessData) {
        Map<String, Map<String, String>> result = new LinkedHashMap<>();
        Field[] fields = businessData.getClass().getDeclaredFields();

        for (Field field : fields) {
            // 跳过不需要的字段
            if (isExcludedField(field)) {
                continue;
            }
            try {
                String fieldName = field.getName();
                Object fieldValue = BeanUtil.getFieldValue(businessData, fieldName);
                // 获取字段上的 @Schema 注解
                Schema schemaAnnotation = field.getAnnotation(Schema.class);
                String description = schemaAnnotation != null ? schemaAnnotation.description() : fieldName;

                // 创建内层的 Map
                Map<String, String> fieldInfo = createFieldInfo(description, Objects.toString(fieldValue, null));
                result.put(fieldName, fieldInfo);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return result;
    }

    private Map<String, String> createFieldInfo(String name, String value) {
        Map<String, String> fieldInfo = new LinkedHashMap<>();
        fieldInfo.put(FIELD_NAME_KEY, name);
        fieldInfo.put(FIELD_VALUE_KEY, value);
        return fieldInfo;
    }

    @Transactional
    @Override
    public Boolean processEntities(Map<String, Object> data) throws Exception {
        if (!data.containsKey("carId") || !data.containsKey("carModelId")) {
            throw new IRuntimeException("车辆和车型id必须传入");
        }
        // validateMap(data, VERIFICATION_PARAM);
        // 从传入的数据中获取 carId 和 carModelId
        Object carId = data.get("carId");
        Object carModelId = data.get("carModelId");

        // 获取包下所有实体类
        List<Class<?>> entityClasses = getAllEntities();

        // 标记所有操作是否成功
        boolean allSuccessful = true;

        for (Class<?> entityClass : entityClasses) {
            // 实例化实体对象
            Object entity = entityClass.getDeclaredConstructor().newInstance();

            // 给实体动态赋值
            boolean hasValidField = assignValuesToEntity(entity, data);

            // 如果没有有效字段被赋值，跳过当前实体
            if (!hasValidField) {
                continue;
            }

            // 给实体设置 carId 和 carModelId
            setFieldValue(entity, "carId", carId);
            setFieldValue(entity, "carModelId", carModelId);
            // 获取对应的 Service Bean
            QueryWrapper<?> queryWrapper = createQueryWrapper(entityClass, carModelId);
            // 如果存在，设置 ID
            Object existingEntity = baseDao.selectOne(entityClass, queryWrapper);
            int countResult = 0;
            if (ObjectUtil.isNotNull(existingEntity)) {
                Object id = getIdValue(existingEntity);
                setFieldValue(entity, "id", id);
                countResult = baseDao.updateById(entity);
            } else {
                countResult = baseDao.insert(entity);
            }
            allSuccessful = countResult > 0;
        }
        return allSuccessful;
    }

    @Override
    public void exportExcelAllTable(ByteArrayOutputStream byteArrayOutputStream) throws Exception {
        List<Class<?>> entityClasses = getAllEntities();
        // 排序实体类（假设实体类名中包含数字，按数字升序排序）
        entityClasses.sort(Comparator.comparingInt(e -> extractNumberFromClassName(e.getSimpleName())));

        // 2. 创建Excel工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("数据");

        // 3. 创建表头并设置背景颜色
        Row headerRow = sheet.createRow(0);
        CellStyle headerCellStyle = workbook.createCellStyle();
        headerCellStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        headerCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        headerRow.createCell(0).setCellValue("字段定义key(请勿删除)");
        headerRow.getCell(0).setCellStyle(headerCellStyle);

        headerRow.createCell(1).setCellValue("参数");
        headerRow.getCell(1).setCellStyle(headerCellStyle);

        headerRow.createCell(2).setCellValue("数据");
        headerRow.getCell(2).setCellStyle(headerCellStyle);

        int rowIndex = 1;

        for (Class<?> entityClass : entityClasses) {
            // 4. 为每个实体类增加"参数"行
            Row parameterRow = sheet.createRow(rowIndex++);
            parameterRow.createCell(0).setCellValue("参数");

            // 获取 @Schema 注解的参数说明
            String paramDescription = getParamDescription(entityClass);
            Cell paramCell = parameterRow.createCell(1);
            paramCell.setCellValue(paramDescription);

            // 设置加粗样式
            CellStyle paramCellStyle = workbook.createCellStyle();
            Font paramFont = workbook.createFont();
            paramFont.setBold(true); // 加粗
            paramCellStyle.setFont(paramFont);
            paramCell.setCellStyle(paramCellStyle);

            parameterRow.createCell(2).setCellValue(""); // 空数据列

            // 获取所有字段并添加到表格
            Field[] fields = entityClass.getDeclaredFields();
            for (Field field : fields) {
                // 跳过不需要的字段
                if (isExcludedField(field)) {
                    continue;
                }

                // 获取 @Schema 注解的 description 和 @ExcelField 注解的 name
                Schema schemaAnnotation = field.getAnnotation(Schema.class);
                String description = schemaAnnotation != null ? schemaAnnotation.description() : "";
                String fieldName = field.getName();

                // 添加字段定义key (字段名)
                Row row = sheet.createRow(rowIndex++);
                row.createCell(0).setCellValue(fieldName);

                // 参数为 @Schema 的 description
                row.createCell(1).setCellValue(description);

                // 数据为空，设置为空字符串
                row.createCell(2).setCellValue("");
            }
        }

        // 5. 设置列宽度为动态宽度
        for (int i = 0; i < 3; i++) {
            sheet.autoSizeColumn(i);
        }

        // 将工作簿写入到 ByteArrayOutputStream
        workbook.write(byteArrayOutputStream);
        workbook.close();
    }

    @Override
    public Map<String, String> findEntitiesByDescription(String searchTerm) throws Exception {
        Map<String, String> result = new HashMap<>();

        // 获取所有实体类
        List<Class<?>> entityClasses = getAllEntities();

        for (Class<?> entityClass : entityClasses) {
            // 获取类上的 @Schema 注解
            Schema classSchema = entityClass.getAnnotation(Schema.class);
            if (classSchema != null && classSchema.description().contains(searchTerm)) {
                // 获取实体类的所有字段
                Field[] fields = entityClass.getDeclaredFields();

                for (Field field : fields) {
                    // 排除不需要的字段
                    if (isExcludedField(field)) {
                        continue;
                    }

                    // 获取字段的 @Schema 注解
                    Schema fieldSchema = field.getAnnotation(Schema.class);
                    if (fieldSchema != null) {
                        String description = fieldSchema.description();
                        // 将字段名和字段描述添加到 Map
                        result.put(field.getName(), description);
                    }
                }
            }
        }

        return result;
    }

    @Override
    public Map<String, Map<String, String>> importExcel(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream()) {
            Workbook workbook = new XSSFWorkbook(inputStream);
            Sheet sheet = workbook.getSheetAt(0);

            Map<String, Map<String, String>> resultMap = new LinkedHashMap<>();

            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);

                if (row == null) {
                    continue;
                }

                Cell keyCell = row.getCell(0);
                Cell paramCell = row.getCell(1);
                Cell dataCell = row.getCell(2);

                if (dataCell == null
                        || (dataCell.getCellType() == CellType.STRING && dataCell.getStringCellValue().isEmpty())) {
                    continue;
                }

                String key = (keyCell != null) ? keyCell.getStringCellValue() : "";
                String name = (paramCell != null) ? paramCell.getStringCellValue() : "";
                String value = getCellValue(dataCell);

                Map<String, String> fieldInfo = new LinkedHashMap<>();
                fieldInfo.put("name", name);
                fieldInfo.put("value", value);
                if (!"参数".equals(key)) {
                    resultMap.put(key, fieldInfo);
                }
            }

            return resultMap;
        }
    }

    @Transactional
    @Override
    public void removeBatchByIdsAllTable(Long carId, Long carModelId) throws Exception {
        // 获取包下所有实体类
        List<Class<?>> entityClasses = getAllEntities();
        for (Class<?> entityClass : entityClasses) {
            if (entityClass.isAnnotationPresent(TableName.class)) {
                // 获取注解
                TableName tableNameAnnotation = entityClass.getAnnotation(TableName.class);
                // 返回注解的值
                if (StrUtil.isNotEmpty(tableNameAnnotation.value())) {
                    Map<String, Object> dataMap = new HashMap<>();
                    dataMap.put("car_id", carId);
                    dataMap.put("car_model_id", carModelId);
                    baseDao.delete(tableNameAnnotation.value(), dataMap);
                }
            }
        }
    }

    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case NUMERIC:
                // 判断是否是整数
                if (isInteger(cell.getNumericCellValue())) {
                    return String.valueOf((long) cell.getNumericCellValue()); // 转换为long类型，去掉.0
                } else {
                    return String.valueOf(cell.getNumericCellValue()); // 处理小数
                }
            case STRING:
                return cell.getStringCellValue();
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                // 处理公式，获取公式计算后的值
                return String.valueOf(cell.getNumericCellValue()); // 这里也可以根据需要返回其他类型
            default:
                return "";
        }
    }

    // 判断是否为整数
    private boolean isInteger(double value) {
        return value == Math.floor(value); // 如果数字等于它的向下取整值，则为整数
    }

    // 提取类名中的数字部分（假设实体类名带有数字）
    private int extractNumberFromClassName(String className) {
        String numberStr = className.replaceAll("\\D+", ""); // 提取数字
        return numberStr.isEmpty() ? Integer.MAX_VALUE : Integer.parseInt(numberStr);
    }

    // 判断是否为不需要的字段
    private boolean isExcludedField(Field field) {
        String[] excludedFields = { "id", "carId", "carModelId", "createTime", "updateTime", "serialVersionUID" };
        for (String excludedField : excludedFields) {
            if (field.getName().equals(excludedField)) {
                return true;
            }
        }
        return false;
    }

    // 获取实体类中的参数描述（@Schema(description)）
    private String getParamDescription(Class<?> entityClass) {
        Schema schema = entityClass.getAnnotation(Schema.class);
        return schema != null ? schema.description() : "";
    }

    private List<Class<?>> getAllEntities() throws Exception {
        // 创建 ClassPathScanningCandidateComponentProvider 实例
        ClassPathScanningCandidateComponentProvider provider = new ClassPathScanningCandidateComponentProvider(false);

        // 设置扫描的过滤器，假设我们只扫描带有某个注解的类，可以根据实际需要调整
        // 扫描有 @Component 注解的类
        provider.addIncludeFilter(new AnnotationTypeFilter(TableName.class));
        // 扫描指定包路径下的所有类
        List<Class<?>> classes = new ArrayList<>();
        var resources = provider.findCandidateComponents(ENTITY_PACKAGE);

        // 通过资源加载类
        for (var resource : resources) {
            try {
                classes.add(Class.forName(resource.getBeanClassName()));
            } catch (ClassNotFoundException e) {
                // 处理类加载异常
                e.printStackTrace();
            }
        }

        return classes;
    }

    /**
     * 将 Map 中的数据填充到实体对象中.
     *
     * @param entity 目标实体对象
     * @param data   数据 Map
     * @param <T>    实体类型
     * @return 如果数据被成功填充，则返回 true,否则返回 false.
     */
    public <T> boolean assignValuesToEntity(T entity, Map<String, Object> data) {
        if (entity == null || data == null || data.isEmpty()) {
            return false; // 数据为空或实体为空
        }

        // 获取实体所有字段的值的字符串表示
        String before = getEntityFieldValueString(entity);

        // 使用 BeanUtil 填充实体
        BeanUtil.fillBeanWithMap(data, entity, true);

        String after = getEntityFieldValueString(entity);
        // 如果填充前后值发生变化，则返回 true
        return !Objects.equals(before, after);
    }

    /**
     * 获取实体对象所有字段值的字符串表示
     */
    public <T> String getEntityFieldValueString(T entity) {
        if (entity == null) {
            return "";
        }
        Field[] fields = entity.getClass().getDeclaredFields();
        StringJoiner joiner = new StringJoiner(",");
        Arrays.stream(fields).forEach(field -> {
            try {
                String fieldName = field.getName();
                Object fieldValue = BeanUtil.getFieldValue(entity, fieldName);
                joiner.add(Objects.toString(fieldValue, ""));
            } catch (Exception e) {
                log.error("Error accessing field " + field.getName(), e); // 记录详细异常日志
            }
        });
        return joiner.toString();
    }

    private void setFieldValue(Object entity, String fieldName, Object value) {
        try {
            // 使用 BeanUtil 注入值
            BeanUtil.setProperty(entity, fieldName, value);
        } catch (Exception e) {
            // 捕获并打印异常
            e.printStackTrace();
        }
    }

    /**
     * 获取实体对象的 id 字段的值
     *
     * @param entity 目标实体对象
     * @param <T>    实体类型
     * @return id 字段的值,如果实体为 null 或找不到 getId 方法, 则返回 null
     */
    public <T> Object getIdValue(T entity) {
        if (entity == null) {
            return null; // 如果实体为空，则直接返回 null
        }

        try {
            Method method = entity.getClass().getMethod("getId");
            return method.invoke(entity);
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            return null;
        }
    }

    private QueryWrapper<?> createQueryWrapper(Class<?> entityClass, Object carModelId) {
        QueryWrapper<?> queryWrapper = new QueryWrapper<>();
        try {
            // 使用反射获取字段值并设置查询条件
            queryWrapper.eq("car_model_id", carModelId);
            queryWrapper.orderByDesc("create_time");
            // 限制结果为 1
            queryWrapper.last("LIMIT 1");
        } catch (Exception e) {
            throw new RuntimeException("Error creating query wrapper for " + entityClass.getName(), e);
        }
        return queryWrapper;
    }
}
