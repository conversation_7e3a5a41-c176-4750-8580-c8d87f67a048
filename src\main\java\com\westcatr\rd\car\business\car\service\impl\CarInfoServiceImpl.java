package com.westcatr.rd.car.business.car.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.core.domain.IUser;
import com.westcatr.rd.boot.core.exception.IRuntimeException;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.service.JoinInfoService;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.boot.sso.util.AuthUtil;
import com.westcatr.rd.car.business.car.entity.CarBasicInfo;
import com.westcatr.rd.car.business.car.entity.CarDisplayInfo;
import com.westcatr.rd.car.business.car.entity.CarFlowDataInfo;
import com.westcatr.rd.car.business.car.entity.CarFlowParamDataInfo;
import com.westcatr.rd.car.business.car.entity.CarInfo;
import com.westcatr.rd.car.business.car.entity.CarModelInfo;
import com.westcatr.rd.car.business.car.excel.CarInfoListener;
import com.westcatr.rd.car.business.car.mapper.CarInfoMapper;
import com.westcatr.rd.car.business.car.pojo.dto.CarExcelDto;
import com.westcatr.rd.car.business.car.pojo.dto.SaveOrUpdateReturnInfoDto;
import com.westcatr.rd.car.business.car.pojo.query.CarInfoQuery;
import com.westcatr.rd.car.business.car.pojo.query.CarModelInfoQuery;
import com.westcatr.rd.car.business.car.pojo.vo.CarInfoVO;
import com.westcatr.rd.car.business.car.service.CarBasicInfoService;
import com.westcatr.rd.car.business.car.service.CarDisplayInfoService;
import com.westcatr.rd.car.business.car.service.CarFlowDataInfoService;
import com.westcatr.rd.car.business.car.service.CarFlowParamDataInfoService;
import com.westcatr.rd.car.business.car.service.CarInfoService;
import com.westcatr.rd.car.business.car.service.CarModelInfoService;
import com.westcatr.rd.car.business.car.service.CarParameterInfoService;
import com.westcatr.rd.car.business.demand.entity.DemandBrowseInfo;
import com.westcatr.rd.car.business.demand.service.DemandBrowseInfoService;
import com.westcatr.rd.car.business.flow.pojo.dto.CompleteTaskDto;
import com.westcatr.rd.car.business.flow.service.FlowTaskInfoService;
import com.westcatr.rd.car.business.myflow.entity.CarFlowTaskInfo;
import com.westcatr.rd.car.business.myflow.service.CarFlowTaskInfoService;
import com.westcatr.rd.car.business.org.entity.OrgUserInfo;
import com.westcatr.rd.car.business.org.service.OrgUserInfoService;
import com.westcatr.rd.car.business.testdrive.mapper.TestDriveEvaluationInfoMapper;
import com.westcatr.rd.car.common.ExcelResponseHelper;
import com.westcatr.rd.car.common.easyexcel.EasyExcelUtil;
import com.westcatr.rd.car.common.easyexcel.ExcelSelectedResolve;
import com.westcatr.rd.car.common.easyexcel.SelectedSheetWriteHandler;
import com.westcatr.rd.car.enums.CarStatusEnum;
import com.westcatr.rd.car.enums.FlowStatusCarAdd;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <p>
 * 车辆—基础信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Service
public class CarInfoServiceImpl extends ServiceImpl<CarInfoMapper, CarInfo> implements CarInfoService {

    @Autowired
    private CarModelInfoService carModelInfoService;

    @Autowired
    private TestDriveEvaluationInfoMapper testDriveEvaluationInfoMapper;

    @Lazy
    @Autowired
    private CarDisplayInfoService carDisplayInfoService;

    @Autowired
    private DemandBrowseInfoService demandBrowseInfoService;

    @Autowired
    private OrgUserInfoService orgUserInfoService;

    @Autowired
    private CarFlowDataInfoService carFlowDataInfoService;

    @Autowired
    private CarFlowParamDataInfoService carFlowParamDataInfoService;

    @Autowired
    private JoinInfoService joinInfoService;

    @Autowired
    private CarParameterInfoService carParameterInfoService;

    @Autowired
    private CarBasicInfoService carBasicInfoService;

    @Autowired
    private FlowTaskInfoService flowTaskInfoService;

    @Autowired
    private CarFlowTaskInfoService carFlowTaskInfoService;

    @Value("${caradmin.username}")
    private String carAdminUsername;

    @Override
    public IPage<CarInfo> entityPage(CarInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<CarInfo>().create(query));
    }

    @Override
    public CarInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Transactional
    @Override
    public SaveOrUpdateReturnInfoDto saveEntity(CarInfo param) throws Exception {
        IUser iUser = AuthUtil.getUserE();
        Long supplierId;
        if (ObjectUtil.isNotNull(iUser.getExtendData().getLong("supplierId"))) {
            supplierId = iUser.getExtendData().getLong("supplierId");
        } else {
            throw new IRuntimeException("当前用户不属于供应商，无法进行此操作");
        }
        long oldCount = this.count(new LambdaQueryWrapper<CarInfo>().eq(CarInfo::getBrandModel, param.getBrandModel())
                .eq(CarInfo::getSupplierId, supplierId));
        if (oldCount > 0) {
            throw new IRuntimeException("当前供应商已存在此车型，请勿重复添加");
        }
        CarBasicInfo carBasicInfo = carBasicInfoService.getOne(new LambdaQueryWrapper<>(CarBasicInfo.class)
                .like(CarBasicInfo::getBrandModel, param.getBrandModel()).last("limit 1"));
        if (ObjectUtil.isNull(carBasicInfo)) {
            carBasicInfo = new CarBasicInfo();
            carBasicInfo.setBrandModel(param.getBrandModel());
            carBasicInfoService.save(carBasicInfo);
        }
        param.setCarBasicId(carBasicInfo.getId());
        param.setStatusInfo(0);
        param.setListingStatus(0);
        param.setSupplierId(supplierId);
        SaveOrUpdateReturnInfoDto dto = new SaveOrUpdateReturnInfoDto();
        if (this.saveOrUpdate(param)) {
            dto.setCarId(param.getId());
            if (CollUtil.isNotEmpty(param.getAddCarModelInfos())) {
                List<CarModelInfo> carModelInfos = param.getAddCarModelInfos();
                carModelInfos.forEach(carModelInfo -> {
                    carModelInfo.setCarId(param.getId());
                    carModelInfo.setSupplierId(supplierId);
                });
                if (carModelInfoService.saveOrUpdateBatch(carModelInfos)) {
                    dto.setCarModels(carModelInfos.stream().map(CarModelInfo::getId).toList());
                    if (CollUtil.isNotEmpty(param.getNewAddCarParameterInfo())
                            && !param.getNewAddCarParameterInfo().isEmpty()) {
                        Map<String, Object> info = param.getNewAddCarParameterInfo();
                        info.put("carId", param.getId());
                        info.put("carModelId", carModelInfos.get(0).getId());
                        carParameterInfoService.processEntities(info);
                    }
                }
            }
            dto.setOldStatus(param.getStatusInfo());
        }
        return dto;
    }

    @Override
    public SaveOrUpdateReturnInfoDto updateEntity(CarInfo param) throws Exception {
        IUser iUser = AuthUtil.getUserE();
        Long supplierId;
        if (ObjectUtil.isNotNull(iUser.getExtendData().getLong("supplierId"))) {
            supplierId = iUser.getExtendData().getLong("supplierId");
        } else {
            throw new IRuntimeException("当前用户不属于供应商，无法进行此操作");
        }
        SaveOrUpdateReturnInfoDto dto = new SaveOrUpdateReturnInfoDto();
        CarInfo carOldInfo = this.getById(param.getId());
        // 如果是驳回状态下更新的话，就不改为草稿状态
        if (carOldInfo.getStatusInfo() != CarStatusEnum.HAD_REJECTED.getCode()) {
            param.setStatusInfo(CarStatusEnum.DRAFT.getCode());
            param.setListingStatus(0);
        }
        dto.setOldStatus(carOldInfo.getStatusInfo());
        dto.setOldFlowId(carOldInfo.getFlowId());
        // 针对上下架单独处理，避免逻辑混乱
        if (this.updateById(param)) {
            dto.setCarId(param.getId());
            if (CollUtil.isNotEmpty(param.getAddCarModelInfos())) {
                List<CarModelInfo> carModelInfos = param.getAddCarModelInfos();
                carModelInfos.forEach(carModelInfo -> {
                    carModelInfo.setCarId(param.getId());
                    carModelInfo.setSupplierId(supplierId);
                });

                List<Long> newCarModelIds = carModelInfos.stream().map(CarModelInfo::getId).toList();
                List<CarModelInfo> oldCarModelInfos = carModelInfoService
                        .list(new LambdaQueryWrapper<CarModelInfo>().eq(CarModelInfo::getCarId, param.getId()));
                List<Long> deleteIds = new ArrayList<>();
                if (CollUtil.isNotEmpty(oldCarModelInfos)) {
                    // 获取需要删除的id
                    deleteIds = oldCarModelInfos.stream().map(CarModelInfo::getId)
                            .filter(id -> !newCarModelIds.contains(id)).toList();
                }
                if (carModelInfoService.saveOrUpdateBatch(param.getAddCarModelInfos())) {
                    dto.setCarModels(carModelInfos.stream().map(CarModelInfo::getId).toList());
                }

                if (CollUtil.isNotEmpty(deleteIds)) {
                    if (carModelInfoService.removeByIds(deleteIds)) {
                        deleteIds.forEach(y -> {
                            try {
                                carParameterInfoService.removeBatchByIdsAllTable(param.getId(), y);
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        });
                    }
                }
            }
            if (CollUtil.isNotEmpty(param.getNewAddCarParameterInfo())
                    && !param.getNewAddCarParameterInfo().isEmpty() && param.getCarModelId() != null) {
                Map<String, Object> info = param.getNewAddCarParameterInfo();
                info.put("carId", param.getId());
                info.put("carModelId", param.getCarModelId());
                carParameterInfoService.processEntities(info);
            }
            // 如果已经审核完成的车辆，供应商再去编辑，并且这个车辆的图片设置为banner置顶，那么同时取消他的置顶
            LambdaUpdateWrapper<CarDisplayInfo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(CarDisplayInfo::getCarId, param.getId());
            updateWrapper.set(CarDisplayInfo::getIsCarousel, 0);
            carDisplayInfoService.update(updateWrapper);
        }
        // 不是驳回状态下，用户编辑信息之后把流程id重置为空
        if (carOldInfo.getStatusInfo() != CarStatusEnum.HAD_REJECTED.getCode()) {
            if (StrUtil.isNotEmpty(carOldInfo.getFlowId())) {
                String oldFlowId = carOldInfo.getFlowId();
                LambdaUpdateWrapper<CarInfo> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(CarInfo::getId, param.getId());
                updateWrapper.set(CarInfo::getFlowId, null);
                this.update(updateWrapper);
                joinInfoService.add(2, param.getId(), oldFlowId);
            }
        }

        return dto;
    }

    @Override
    public boolean removeEntityById(Long id) {
        if (this.removeById(id)) {
            carModelInfoService.remove(new LambdaQueryWrapper<>(CarModelInfo.class).eq(CarModelInfo::getCarId, id));
            carDisplayInfoService
                    .remove(new LambdaQueryWrapper<>(CarDisplayInfo.class).eq(CarDisplayInfo::getCarId, id));
        }
        return true;
    }

    @Override
    public void exportTemplate() {
        HttpServletResponse httpServletResponse = ExcelResponseHelper.getExcelResponse("车辆信息模板");
        try {

            // WriteSheet writeSheet = EasyExcelUtil.writeSelectedSheet(CarExcelDto.class,
            // 0, "车辆配置信息");
            // 使用 EasyExcel 写入数据到响应流
            Map<Integer, ExcelSelectedResolve> selectedMap = EasyExcelUtil.resolveSelectedAnnotation(CarExcelDto.class);
            EasyExcel.write(httpServletResponse.getOutputStream(), CarExcelDto.class)
                    .registerWriteHandler(new SelectedSheetWriteHandler(selectedMap))
                    .sheet("车辆基本信息")
                    .doWrite(Collections.emptyList());
        } catch (IOException e) {
            throw new IRuntimeException("导出模板失败");
        }
    }

    @Transactional
    @Override
    public boolean importExcel(MultipartFile file) throws IOException {
        if (file.isEmpty()) {
            throw new IRuntimeException("文件不能为空！");
        }
        // 创建监听器实例
        CarInfoListener listener = new CarInfoListener();
        // 解析文件
        EasyExcel.read(file.getInputStream(), CarExcelDto.class, listener).sheet().doRead();
        // 获取解析后的数据
        List<CarExcelDto> dataList = listener.getDataList();
        if (CollUtil.isEmpty(dataList)) {
            throw new IRuntimeException("导入数据为空");
        }
        IUser iUser = AuthUtil.getUserE();
        if (ObjectUtil.isNull(iUser.getExtendData().getLong("supplierId"))) {
            throw new IRuntimeException("当前用户不属于供应商，无法导入新增");
        }
        Long supplierId = iUser.getExtendData().getLong("supplierId");
        for (CarExcelDto data : dataList) {
            CarBasicInfo carBasicInfo = carBasicInfoService.getOne(new LambdaQueryWrapper<>(CarBasicInfo.class)
                    .like(CarBasicInfo::getBrandModel, data.getCarBrandModel()).last("limit 1"));
            if (ObjectUtil.isNull(carBasicInfo)) {
                carBasicInfo = new CarBasicInfo();
                carBasicInfo.setBrandModel(data.getCarBrandModel());
                carBasicInfoService.save(carBasicInfo);
            }
            CarInfo carInfo = this.getOne(new LambdaQueryWrapper<>(CarInfo.class)
                    .eq(CarInfo::getBrandModel, data.getCarBrandModel()).eq(CarInfo::getSupplierId, supplierId));
            if (ObjectUtil.isNull(carInfo)) {
                carInfo = new CarInfo();
                carInfo.setBrandModel(data.getCarBrandModel());
            }
            carInfo.setIntroduction(data.getIntroduction());
            carInfo.setSupplierId(supplierId);
            carInfo.setStatusInfo(CarStatusEnum.DRAFT.getCode());
            carInfo.setListingStatus(0);
            carInfo.setCarBasicId(carBasicInfo.getId());
            if (this.saveOrUpdate(carInfo)) {
                CarModelInfo carModelInfo = carModelInfoService.getOne(new LambdaQueryWrapper<>(CarModelInfo.class)
                        .eq(CarModelInfo::getCarId, carInfo.getId()).eq(CarModelInfo::getYearModel, data.getYearModel())
                        .eq(CarModelInfo::getConfigName, data.getConfigName()).last("limit 1"));
                if (ObjectUtil.isNull(carModelInfo)) {
                    carModelInfo = new CarModelInfo();
                }
                carModelInfo.setCarId(carInfo.getId());
                carModelInfo.setYearModel(data.getYearModel());
                carModelInfo.setConfigName(data.getConfigName());
                if (StrUtil.isNotEmpty(data.getTestDrive())) {
                    if ("是".equals(data.getTestDrive())) {
                        carModelInfo.setIsTestDrive(true);
                    } else if ("否".equals(data.getTestDrive())) {
                        carModelInfo.setIsTestDrive(false);
                    }
                }
                carModelInfo.setSupplierId(supplierId);
                carModelInfo.setPrice(data.getPrice());
                carModelInfoService.saveOrUpdate(carModelInfo);
            }
        }
        return true;
    }

    @Override
    public List<CarInfoVO> hotRecommendationList(Integer queryCount) {
        List<CarInfoVO> carInfoVos = new ArrayList<>();
        List<Map<String, Object>> queryResult = testDriveEvaluationInfoMapper.findTop6ScoresGroupedByCarId();
        Map<Long, Double> map = queryResult.stream()
                .collect(Collectors.toMap(
                        row -> (Long) row.get("car_id"),
                        row -> (Double) row.get("avg_score")));
        if (ObjectUtil.isNotNull(map) && !map.isEmpty()) {
            List<Long> carIds = new ArrayList<>(map.keySet());
            CarInfoQuery carInfoQuery = new CarInfoQuery();
            carInfoQuery.setIds(carIds);
            carInfoVos = new AssociationQuery<>(CarInfoVO.class).voList(carInfoQuery);
            carInfoVos.forEach(x -> {
                if (CollUtil.isNotEmpty(x.getCarModelInfos())) {
                    x.setLowestPrice(Optional.ofNullable(x.getCarModelInfos())
                            .orElse(Collections.emptyList()) // 如果为 null 则使用空集合
                            .stream()
                            .mapToDouble(CarModelInfo::getPrice)
                            .min()
                            .orElse(0));

                    x.setHighestPrice(Optional.ofNullable(x.getCarModelInfos())
                            .orElse(Collections.emptyList())
                            .stream()
                            .mapToDouble(CarModelInfo::getPrice)
                            .max()
                            .orElse(0));
                }
                x.setTestDriveRating(map.get(x.getId()));
            });
            // 按照试驾评分排序
            carInfoVos.sort(Comparator.comparing(CarInfoVO::getTestDriveRating).reversed());
        }
        if (queryCount != null) {
            carInfoVos = carInfoVos.subList(0, Math.min(queryCount, carInfoVos.size()));
        }
        return carInfoVos;
    }

    @Override
    public CarInfoVO getMyVo(Long id) {
        AssociationQuery<CarInfoVO> associationQuery = new AssociationQuery<>(CarInfoVO.class);
        CarInfoVO carInfoVO = associationQuery.getVo(id);
        if (carInfoVO == null) {
            return null;
        }
        carInfoVO.setCarModelYearDtoList(carDisplayInfoService.getCarModelYearDtoList(id));
        if (CollUtil.isNotEmpty(carInfoVO.getCarModelInfos())) {
            carInfoVO.setLowestPrice(
                    Optional.ofNullable(carInfoVO.getCarModelInfos())
                            .orElse(Collections.emptyList())
                            .stream()
                            .mapToDouble(carModelInfo -> Optional.ofNullable(carModelInfo.getPrice()).orElse(0.0))
                            .min()
                            .orElse(0.0));

            carInfoVO.setHighestPrice(
                    Optional.ofNullable(carInfoVO.getCarModelInfos())
                            .orElse(Collections.emptyList())
                            .stream()
                            .mapToDouble(carModelInfo -> Optional.ofNullable(carModelInfo.getPrice()).orElse(0.0))
                            .max()
                            .orElse(0.0));
        }
        if (CollUtil.isNotEmpty(carInfoVO.getTestDriveEvaluationInfos())) {
            double rating = Optional.ofNullable(carInfoVO.getTestDriveEvaluationInfos())
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(Objects::nonNull)
                    .mapToDouble(info -> info.getScoreInfo() == null ? 0 : info.getScoreInfo())
                    .average()
                    .orElse(0);
            // 向下取整保留两位小数
            carInfoVO.setTestDriveRating(Math.floor(rating * 100) / 100);
        }
        demandBrowseInfoService.saveEntity(new DemandBrowseInfo(
                carInfoVO.getId(),
                (carInfoVO.getCarModelInfos() != null && !carInfoVO.getCarModelInfos().isEmpty())
                        ? carInfoVO.getCarModelInfos().get(0).getId()
                        : null,
                AuthUtil.getUserE().getId()));
        return carInfoVO;
    }

    @Transactional
    @Override
    public boolean startedFlow(CarInfo carInfo) throws Exception {
        Long adminId = orgUserInfoService.findFirstAdminId();
        if (adminId == null) {
            throw new IRuntimeException("未找到管理员，无法发起流程！");
        }
        IUser iUser = AuthUtil.getUserE();
        SaveOrUpdateReturnInfoDto dto = null;
        if ("表单提交".equals(carInfo.getSubmitType())) {
            if (carInfo.getId() == null) {
                dto = this.saveEntity(carInfo);
            } else {
                dto = this.updateEntity(carInfo);
            }
        } else if ("列表提交".equals(carInfo.getSubmitType())) {
            dto = new SaveOrUpdateReturnInfoDto();
            dto.setCarId(carInfo.getId());
            List<CarModelInfo> carModelInfos = carModelInfoService
                    .list(new LambdaQueryWrapper<>(CarModelInfo.class).eq(CarModelInfo::getCarId, carInfo.getId()));
            if (CollUtil.isNotEmpty(carModelInfos)) {
                dto.setCarModels(carModelInfos.stream().map(CarModelInfo::getId).toList());
            }
            CarInfo carInfoOld = this.getEntityById(carInfo.getId());
            if (ObjectUtil.isNotNull(carInfoOld)) {
                dto.setOldFlowId(carInfoOld.getFlowId());
                dto.setOldStatus(carInfoOld.getStatusInfo());
            }
        }
        if (ObjectUtil.isNotNull(dto)) {
            // 如果是驳回状态，那么就是提交流程
            if (dto.getOldStatus() == CarStatusEnum.HAD_REJECTED.getCode()) {
                CompleteTaskDto completeTaskDto = new CompleteTaskDto();
                completeTaskDto.setBusId(carInfo.getId());
                completeTaskDto.setIsPass(true);
                flowTaskInfoService.completeTaskNew(completeTaskDto);
            } else {
                OrgUserInfo orgUserInfo = orgUserInfoService
                        .getOne(new LambdaQueryWrapper<OrgUserInfo>().eq(OrgUserInfo::getUserName, carAdminUsername)
                                .last("limit 1"));
                if (ObjectUtil.isNull(orgUserInfo)) {
                    throw new IRuntimeException("未找到车辆审核管理员，无法发起流程！");
                }
                CarFlowTaskInfo carFlowTaskInfo = new CarFlowTaskInfo();
                carFlowTaskInfo.setTaskName("车辆信息新增");
                Date date = new Date();

                // 新增发起人流程节点
                carFlowTaskInfo.setStartTime(date);
                carFlowTaskInfo.setEndTime(new Date(date.getTime() + 10000));
                carFlowTaskInfo.setUserId(iUser.getId());
                carFlowTaskInfo.setUserFullName(iUser.getFullName());
                carFlowTaskInfo.setBusType("新增车辆信息审批");
                carFlowTaskInfo.setBusId(carInfo.getId());
                carFlowTaskInfo.setStatusInfo(1);
                carFlowTaskInfo.setIsPassed(1);
                carFlowTaskInfo.setTaskName("车辆信息新增");
                carFlowTaskInfo.setCommitInfo("启动流程自动提交");
                carFlowTaskInfoService.save(carFlowTaskInfo);

                // 新增审核人流程节点
                carFlowTaskInfo.setId(null);
                carFlowTaskInfo.setUserId(orgUserInfo.getId());
                carFlowTaskInfo.setUserFullName(orgUserInfo.getFullName());
                carFlowTaskInfo.setTaskName("车辆信息审批");
                carFlowTaskInfo.setStatusInfo(0);
                carFlowTaskInfo.setIsPassed(null);
                carFlowTaskInfo.setCommitInfo(null);
                carFlowTaskInfo.setStartTime(new Date(date.getTime() + 20000));
                carFlowTaskInfoService.save(carFlowTaskInfo);
                LambdaUpdateWrapper<CarInfo> carInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                carInfoLambdaUpdateWrapper.eq(CarInfo::getId, carInfo.getId());
                carInfoLambdaUpdateWrapper.set(CarInfo::getStatusInfo, CarStatusEnum.AUDIT.getCode());
                this.update(carInfoLambdaUpdateWrapper);
            }
        } else {
            throw new IRuntimeException("保存或更新失败");
        }
        return true;
    }

    @Override
    public Map<String, Object> processingFlow(CompleteTaskDto completeTaskDto) {
        Map<String, Object> result = new HashMap<>();
        if (FlowStatusCarAdd.VEHICLE_INFORMATION_APPROVAL.getProcessId().equals(completeTaskDto.getTaskDefinedKey())) {
            // 判断状态码
            int status = completeTaskDto.getIsPass()
                    ? CarStatusEnum.ALREADY_THROUGH.getCode()
                    : CarStatusEnum.HAD_REJECTED.getCode();

            // 更新 CarInfo
            LambdaUpdateWrapper<CarInfo> carInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            carInfoLambdaUpdateWrapper.eq(CarInfo::getId, completeTaskDto.getBusId());
            carInfoLambdaUpdateWrapper.set(CarInfo::getStatusInfo, status);
            if (completeTaskDto.getIsPass()) {
                carInfoLambdaUpdateWrapper.set(CarInfo::getListingStatus, 1);
            }
            this.update(carInfoLambdaUpdateWrapper);

            if (completeTaskDto.getIsPass()) {
                // 设置结果
                result.put("approvalOne", true);
            } else {
                result.put("approvalOne", false);
            }
        } else if (FlowStatusCarAdd.VEHICLE_INFORMATION_ADD.getProcessId()
                .equals(completeTaskDto.getTaskDefinedKey())) {
            LambdaUpdateWrapper<CarInfo> carInfoLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            carInfoLambdaUpdateWrapper.eq(CarInfo::getId, completeTaskDto.getBusId());
            carInfoLambdaUpdateWrapper.set(CarInfo::getStatusInfo, CarStatusEnum.AUDIT.getCode());
            this.update(carInfoLambdaUpdateWrapper);
            result.put("approvalOne", true);
        }
        return result;
    }

    @Override
    public IPage<CarInfoVO> myPageVo(CarInfoQuery query) {
        // 数据权限部分
        // 数据权限部分
        Integer status = CarStatusEnum.ALREADY_THROUGH.getCode();
        IUser iUser = AuthUtil.getUserE();

        // 获取用户的扩展数据中的 supplierId
        Long supplierId = iUser.getExtendData().getLong("supplierId");

        // 如果提供了查询状态信息，更新 status
        if (query.getStatusInfo() != null) {
            status = query.getStatusInfo();
        }

        // 如果用户有 supplierId，设置查询的 supplierId
        if (supplierId != null) {
            if (query.getStatusInfo() == null) {
                status = null;
            }
            query.setSupplierId(supplierId);
            query.setStatusInfo(status);
        } else {
            query.setStatusInfo(status);
        }
        if (Stream
                .of(query.getSupplierName(), query.getSupplierType(), query.getSupplierId(), query.getLowPrice(),
                        query.getHighPrice())
                .anyMatch(ObjectUtil::isNotNull)) {
            CarModelInfoQuery carModelInfoQuery = new CarModelInfoQuery();
            if (StrUtil.isNotEmpty(query.getSupplierName())) {
                carModelInfoQuery.setCompanyName(query.getSupplierName());
            }
            if (StrUtil.isNotEmpty(query.getSupplierType())) {
                carModelInfoQuery.setSupplierType(query.getSupplierType());
            }
            if (query.getSupplierId() != null) {
                carModelInfoQuery.setSupplierId(query.getSupplierId());
            }
            if (query.getLowPrice() != null) {
                carModelInfoQuery.setLowPrice(query.getLowPrice());
            }
            if (query.getHighPrice() != null) {
                carModelInfoQuery.setHighPrice(query.getHighPrice());
            }
            List<CarModelInfo> carModelInfos = new AssociationQuery<>(CarModelInfo.class).voList(carModelInfoQuery);
            if (CollUtil.isNotEmpty(carModelInfos)) {
                query.setIds(carModelInfos.stream().map(CarModelInfo::getCarId).distinct().toList());
            } else {
                query.setId("-1");
            }
        }
        query.setStatusSort(0);
        query.setTimeSort(1);
        IPage<CarInfoVO> iPage = new AssociationQuery<>(CarInfoVO.class).voPage(query);
        if (CollUtil.isNotEmpty(iPage.getRecords())) {
            iPage.getRecords().forEach(x -> {
                CarFlowTaskInfo carFlowTaskInfo = carFlowTaskInfoService
                        .getOne(new LambdaQueryWrapper<CarFlowTaskInfo>().eq(CarFlowTaskInfo::getBusId, x.getId())
                                .eq(CarFlowTaskInfo::getStatusInfo, 0).orderByDesc(CarFlowTaskInfo::getCreateTime)
                                .last("limit 1"));
                if (ObjectUtil.isNotNull(carFlowTaskInfo)) {
                    x.setApprovalUserIds(List.of(carFlowTaskInfo.getUserId().toString()));
                }
            });
        }
        return iPage;
    }

    @Override
    public CarFlowDataInfo getFlowOldData(Long carId) {
        return carFlowDataInfoService
                .getOne(new LambdaQueryWrapper<CarFlowDataInfo>().eq(CarFlowDataInfo::getCarId, carId).last("limit 1"));
    }

    @Override
    public CarFlowParamDataInfo getCarFlowParamDataInfo(Long carModelId) {
        return carFlowParamDataInfoService.getOne(new LambdaQueryWrapper<CarFlowParamDataInfo>()
                .eq(CarFlowParamDataInfo::getCarModelId, carModelId).last("limit 1"));
    }

    @Override
    public boolean upperAndLowerFrames(CarInfo carInfo) {
        LambdaUpdateWrapper<CarInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CarInfo::getId, carInfo.getId());
        wrapper.set(CarInfo::getListingStatus, carInfo.getListingStatus());
        return this.update(wrapper);
    }

}
