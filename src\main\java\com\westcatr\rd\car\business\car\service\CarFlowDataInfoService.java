package com.westcatr.rd.car.business.car.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.car.business.car.pojo.query.CarFlowDataInfoQuery;
import com.westcatr.rd.car.business.car.entity.CarFlowDataInfo;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface CarFlowDataInfoService extends IService<CarFlowDataInfo> {

    IPage<CarFlowDataInfo> entityPage(CarFlowDataInfoQuery query);

    CarFlowDataInfo getEntityById(Long id);

    boolean saveEntity(CarFlowDataInfo param);

    boolean updateEntity(CarFlowDataInfo param);

    boolean removeEntityById(Long id);
}
