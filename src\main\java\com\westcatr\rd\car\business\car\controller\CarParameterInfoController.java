package com.westcatr.rd.car.business.car.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.car.business.car.entity.CarParameterInfo;
import com.westcatr.rd.car.business.car.pojo.dto.FindEntitiesByDescriptionDto;
import com.westcatr.rd.car.business.car.pojo.query.CarParameterInfoQuery;
import com.westcatr.rd.car.business.car.pojo.vo.CarParameterInfoVO;
import com.westcatr.rd.car.business.car.service.CarParameterInfoService;
import com.westcatr.rd.car.common.annotation.AuditLog;
import com.westcatr.rd.car.enums.OperationTypeEnum;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * CarParameterInfo 控制器
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Validated
@Tag(name = "车辆—参数信息表接口", description = "车辆—参数信息表接口")
@Slf4j
@RestController
public class CarParameterInfoController {

    @Autowired
    private CarParameterInfoService carParameterInfoService;

    @Operation(summary = "获取车辆—参数信息表分页数据")
    @PostMapping("/carParameterInfo/page")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车辆参数信息表分页数据")
    public IResult<IPage<CarParameterInfo>> getCarParameterInfoPage(@RequestBody CarParameterInfoQuery query) {
        return IResult.ok(carParameterInfoService.entityPage(query));
    }

    @Operation(summary = "获取车辆—参数信息表数据")
    @PostMapping("/carParameterInfo/get")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车辆参数信息表数据: ID={#id.id}")
    public IResult<CarParameterInfo> getCarParameterInfoById(@RequestBody @Id ID id) {
        return IResult.ok(carParameterInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增车辆—参数信息表数据")
    @PostMapping("/carParameterInfo/add")
    @AuditLog(operationType = OperationTypeEnum.ADD, description = "新增车辆参数信息表数据")
    public IResult addCarParameterInfo(@RequestBody Map<String, Object> param) throws Exception {
        return IResult.auto(carParameterInfoService.processEntities(param));
    }

    @Operation(summary = "更新车辆—参数信息表数据")
    @PostMapping("/carParameterInfo/update")
    @AuditLog(operationType = OperationTypeEnum.UPDATE, description = "更新车辆参数信息表数据")
    public IResult updateCarParameterInfoById(@RequestBody Map<String, Object> param) throws Exception {
        return IResult.auto(carParameterInfoService.processEntities(param));
    }

    @Operation(summary = "删除车辆—参数信息表数据")
    @PostMapping("/carParameterInfo/delete")
    @AuditLog(operationType = OperationTypeEnum.DELETE, description = "删除车辆参数信息表数据: ID={#id.id}")
    public IResult deleteCarParameterInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            carParameterInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取车辆—参数信息表VO分页数据")
    @PostMapping("/carParameterInfo/voPage")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车辆参数信息表VO分页数据")
    public IResult<IPage<CarParameterInfoVO>> getCarParameterInfoVoPage(@RequestBody CarParameterInfoQuery query) {
        AssociationQuery<CarParameterInfoVO> associationQuery = new AssociationQuery<>(CarParameterInfoVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取车辆—参数信息表VO数据")
    @PostMapping("/carParameterInfo/getVo")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车辆参数信息表VO数据: ID={#id.id}")
    public IResult<Map<String, Map<String, String>>> getCarParameterInfoVoById(@RequestBody @Id ID id)
            throws Exception {
        return IResult.ok(carParameterInfoService.collectBusinessDataSorted(id.longId()));
    }

    /*
     * @Operation(summary = "导出车辆—参数信息表模板")
     * 
     * @GetMapping("/carParameterInfo/exportTemplate")
     * public void exportTemplate() {
     * carParameterInfoService.exportTemplate();
     * }
     */

    /*
     * @Operation(summary = "导入车辆—参数信息表")
     * 
     * @PostMapping("/carParameterInfo/import")
     * public IResult<CarParameterInfo> importExcel(Long carId, Long
     * carModelId, @RequestParam("file") MultipartFile file) throws IOException {
     * return IResult.ok(carParameterInfoService.importExcel(carId, carModelId,
     * file));
     * }
     */

    @Operation(summary = "处理车辆—导出模板（单个所有表）")
    @GetMapping("/carParameterInfo/exportTemplate")
    @AuditLog(operationType = OperationTypeEnum.EXPORT, description = "导出车辆参数信息表模板")
    public ResponseEntity<byte[]> exportExcelAllTable() throws Exception {
        // 获取实体类并生成Excel文件
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        carParameterInfoService.exportExcelAllTable(byteArrayOutputStream);

        // 设置响应头并返回文件
        HttpHeaders headers = new HttpHeaders();
        String fileName = "车型参数模板表.xlsx";

        // 对文件名进行URL编码，确保中文文件名能够正确显示// 将 "+" 替换为空格
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8)
                .replaceAll("\\+", "%20");

        headers.add("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
        headers.add("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

        return new ResponseEntity<>(byteArrayOutputStream.toByteArray(), headers, HttpStatus.OK);
    }

    @Operation(summary = "处理车辆—不同参数对应的key、value")
    @PostMapping("/carParameterInfo/findEntitiesByDescription")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车辆参数对应关系: searchTerm={#dto.searchTerm}")
    public IResult<Map<String, String>> findEntitiesByDescription(@RequestBody FindEntitiesByDescriptionDto dto)
            throws Exception {
        return IResult.ok(carParameterInfoService.findEntitiesByDescription(dto.getSearchTerm()));
    }

    @Operation(summary = "处理车辆—导入模板（单个所有表）")
    @PostMapping("/carParameterInfo/import")
    @AuditLog(operationType = OperationTypeEnum.IMPORT, description = "导入车辆参数信息表数据")
    public IResult<Map<String, Map<String, String>>> importExcelAllTable(@RequestParam("file") MultipartFile file)
            throws IOException {
        return IResult.ok(carParameterInfoService.importExcel(file));
    }
}
