package com.westcatr.rd.car.business.fanfan.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.car.business.supplier.task.SupplierRelationSyncTask;
import com.westcatr.rd.car.business.supplier.task.SupplierUserSyncTask;
import com.westcatr.rd.car.scheduledtask.FanFanMaterialSyncTask;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * 泛泛系统数据同步控制器
 *
 * <AUTHOR>
 * @date 2025-06-08
 */
@Slf4j
@RestController
@RequestMapping("/api/fanfan/sync")
@Tag(name = "泛泛系统数据同步接口")
public class FanFanSyncController {

    @Autowired
    private FanFanMaterialSyncTask fanFanMaterialSyncTask;

    @Autowired
    private SupplierUserSyncTask supplierUserSyncTask;

    @Autowired
    private SupplierRelationSyncTask supplierRelationSyncTask;

    /**
     * 手动触发同步泛泛系统配件和工时数据
     *
     * @return 同步结果
     */
    @PostMapping("/materials")
    @Operation(summary = "手动触发同步泛泛系统配件和工时数据")
    public IResult<String> syncMaterials() {
        log.info("🔄 手动触发同步泛泛系统配件和工时数据");
        try {
            fanFanMaterialSyncTask.syncFanFanMaterials();
            return IResult.ok("同步泛泛系统配件和工时数据成功");
        } catch (Exception e) {
            // log.error("❌ 同步泛泛系统配件和工时数据失败", e);
            return IResult.fail("同步失败：" + e.getMessage());
        }
    }

    /**
     * 手动触发同步维修厂用户
     * 为无用户ID的维修供应商创建用户并建立关联
     *
     * @return 同步结果
     */
    @PostMapping("/repair-users")
    @Operation(summary = "手动触发同步维修厂用户")
    public IResult<String> syncRepairUsers() {
        log.info("🔄 手动触发同步维修厂用户");
        try {
            supplierUserSyncTask.manualSync();
            return IResult.ok("同步维修厂用户成功");
        } catch (Exception e) {
            // log.error("❌ 同步维修厂用户失败", e);
            return IResult.fail("同步失败：" + e.getMessage());
        }
    }

    /**
     * 手动触发同步供应商关联关系
     * 同步维修厂与上级公司的关联关系
     *
     * @return 同步结果
     */
    @PostMapping("/supplier-relations")
    @Operation(summary = "手动触发同步供应商关联关系")
    public IResult<String> syncSupplierRelations() {
        log.info("🔄 手动触发同步供应商关联关系");
        try {
            supplierRelationSyncTask.manualSync();
            return IResult.ok("同步供应商关联关系成功");
        } catch (Exception e) {
            log.error("❌ 同步供应商关联关系失败", e);
            return IResult.fail("同步失败：" + e.getMessage());
        }
    }

    /**
     * 测试对应关系文件读取
     * 验证文件是否能正确读取
     *
     * @return 测试结果
     */
    @PostMapping("/test-relation-file")
    @Operation(summary = "测试对应关系文件读取")
    public IResult<String> testRelationFile() {
        log.info("🧪 测试对应关系文件读取");
        try {
            supplierRelationSyncTask.testFileReading();
            return IResult.ok("文件读取测试完成，请查看日志");
        } catch (Exception e) {
            log.error("❌ 文件读取测试失败", e);
            return IResult.fail("测试失败：" + e.getMessage());
        }
    }
}
