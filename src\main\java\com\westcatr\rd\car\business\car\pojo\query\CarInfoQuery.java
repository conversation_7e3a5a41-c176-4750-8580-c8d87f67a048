package com.westcatr.rd.car.business.car.pojo.query;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import jakarta.validation.constraints.Pattern;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.westcatr.rd.boot.orm.association.annotation.JoinExpression;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 车辆—基础信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "车辆—基础信息表查询对象")
public class CarInfoQuery extends PageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @QueryCondition(field = "car_info.id")
    @Pattern(regexp = "^$|^[0-9]+$", message = "id只能输入数字")
    private String id;

    @Schema(description = "主键ID集合")
    @QueryCondition(condition = QueryCondition.Condition.IN, field = "car_info.id")
    private List<Long> ids;

    @Schema(description = "最高价格")
    @QueryCondition(condition = QueryCondition.Condition.IN, field = "flow_id")
    private List<String> flowIds;

    @Schema(description = "品牌与车型")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String brandModel;

    @Schema(description = "车辆介绍")
    @QueryCondition
    private String description;

    @Schema(description = "创建时间")
    @QueryCondition
    private Date createTime;

    @Schema(description = "更新时间")
    @QueryCondition
    private Date updateTime;

    @Schema(description = "视频标题")
    @QueryCondition
    private String videoTitle;

    @Schema(description = "车辆图片")
    @QueryCondition
    private String carImgUrl;

    @Schema(description = "车辆推广图")
    @QueryCondition
    private String carPromotionImgUrl;

    @Schema(description = "车辆视频")
    @QueryCondition
    private String carVideoUrl;

    @Schema(description = "状态")
    @QueryCondition(field = "status_info")
    private Integer statusInfo;

    @Schema(description = "上架状态(1上架、0下架)")
    @QueryCondition(field = "listing_status")
    private Integer listingStatus;

    @Schema(description = "试驾评分")
    @QueryCondition
    private Double testDriveScoreInfo;

    @Schema(description = "价格区间（低）")
    // @JoinExpression(value = "join car_model_info cmi on cmi.car_id=car_info.id")
    // @QueryCondition(condition = QueryCondition.Condition.GE, field = "cmi.price")
    private Double lowPrice;

    @Schema(description = "价格区间（高）")
    // @JoinExpression(value = "join car_model_info cmi on cmi.car_id=car_info.id")
    // @QueryCondition(condition = QueryCondition.Condition.LE, field = "cmi.price")
    private Double highPrice;

    @Schema(description = "id排序，1倒叙，0正序")
    @QueryCondition(condition = QueryCondition.Condition.DEFAULT, field = "status_info", sort = QueryCondition.Sort.AUTO)
    private Integer statusSort;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "开始时间")
    @QueryCondition(condition = QueryCondition.Condition.GE, field = "car_info.create_time")
    private Date beginTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "结束时间")
    @QueryCondition(condition = QueryCondition.Condition.LE, field = "car_info.create_time")
    private Date endTime;

    @Schema(description = "是否设置排序,传入0或者1")
    @JoinExpression(value = "join car_display_info cdi on cdi.car_id=@m.id")
    @QueryCondition(condition = QueryCondition.Condition.DEFAULT, field = "cdi.sort_num", sort = QueryCondition.Sort.AUTO)
    private Integer tfSetTheSort;

    @Schema(description = "时间排序，1倒叙，0正序")
    @QueryCondition(condition = QueryCondition.Condition.DEFAULT, field = "car_info.create_time", sort = QueryCondition.Sort.AUTO)
    private Integer timeSort;

    @Schema(description = "id排序，1倒叙，0正序")
    @QueryCondition(condition = QueryCondition.Condition.DEFAULT, field = "id", sort = QueryCondition.Sort.AUTO)
    private Integer idSort;

    // @JoinExpression(value = "join car_parameter_info cpi on
    // cpi.car_id=car_info.id")
    // @QueryCondition(condition = QueryCondition.Condition.EQ, field =
    // "cpi.energy_type")
    @Schema(description = "能源类型")
    private String energyType;

    /*
     * @JoinExpression(value =
     * "inner join car_model_info cmi on cmi.car_id=car_info.id inner join supplier_info si on cmi.supplier_id=si.id"
     * )
     * 
     * @QueryCondition(condition = QueryCondition.Condition.EQ, field = "si.id")
     * 
     * @Select()
     */
    private Long supplierId;

    /*
     * @JoinExpression(value =
     * "join car_model_info cmi on cmi.car_id=car_info.id left join supplier_info si on cmi.supplier_id=si.id"
     * )
     * 
     * @QueryCondition(condition = QueryCondition.Condition.LIKE, field =
     * "si.company_name")
     */
    private String supplierName;

    /*
     * @JoinExpression(value =
     * "join car_model_info cmi on cmi.car_id=car_info.id left join supplier_info si on cmi.supplier_id=si.id"
     * )
     * 
     * @QueryCondition(condition = QueryCondition.Condition.EQ, field =
     * "si.supplier_type")
     */
    private String supplierType;

}
