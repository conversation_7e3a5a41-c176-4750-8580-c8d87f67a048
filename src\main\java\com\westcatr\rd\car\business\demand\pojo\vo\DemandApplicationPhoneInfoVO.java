package com.westcatr.rd.car.business.demand.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.car.business.demand.entity.DemandApplicationPhoneInfo;
import com.westcatr.rd.car.business.supplier.entity.SupplierInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 需求—供应商申请获取用户电话信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "需求—供应商申请获取用户电话信息表VO对象")
public class DemandApplicationPhoneInfoVO extends DemandApplicationPhoneInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    @Schema(description = "供应商信息")
    @JoinSelect(joinClass = SupplierInfo.class, mainId = "supplierId")
    private SupplierInfo supplierInfo;

    @TableField(exist = false)
    @Schema(description = "需求信息")
    private DemandInfoVO demandInfo;
}
