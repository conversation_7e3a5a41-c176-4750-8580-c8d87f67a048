package com.westcatr.rd.car.business.car.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.car.business.car.entity.CarModelInfo;
import com.westcatr.rd.car.business.car.pojo.query.CarModelInfoQuery;
import com.westcatr.rd.car.business.car.pojo.vo.CarModelInfoVO;
import com.westcatr.rd.car.business.car.service.CarModelInfoService;
import com.westcatr.rd.car.common.annotation.AuditLog;
import com.westcatr.rd.car.enums.OperationTypeEnum;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * CarModelInfo 控制器
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Validated
@Tag(name = "车辆—车型信息表接口", description = "车辆—车型信息表接口")
@Slf4j
@RestController
public class CarModelInfoController {

    @Autowired
    private CarModelInfoService carModelInfoService;

    @Operation(summary = "获取车辆—车型信息表分页数据")
    @PostMapping("/carModelInfo/page")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车辆车型信息表分页数据")
    public IResult<IPage<CarModelInfo>> getCarModelInfoPage(@RequestBody CarModelInfoQuery query) {
        return IResult.ok(carModelInfoService.entityPage(query));
    }

    @Operation(summary = "获取车辆—车型信息表数据")
    @PostMapping("/carModelInfo/get")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车辆车型信息表数据: ID={#id.id}")
    public IResult<CarModelInfo> getCarModelInfoById(@RequestBody @Id ID id) {
        return IResult.ok(carModelInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增车辆—车型信息表数据")
    @PostMapping("/carModelInfo/add")
    @AuditLog(operationType = OperationTypeEnum.ADD, description = "新增车辆车型信息表数据: ID={#param.id}")
    public IResult addCarModelInfo(@RequestBody @Validated(Insert.class) CarModelInfo param) {
        return IResult.auto(carModelInfoService.saveEntity(param));
    }

    @Operation(summary = "更新车辆—车型信息表数据")
    @PostMapping("/carModelInfo/update")
    @AuditLog(operationType = OperationTypeEnum.UPDATE, description = "更新车辆车型信息表数据: ID={#param.id}")
    public IResult updateCarModelInfoById(@RequestBody @Validated(Update.class) CarModelInfo param) {
        return IResult.auto(carModelInfoService.updateEntity(param));
    }

    @Operation(summary = "删除车辆—车型信息表数据")
    @PostMapping("/carModelInfo/delete")
    @AuditLog(operationType = OperationTypeEnum.DELETE, description = "删除车辆车型信息表数据: ID={#id.id}")
    public IResult deleteCarModelInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            carModelInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取车辆—车型信息表VO分页数据")
    @PostMapping("/carModelInfo/voPage")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车辆车型信息表VO分页数据")
    public IResult<IPage<CarModelInfoVO>> getCarModelInfoVoPage(@Validated @RequestBody CarModelInfoQuery query) {
        return IResult.ok(carModelInfoService.getMyVoPage(query));
    }

    @Operation(summary = "获取车辆—车型信息表VO数据")
    @PostMapping("/carModelInfo/getVo")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车辆车型信息表VO数据: ID={#id.id}")
    public IResult<CarModelInfoVO> getCarModelInfoVoById(@RequestBody @Id ID id) {
        return IResult.ok(carModelInfoService.getMyVo(id.longId()));
    }

    @Operation(summary = "获取车辆—车型信息表VO数据（只获取可以试驾的车辆）")
    @PostMapping("/carModelInfo/getVoTestDrive")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询可试驾车辆车型信息: ID={#id.id}")
    public IResult<CarModelInfoVO> getVoTestDrive(@RequestBody @Id ID id) {
        return IResult.ok(carModelInfoService.testDriveGetMyVo(id.longId()));
    }

    @Operation(summary = "获取审核中的车型信息")
    @PostMapping("/carModelInfo/getVoAuditing")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询审核中的车型信息分页数据")
    public IResult<IPage<CarModelInfoVO>> getVoAuditing(@Validated @RequestBody CarModelInfoQuery query) {
        return IResult.ok(carModelInfoService.getMyVoPageAuditing(query));
    }

}
