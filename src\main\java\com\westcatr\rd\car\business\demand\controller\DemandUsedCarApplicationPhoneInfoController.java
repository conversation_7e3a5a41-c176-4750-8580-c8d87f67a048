package com.westcatr.rd.car.business.demand.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.boot.web.excel.pojo.ExcelExportParam;
import com.westcatr.rd.boot.web.excel.service.IExcelExportService;
import com.westcatr.rd.car.business.demand.entity.DemandUsedCarApplicationPhoneInfo;
import com.westcatr.rd.car.business.demand.pojo.query.DemandUsedCarApplicationPhoneInfoQuery;
import com.westcatr.rd.car.business.demand.pojo.vo.DemandUsedCarApplicationPhoneInfoVO;
import com.westcatr.rd.car.business.demand.service.DemandUsedCarApplicationPhoneInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import static cn.hutool.core.util.StrUtil.COMMA;

/**
 * DemandUsedCarApplicationPhoneInfo 控制器
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Validated
@Tag(name = "需求—二手车供应商申请获取用户电话信息表接口", description = "需求—二手车供应商申请获取用户电话信息表接口")
@Slf4j
@RestController
public class DemandUsedCarApplicationPhoneInfoController {

    @Autowired
    private DemandUsedCarApplicationPhoneInfoService demandUsedCarApplicationPhoneInfoService;
    @Autowired
    private IExcelExportService iExcelExportService;

    @Operation(summary = "获取需求—二手车供应商申请获取用户电话信息表分页数据")
    @PostMapping("/demandUsedCarApplicationPhoneInfo/page")
    public IResult<IPage<DemandUsedCarApplicationPhoneInfo>> getDemandUsedCarApplicationPhoneInfoPage(@RequestBody DemandUsedCarApplicationPhoneInfoQuery query) {
        return IResult.ok(demandUsedCarApplicationPhoneInfoService.entityPage(query));
    }

    @Operation(summary = "获取需求—二手车供应商申请获取用户电话信息表数据")
    @PostMapping("/demandUsedCarApplicationPhoneInfo/get")
    public IResult<DemandUsedCarApplicationPhoneInfo> getDemandUsedCarApplicationPhoneInfoById(@RequestBody @Id ID id) {
        return IResult.ok(demandUsedCarApplicationPhoneInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增需求—二手车供应商申请获取用户电话信息表数据")
    @PostMapping("/demandUsedCarApplicationPhoneInfo/add")
    public IResult addDemandUsedCarApplicationPhoneInfo(@RequestBody @Validated(Insert.class) DemandUsedCarApplicationPhoneInfo param) {
        return IResult.auto(demandUsedCarApplicationPhoneInfoService.saveEntity(param));
    }

    @Operation(summary = "更新需求—二手车供应商申请获取用户电话信息表数据")
    @PostMapping("/demandUsedCarApplicationPhoneInfo/update")
    public IResult updateDemandUsedCarApplicationPhoneInfoById(@RequestBody @Validated(Update.class) DemandUsedCarApplicationPhoneInfo param) {
        return IResult.auto(demandUsedCarApplicationPhoneInfoService.updateEntity(param));
    }

    @Operation(summary = "删除需求—二手车供应商申请获取用户电话信息表数据")
    @PostMapping("/demandUsedCarApplicationPhoneInfo/delete")
    public IResult deleteDemandUsedCarApplicationPhoneInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            demandUsedCarApplicationPhoneInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取需求—二手车供应商申请获取用户电话信息表VO分页数据")
    @PostMapping("/demandUsedCarApplicationPhoneInfo/voPage")
    public IResult<IPage<DemandUsedCarApplicationPhoneInfoVO>> getDemandUsedCarApplicationPhoneInfoVoPage(@RequestBody DemandUsedCarApplicationPhoneInfoQuery query) {
        AssociationQuery<DemandUsedCarApplicationPhoneInfoVO> associationQuery = new AssociationQuery<>(DemandUsedCarApplicationPhoneInfoVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取需求—二手车供应商申请获取用户电话信息表VO数据")
    @PostMapping("/demandUsedCarApplicationPhoneInfo/getVo")
    public IResult<DemandUsedCarApplicationPhoneInfoVO> getDemandUsedCarApplicationPhoneInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<DemandUsedCarApplicationPhoneInfoVO> associationQuery = new AssociationQuery<>(DemandUsedCarApplicationPhoneInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    @Operation(summary = "导出需求—二手车供应商申请获取用户电话信息表数据")
    @PostMapping("/demandUsedCarApplicationPhoneInfo/export")
    public void export(@RequestBody ExcelExportParam<DemandUsedCarApplicationPhoneInfoQuery> query) {
        iExcelExportService.exportExcel("需求—二手车供应商申请获取用户电话信息表数据", DemandUsedCarApplicationPhoneInfoVO.class, query, false);
    }

}
