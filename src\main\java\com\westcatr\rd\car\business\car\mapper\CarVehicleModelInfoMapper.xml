<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.car.business.car.mapper.CarVehicleModelInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.car.business.car.entity.CarVehicleModelInfo">
        <id column="id" property="id" />
        <result column="vin" property="vin" />
        <result column="model_code" property="modelCode" />
        <result column="model_name" property="modelName" />
        <result column="body_type" property="bodyType" />
        <result column="engine_type" property="engineType" />
        <result column="engine_displacement" property="engineDisplacement" />
        <result column="transmission_type" property="transmissionType" />
        <result column="fuel_type" property="fuelType" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, vin, model_code, model_name, body_type, engine_type, engine_displacement, transmission_type, fuel_type, create_time, update_time
    </sql>

</mapper>
