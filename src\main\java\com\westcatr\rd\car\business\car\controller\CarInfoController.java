package com.westcatr.rd.car.business.car.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import java.io.IOException;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.car.business.car.entity.CarFlowDataInfo;
import com.westcatr.rd.car.business.car.entity.CarFlowParamDataInfo;
import com.westcatr.rd.car.business.car.entity.CarInfo;
import com.westcatr.rd.car.business.car.pojo.dto.CarFlowParamDataInfoDto;
import com.westcatr.rd.car.business.car.pojo.dto.HotRecommendationListDto;
import com.westcatr.rd.car.business.car.pojo.dto.SaveOrUpdateReturnInfoDto;
import com.westcatr.rd.car.business.car.pojo.query.CarInfoQuery;
import com.westcatr.rd.car.business.car.pojo.vo.CarInfoVO;
import com.westcatr.rd.car.business.car.service.CarInfoService;
import com.westcatr.rd.car.common.annotation.AuditLog;
import com.westcatr.rd.car.enums.OperationTypeEnum;

import cn.hutool.core.util.ObjectUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * CarInfo 控制器
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Validated
@Tag(name = "车辆—基础信息表接口", description = "车辆—基础信息表接口")
@Slf4j
@RestController
public class CarInfoController {

    @Autowired
    private CarInfoService carInfoService;

    @Operation(summary = "获取车辆—基础信息表分页数据")
    @PostMapping("/carInfo/page")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车辆基础信息表分页数据")
    public IResult<IPage<CarInfo>> getCarInfoPage(@RequestBody CarInfoQuery query) {
        return IResult.ok(carInfoService.entityPage(query));
    }

    @Operation(summary = "获取车辆—基础信息表数据")
    @PostMapping("/carInfo/get")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车辆基础信息表数据: ID={#id.id}")
    public IResult<CarInfo> getCarInfoById(@RequestBody @Id ID id) {
        return IResult.ok(carInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增车辆—基础信息表数据")
    @PostMapping("/carInfo/add")
    @AuditLog(operationType = OperationTypeEnum.ADD, description = "新增车辆基础信息表数据")
    public IResult<Long> addCarInfo(@RequestBody @Validated CarInfo param) throws Exception {
        SaveOrUpdateReturnInfoDto dto = carInfoService.saveEntity(param);
        if (ObjectUtil.isNotNull(dto)) {
            return IResult.ok(dto.getCarId());
        } else {
            return IResult.fail("保存失败");
        }
    }

    @Operation(summary = "更新车辆—基础信息表数据")
    @PostMapping("/carInfo/update")
    @AuditLog(operationType = OperationTypeEnum.UPDATE, description = "更新车辆基础信息表数据: ID={#param.id}")
    public IResult updateCarInfoById(@Validated @RequestBody CarInfo param) throws Exception {
        boolean result = false;
        SaveOrUpdateReturnInfoDto dto = carInfoService.updateEntity(param);
        if (ObjectUtil.isNotNull(dto)) {
            result = true;
        }
        return IResult.auto(result);
    }

    @Operation(summary = "删除车辆—基础信息表数据")
    @PostMapping("/carInfo/delete")
    @AuditLog(operationType = OperationTypeEnum.DELETE, description = "删除车辆基础信息表数据: ID={#id.id}")
    public IResult deleteCarInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            carInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取车辆—基础信息表VO分页数据")
    @PostMapping("/carInfo/voPage")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车辆基础信息表VO分页数据")
    public IResult<IPage<CarInfoVO>> getCarInfoVoPage(@Validated @RequestBody CarInfoQuery query) {
        return IResult.ok(carInfoService.myPageVo(query));
    }

    @Operation(summary = "获取车辆—基础信息表VO数据")
    @PostMapping("/carInfo/getVo")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车辆基础信息表VO数据: ID={#id.id}")
    public IResult<CarInfoVO> getCarInfoVoById(@RequestBody @Id ID id) {
        return IResult.ok(carInfoService.getMyVo(id.longId()));
    }

    @Operation(summary = "导出车辆—基础信息表模板")
    @GetMapping("/carInfo/exportTemplate")
    @AuditLog(operationType = OperationTypeEnum.EXPORT, description = "导出车辆基础信息表模板")
    public void exportTemplate() {
        carInfoService.exportTemplate();
    }

    @Operation(summary = "导入车辆—基础信息表")
    @PostMapping("/carInfo/import")
    @AuditLog(operationType = OperationTypeEnum.IMPORT, description = "导入车辆基础信息表数据")
    public IResult importExcel(@RequestParam("file") MultipartFile file) throws IOException {
        return IResult.auto(carInfoService.importExcel(file));
    }

    @Operation(summary = "获取热门推荐车辆")
    @PostMapping("/carInfo/hotRecommendationList")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "获取热门推荐车辆列表: count={#params.queryCount}")
    public IResult<List<CarInfoVO>> hotRecommendationList(@RequestBody HotRecommendationListDto params) {
        return IResult.ok(carInfoService.hotRecommendationList(params.getQueryCount()));
    }

    @Operation(summary = "启动流程")
    @PostMapping("/carInfo/startedFlow")
    @AuditLog(operationType = OperationTypeEnum.UPDATE, description = "启动车辆流程: ID={#carInfo.id}")
    public IResult startedFlow(@RequestBody CarInfo carInfo) throws Exception {
        return IResult.auto(carInfoService.startedFlow(carInfo));
    }

    @Operation(summary = "获取旧的数据（表单）")
    @PostMapping("/carInfo/getFlowOldData")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "获取流程旧数据: carModelId={#params.carModelId}")
    public IResult<CarFlowDataInfo> completeTask(@RequestBody CarFlowParamDataInfoDto params) {
        return IResult.ok(carInfoService.getFlowOldData(params.getCarModelId()));
    }

    @Operation(summary = "获取流程参数详情（表单）")
    @PostMapping("/carInfo/getCarFlowParamDataInfo")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "获取流程参数详情: carModelId={#params.carModelId}")
    public IResult<CarFlowParamDataInfo> getCarFlowParamDataInfo(@RequestBody CarFlowParamDataInfoDto params) {
        return IResult.ok(carInfoService.getCarFlowParamDataInfo(params.getCarModelId()));
    }

    @Operation(summary = "上架/下架")
    @PostMapping("/carInfo/upperAndLowerFrames")
    @AuditLog(operationType = OperationTypeEnum.UPDATE, description = "更新车辆上下架状态: ID={#carInfo.id}")
    public IResult upperAndLowerFrames(@RequestBody CarInfo carInfo) {
        return IResult.auto(carInfoService.upperAndLowerFrames(carInfo));
    }

}
