## E出行开发平台接口文档

#### 接口加解密验签

1. 生成随机Key
2. 通过随机Key用公钥加密,在编码为Base64字符串，设置key
3. 通过随机Key加密业务数据,设置content参数


#### 全局Header参数

参数名 | 数据类型 | 是否必须 | 示例值 | 描述 
:-- | :-- | :--|:-: |:-: 
timestamp|string|是| 1734615150364 | 时间戳 
nonce|string|否| xzWeqbhxJ99MRh4646GCwM2 | 商户订单号 随机字符串 
sign|string|是| xxx | 签名串,根据我们给哦公私钥生成 
partnerId|string|是| 240717051555cm7k0eq700fm | 商户号【由E出行在上线前分配】 


## /吉洛服务商数据同步
#### 接口URL
> /e-travel/e-travel/openapi/jiluo/provider/register-sync

#### 请求方式
> POST

#### Content-Type
> json

#### 实际请求参数描述

| 参数名    | 数据类型 | 是否必须 | 示例值                   | 描述                                                         |
| --------- | -------- | -------- | ------------------------ | ------------------------------------------------------------ |
| key       | string   | 是       | 随机key                  | rsa私钥加密后的秘钥串，用于敏感信息AES加解密及报文RSA加解签，参数放在URL |
| content   | string   | 是       | 2024-12-19 21:24:56      | 业务参数json序列化后的值，参数放在URL                        |
| timestamp | string   | 是       | 1734615150364            | 时间戳，参数放在URL                                          |
| nonce     | string   | 否       | xzWeqbhxJ99MRh4646GCwM2  | 商户订单号 随机字符串，参数放在URL                           |
| partnerId | string   | 是       | 240717051555cm7k0eq700fm | 商户号【由E出行在上线前分配】，参数放在URL                   |



#### 业务请求Body参数描述

| 参数名         | 数据类型 | 是否必须 | 示例值              | 描述                                                         |
| -------------- | -------- | -------- | ------------------- | ------------------------------------------------------------ |
| departmentName | string   | 是       | 技术部              | 部门名称                                                     |
| birthday       | date     | 否       | 2024-12-19 21:24:56 | 出生日期                                                     |
| country        | string   | 否       | 中国                | 国家                                                         |
| orgName        | string   | 是       | A公司               | 机构名称                                                     |
| loginPwd       | string   | 是       | 123456              | 登录密码                                                     |
| gender         | int      | 否       | 0                   | 性别：0-未知，1-男，2-女                                     |
| mail           | string   | 是       | <EMAIL>         | 邮箱                                                         |
| accountName    | string   | 是       | dufu                | 登录名                                                       |
| phone          | string   | 是       | 123xx12             | 手机号                                                       |
| busiType       | string   | 是       | VEHICLE_XL          | 服务商类型 VEHICLE_XL:车辆选型用户,VEHICLE_WX:车辆维修用户   |
| status         | string   | 是       | normal              | 状态：正常=normal,冻结=freeze,禁用=disable,未激活=inactive，锁定=locked |

#### 业务请求Body参数

```javascript
{
	"departmentName": "123456",
	"birthday": "2024-12-19 21:24:56",
	"country": "123456",
	"orgName": "123456",
	"loginPwd": "123456",
	"gender": 0,
	"mail": "123456",
	"accountName": "123456",
	"phone": "123456",
	"busiType": "VEHICLE_XL",
	"status": "normal"
}
```


#### Body参数描述

| 参数名        | 数据类型 | 示例值                         |
| ------------- | -------- | ------------------------------ |
| resultCode    | string   | 0000：注册成功，9995：注册失败 |
| resultMessage | string   | 响应消息                       |
| success       | string   | 成功、失败                     |
| sign          | string   | 签名                           |
| nonce         | string   | 商户订单号 随机字符串          |
| timestamp     | string   | 时间戳                         |

#### 响应Body参数

```javascript
{
  "resultCode": "9995",
  "resultMessage": "用户已存在",
  "success": false,
  "sign": "xxxx",
  "nonce": "yOYFXqNr3AC929xzslWZ",
  "timestamp": "*************"
}
```

#### 