package com.westcatr.rd.car.business.car.pojo.dto;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CarYearInfoDto {

    @Schema(description = "全部年份信息")
    private List<Integer> yearInfos;

    @Schema(description = "品牌车型")
    private String brandModel;

    @Schema(description = "logourl")
    private String logoUrl;

    @Schema(description = "能源类型1")
    private String energyType;

    @Schema(description = "最大马力(Ps)3")
    private String maxHorsepowerPs;

    @Schema(description = "CLTC纯电续航里程(km)1")
    private String basicCltcRange;

    @Schema(description = "车辆级别")
    private String basicCategory;

    @Schema(description = "指导价（低）")
    private String guidePrice;

    /*
     * @Schema(description = "指导价（高）")
     * private Double heightGuidePrice;
     */
    @Schema(description = "最高车速")
    private Double basicTopSpeed;
}
