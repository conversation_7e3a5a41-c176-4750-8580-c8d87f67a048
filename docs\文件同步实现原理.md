# 🌐 文件同步实现原理详解 ✨

## 📋 整体架构

系统设计了一种通过数据库作为中介来实现 OBS（华为云对象存储）和 OSS（阿里云对象存储）之间文件同步的方案。这种方案适用于内网和外网之间只有数据库连接而无法直接传输文件的场景。

### 🏗️ 核心组件

1. **文件存储服务** ：支持本地存储、OBS和OSS三种类型
2. **文件同步服务** ：负责文件分块和状态管理
3. **定时同步任务** ：定期检查并执行文件同步
4. **数据库表** ：存储文件元数据和文件块数据

## 💾 数据库设计

系统使用了两张核心表：

### 📊 synced_files 表
```sql
CREATE TABLE synced_files (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    file_path VARCHAR(1024) NOT NULL,
    original_storage VARCHAR(10) NOT NULL, -- 'OBS' 或 'OSS'
    file_size BIGINT NOT NULL,
    file_hash VARCHAR(64) NOT NULL,
    chunk_size INT NOT NULL,
    total_chunks INT NOT NULL,
    obs_sync_status VARCHAR(20) NOT NULL,
    oss_sync_status VARCHAR(20) NOT NULL,
    last_error TEXT,
    retry_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 📊 file_sync_chunks 表
```sql
CREATE TABLE file_sync_chunks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    file_id BIGINT NOT NULL,
    chunk_index INT NOT NULL,
    chunk_data LONGBLOB NOT NULL,
    chunk_hash VARCHAR(64),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (file_id) REFERENCES synced_files(id),
    INDEX (file_id, chunk_index)
);
```

## 🔄 同步流程详解

### 1️⃣ 上传文件时的同步处理

当用户上传文件时，系统会：

1. **文件上传** ：首先将文件上传到当前环境的存储服务（OBS或OSS）
2. **创建同步记录** ：在`synced_files`表中创建记录，设置原始存储为当前环境
3. **文件分块** ：将文件分割成固定大小的块（由`chunk_size`配置决定，默认4MB）
4. **保存文件块** ：每个块被保存到`file_sync_chunks`表的`chunk_data`字段中
5. **更新同步状态** ：更新`synced_files`表中的状态为`PENDING_DOWNLOAD`

这一过程由`FileSyncAspect`或`StorageManager`中的代码触发，例如：

```java
// 创建同步记录
Long fileId = syncedFileService.createSyncRecord(file, filePath, currentStorage);
// 分块处理文件
processFileChunksForSync(file, fileId);
// 更新同步状态
syncedFileService.updateSyncStatus(fileId, currentStorage, FileSyncConstant.SYNC_STATUS_PENDING_DOWNLOAD);
```

### 2️⃣ 定时同步任务

系统使用Spring的`@Scheduled`注解创建定时任务，分别对应OBS和OSS的同步：

```java
@Scheduled(fixedDelayString = "${file.storage.sync-interval:60000}")
public void syncToObsTask() {
    if (!properties.isEnabled() || !FileSyncConstant.ENV_OBS.equals(properties.getEnvironment())) {
        return;
    }

    //log.info("🔄 开始执行同步到OBS的任务");
    List<SyncedFile> pendingFiles = syncedFileService.getPendingSyncFiles(FileSyncConstant.STORAGE_TYPE_OBS);
    //log.info("📂 找到 {} 个待同步到OBS的文件", pendingFiles.size());

    for (SyncedFile fileInfo : pendingFiles) {
        processSyncToObs(fileInfo);
    }
}

@Scheduled(fixedDelayString = "${file.storage.sync-interval:60000}")
public void syncToOssTask() {
    if (!properties.isEnabled() || !FileSyncConstant.ENV_OSS.equals(properties.getEnvironment())) {
        return;
    }

    //log.info("🔄 开始执行同步到OSS的任务");
    List<SyncedFile> pendingFiles = syncedFileService.getPendingSyncFiles(FileSyncConstant.STORAGE_TYPE_OSS);
    //log.info("📂 找到 {} 个待同步到OSS的文件", pendingFiles.size());

    for (SyncedFile fileInfo : pendingFiles) {
        processSyncToOss(fileInfo);
    }
}
```

定时任务的执行周期由`file.storage.sync-interval`配置决定，默认为60秒。

### 3️⃣ 同步处理流程

当定时任务执行时：

1. **查询待同步文件** ：从`synced_files`表中查询状态为`PENDING_DOWNLOAD`的文件
2. **更新状态** ：将状态更新为`DOWNLOADING`，防止并发处理
3. **获取文件块** ：从`file_sync_chunks`表中获取所有文件块，按`chunk_index`排序
4. **创建临时文件** ：将所有文件块写入临时文件
5. **上传到目标存储** ：将临时文件上传到目标存储服务（OBS或OSS）
6. **验证文件** ：验证上传后的文件哈希值
7. **更新状态并清理** ：如果验证成功，将状态更新为`SYNCED`并删除`file_sync_chunks`中的数据

具体示例代码：

```java
private void processSyncToOss(SyncedFile fileInfo) {
    try {
        // 标记为正在处理
        syncedFileService.updateSyncStatus(fileInfo.getId(), FileSyncConstant.STORAGE_TYPE_OSS,
                FileSyncConstant.SYNC_STATUS_DOWNLOADING);

        log.info("⬇️ 开始下载文件块: {}", fileInfo.getFilePath());
        // 获取文件块
        List<FileChunk> chunks = fileChunkService.getFileChunks(fileInfo.getId());

        // 创建临时文件
        File tempFile = File.createTempFile("oss_sync_", ".tmp");
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            for (FileChunk chunk : chunks) {
                fos.write(chunk.getChunkData());
            }
        }
        log.info("✅ 文件块下载完成，临时文件路径: {}", tempFile.getAbsolutePath());

        // 调用OSS上传接口
        // ossService.uploadFile(tempFile, fileInfo.getFilePath());

        // 验证文件
        boolean verified = verifyFile(fileInfo.getFilePath(), fileInfo.getFileHash());

        if (verified) {
            // 更新状态并清理
            syncedFileService.updateSyncStatus(fileInfo.getId(), FileSyncConstant.STORAGE_TYPE_OSS,
                    FileSyncConstant.SYNC_STATUS_SYNCED);
            fileChunkService.deleteFileChunks(fileInfo.getId());
            log.info("✅ 文件同步到OSS成功: {}", fileInfo.getFilePath());
        } else {
            throw new Exception("文件验证失败");
        }
    } catch (Exception e) {
        log.error("❌ 同步到OSS失败: {}", fileInfo.getFilePath(), e);
        syncedFileService.updateSyncStatusWithError(fileInfo.getId(), FileSyncConstant.STORAGE_TYPE_OSS,
                FileSyncConstant.SYNC_STATUS_FAILED, e.getMessage());
    }
}
```

### 4️⃣ 错误处理与重试

如果同步过程中发生错误：

1. **记录错误信息** ：将错误信息记录到`last_error`字段
2. **更新状态** ：将状态更新为`FAILED`
3. **增加重试次数** ：`retry_count`字段加1
4. **保留文件块** ：不删除文件块数据，等待下次重试
5. **重试限制** ：系统会限制最大重试次数，默认为3次

## 🛠️ 关键配置参数

系统提供了多项配置参数，在`application.properties`中定义：

```properties
# 文件存储类型: LOCAL(本地), OSS(阿里云), OBS(华为云)
file.storage.type=OBS
# 存储模式: SINGLE(单一存储), HYBRID(混合存储)
file.storage.mode=SINGLE
# 是否启用存储切面(需要开启)
file.storage.aspect-enabled=true

# 文件同步相关增强配置
file.storage.chunk-size=4194304
file.storage.sync-interval=60000
file.storage.max-retry-count=3
file.storage.chunk-retention-hours=24
file.storage.storage-first=true
```

## 🔀 同步状态流转图

```
[用户上传文件到OBS]
      ↓
[创建同步记录]
      ↓
[文件分块存入数据库]
      ↓
[OBS状态=SYNCED, OSS状态=PENDING_DOWNLOAD]
      ↓
[OSS环境定时任务检测]
      ↓
[状态更新为DOWNLOADING]
      ↓
[从数据库读取文件块]
      ↓
[重组并上传到OSS]
      ↓
[验证成功] → [OSS状态=SYNCED, 清理文件块]
      ↓
[同步完成]
```

## 🌟 这种同步方式的特点与挑战

### 优点 ✅
- 允许内外网环境之间通过共享数据库进行文件同步
- 支持大文件的分块传输
- 自动重试机制提高可靠性
- 通过哈希值校验保证文件完整性
- 透明的处理过程，不影响用户正常操作

### 挑战 ⚠️
- **数据库压力大** ：将文件内容存储在数据库中会对数据库造成很大压力
- **性能开销** ：BLOB类型存储和读取效率较低
- **存储空间** ：临时占用大量数据库空间
- **事务复杂性** ：大文件处理时需要特别注意事务管理
- **清理机制** ：需要定期清理失败或过期的文件块数据

## 🔐 总结

整个文件同步方案是一种以数据库为中介的"桥接"设计，主要应对内外网隔离情况下的文件交换需求。系统通过文件分块、状态管理和定时任务实现了一种可靠的文件同步机制，但需要注意其对数据库的性能影响。

此方案可以满足在特定网络环境下OBS和OSS之间的文件同步需求，但应当留意数据库的性能监控，并根据实际情况调整配置参数，如分块大小、同步间隔等。 