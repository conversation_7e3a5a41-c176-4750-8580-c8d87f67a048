<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.car.business.demand.mapper.DemandApplicationPhoneInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.car.business.demand.entity.DemandApplicationPhoneInfo">
        <id column="id" property="id" />
        <result column="demand_id" property="demandId" />
        <result column="supplier_id" property="supplierId" />
        <result column="title_info" property="titleInfo" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, demand_id, supplier_id, title_info, create_time, update_time
    </sql>

</mapper>
