package com.westcatr.rd.car.business.demand.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.car.business.demand.entity.DemandUsedCarApplicationPhoneInfo;
import com.westcatr.rd.car.business.supplier.entity.SupplierInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "二手车供应商申请获取用户电话信息VO")
public class DemandUsedCarApplicationPhoneInfoVO extends DemandUsedCarApplicationPhoneInfo {

    @TableField(exist = false)
    @Schema(description = "供应商信息")
    @JoinSelect(joinClass = SupplierInfo.class, mainId = "supplierId")
    private SupplierInfo supplierInfo;

    @TableField(exist = false)
    @Schema(description = "需求信息")
    private DemandUsedCarInfoVO demandInfo;
}
