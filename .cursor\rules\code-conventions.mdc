---
description:
globs:
alwaysApply: false
---
# 代码规范与开发约定

## Java 后端开发规范

### 命名规范
- **类名**: 使用 PascalCase，如 `UserService`
- **方法名**: 使用 camelCase，如 `getUserById`
- **变量名**: 使用 camelCase，如 `userList`
- **常量**: 全大写+下划线，如 `MAX_RETRY_COUNT`
- **包名**: 全小写，如 `com.westcatr.rd.car.business.car`

### 代码结构
- 遵循三层架构: Controller -> Service -> Mapper
- Controller 只负责请求参数校验和结果返回
- Service 层包含主要业务逻辑
- Mapper 层处理数据库操作

### 注释规范
- 类注释：描述类的作用
- 方法注释：描述方法功能、参数和返回值
- 复杂逻辑必须添加注释说明

## 前端开发规范 (e-admin)

### React 组件规范
- 使用函数式组件和 Hooks
- 组件名使用 PascalCase
- props 使用 camelCase
- 每个组件放在单独文件中

### 样式规范
- 优先使用 Ant Design 组件库
- 自定义样式采用 Less 模块化
- 避免使用内联样式
- 使用变量管理主题色

## 前端开发规范 (e-app)

### Vue 组件规范
- 组件名使用 PascalCase
- props 定义必须包含类型和默认值
- 使用 Vue 3 组合式 API (Composition API)
- 复用逻辑提取到 composables

### 样式规范
- 使用 SCSS 预处理器
- 遵循 BEM 命名方式
- 公共样式放在 styles 目录

## API 规范

### RESTful API 设计
- GET: 查询资源
- POST: 创建资源
- PUT: 更新资源
- DELETE: 删除资源

### 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

### 错误处理
- 使用 HTTP 状态码表示请求状态
- 错误信息必须明确、具体

## 数据库规范

### 表命名
- 使用下划线命名法
- 表名前缀表示模块，如 `car_model`

### 字段命名
- 使用下划线命名法
- id 作为主键
- 创建和更新时间字段: `create_time` 和 `update_time`
- 删除标记字段: `is_deleted`
