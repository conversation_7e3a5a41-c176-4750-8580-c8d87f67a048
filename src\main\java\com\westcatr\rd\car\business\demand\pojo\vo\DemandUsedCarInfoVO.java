package com.westcatr.rd.car.business.demand.pojo.vo;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.car.business.demand.entity.DemandUsedCarInfo;
import com.westcatr.rd.car.business.org.entity.OrgUserInfo;
import com.westcatr.rd.car.business.supplier.entity.SupplierInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "二手车需求信息VO")
public class DemandUsedCarInfoVO extends DemandUsedCarInfo {

    @JoinSelect(joinClass = OrgUserInfo.class, mainId = "createUserId", field = "full_name")
    private String createUserFullName;

    @TableField(exist = false)
    @Schema(description = "是否显示获取手机号码的按钮")
    private boolean tfShowGetPhone = true;

    @TableField(exist = false)
    @JoinSelect(joinClass = DemandUsedCarApplicationPhoneInfoVO.class, relationId = "demand_id")
    private List<DemandUsedCarApplicationPhoneInfoVO> demandApplicationPhoneInfos;

    @JoinSelect(joinClass = SupplierInfo.class, mainId = "supplierId")
    private SupplierInfo supplierInfo;

    @TableField(exist = false)
    private List<SupplierInfo> supplierInfosByCrateUser;
}
