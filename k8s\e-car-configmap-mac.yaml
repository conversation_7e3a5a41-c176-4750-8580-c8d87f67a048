apiVersion: v1
kind: ConfigMap
metadata:
  name: e-car-config-mac
  namespace: default
data:
  # 应用基础配置
  server.port: "7331"
  logging.file.path: "/app/logs"
  westcatr.boot.file.upload-folder: "/app/uploads"
  download.prefix: "http://************:7330"
  
  # 数据库配置
  spring.datasource.driver-class-name: "com.mysql.cj.jdbc.Driver"
  spring.datasource.url: "*****************************************************************"
  spring.datasource.username: "root"
  spring.datasource.password: "Ls13548274447"
  
  # Redis配置
  westcatr.boot.cache.enable-redis: "true"
  spring.redis.database: "3"
  spring.redis.host: "***************"
  spring.redis.password: "123456"
  spring.redis.port: "6379"
  spring.redis.timeout: "5000"
  spring.redis.jedis.pool.max-active: "16"
  spring.redis.jedis.pool.max-idle: "16"
  spring.redis.jedis.pool.max-wait: "5000"
  spring.redis.jedis.pool.min-idle: "1"
  
  # 安全配置
  westcatr.boot.security.encryption: "true"
  
  # 自动配置排除
  spring.autoconfigure.exclude: "org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration"
  
  # 文件存储配置
  file.storage.type: "LOCAL"
  file.storage.mode: "SINGLE"
  file.storage.aspect-enabled: "false"
  file.storage.chunk-size: "4194304"
  file.storage.sync-interval: "60000"
  file.storage.max-retry-count: "3"
  file.storage.chunk-retention-hours: "24"
  file.storage.storage-first: "true"
  
  # 华为云OBS配置
  huaweicloud.obs.end-point: "obs.cn-southwest-2.myhuaweicloud.com"
  huaweicloud.obs.ak: "HPUAMVWS2SVNYZ4K01UT"
  huaweicloud.obs.sk: "wQyQU4IF95iemXwPhnY9ps6282C1vJGhFnRZvgw8"
  huaweicloud.obs.bucket-name: "lsobs"
  huaweicloud.obs.domain: "https://lsobs.obs.cn-southwest-2.myhuaweicloud.com"
  
  # 阿里云OSS配置
  aliyun.oss.endpoint: "https://oss-cn-chengdu.aliyuncs.com"
  aliyun.oss.access-key-id: "LTAI5tMPd4K8YLEEfePAvvEu"
  aliyun.oss.access-key-secret: "******************************"
  aliyun.oss.bucket-name: "lsprojectinfo"
  aliyun.oss.domain: "https://lsprojectinfo.oss-cn-chengdu.aliyuncs.com"
  
  # 日志级别配置 - 减少重复日志输出
  logging.level.com.westcatr.rd.car.business.openapi.controller.FanFanApiController: "WARN"
  logging.level.com.westcatr.rd.car.business.fanfan.impl: "WARN"
  logging.level.com.westcatr.rd.car.business.fanfan: "WARN"
  logging.level.org.springframework.scheduling: "WARN"