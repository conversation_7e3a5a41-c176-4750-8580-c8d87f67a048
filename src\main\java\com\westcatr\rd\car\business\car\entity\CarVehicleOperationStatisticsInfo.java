package com.westcatr.rd.car.business.car.entity;

import java.io.Serializable;
import java.util.Date;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 车型配件表—操作统计
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("car_vehicle_operation_statistics_info")
@Schema(description = "车型配件表—操作统计")
public class CarVehicleOperationStatisticsInfo extends Model<CarVehicleOperationStatisticsInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    // excel 导出时，不显示
    @ExcelIgnore
    private Long id;

    @Schema(description = "操作类型")
    @ExcelProperty(value = "操作类型")
    @Length(max = 255, message = "操作类型长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("operation_type")
    private String operationType;

    @Schema(description = "操作项")
    @ExcelProperty("操作项")
    @Length(max = 255, message = "操作项长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("operation_item")
    private String operationItem;

    @Schema(description = "涉及车型")
    @ExcelProperty("涉及车型")
    @Length(max = 255, message = "涉及车型长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("involved_vehicle_model")
    private String involvedVehicleModel;

    @Schema(description = "操作人")
    @ExcelProperty("操作人")
    @Length(max = 255, message = "操作人长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("operator_full_name")
    private String operatorFullName;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @ExcelProperty("发生时间")
    private Date createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @ExcelIgnore
    private Date updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
