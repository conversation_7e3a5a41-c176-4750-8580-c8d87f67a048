package com.westcatr.rd.car.business.car.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.car.business.car.pojo.query.CarBasicInfoQuery;
import com.westcatr.rd.car.business.car.entity.CarBasicInfo;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * <p>
 * 车辆—车辆基本信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
public interface CarBasicInfoService extends IService<CarBasicInfo> {

    IPage<CarBasicInfo> entityPage(CarBasicInfoQuery query);

    CarBasicInfo getEntityById(Long id);

    boolean saveEntity(CarBasicInfo param);

    boolean updateEntity(CarBasicInfo param);

    boolean removeEntityById(Long id);
}
