package com.westcatr.rd.car.business.car.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 车辆—基础信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("car_info")
@Schema(description = "车辆—基础信息表")
public class CarInfo extends Model<CarInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField(value = "logo_url")
    private String logoUrl;

    @TableField(value = "car_basic_id")
    private Long carBasicId;

    @Schema(description = "品牌与车型")
    @Length(max = 100, message = "品牌与车型长度不能超过100")
    @TableField("brand_model")
    private String brandModel;

    @Schema(description = "车辆简介")
    @Length(max = 2000, message = "车辆简介长度不能超过2000")
    @TableField("introduction")
    private String introduction;

    @Schema(description = "车辆介绍")
    @Length(max = 65535, message = "车辆介绍长度不能超过65535")
    @TableField("description")
    @Size(max = 2000, message = "车辆介绍字符长度不能超过2000")
    private String description;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Schema(description = "视频标题")
    @Length(max = 255, message = "视频标题长度不能超过255")
    @TableField("video_title")
    private String videoTitle;

    @Schema(description = "车辆图片")
    @Length(max = 255, message = "车辆图片长度不能超过255")
    @TableField("car_img_url")
    private String carImgUrl;

    @Schema(description = "车辆推广图")
    @Length(max = 255, message = "车辆推广图长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("car_promotion_img_url")
    private String carPromotionImgUrl;

    @Schema(description = "车辆视频")
    @Length(max = 255, message = "车辆视频长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("car_video_url")
    private String carVideoUrl;

    @Schema(description = "状态，（草稿中=0、已驳回=1、待提交=2、审核中=3、4=已通过）")
    @TableField("status_info")
    private Integer statusInfo;

    @Schema(description = "试驾评分")
    @TableField("test_drive_score_info")
    private Double testDriveScoreInfo;

    @Schema(description = "供应商id")
    @TableField("supplier_id")
    private Long supplierId;

    @TableField(exist = false)
    @Schema(description = "批量新增车型")
    private List<CarModelInfo> addCarModelInfos;

    @TableField(exist = false)
    @Schema(description = "批量新增车辆参数")
    private CarParameterInfo addCarParameterInfo;

    @TableField(exist = false)
    private Map<String, Object> newAddCarParameterInfo;

    @Schema(description = "上架状态(1上架、0下架)")
    @TableField("listing_status")
    private Integer listingStatus;

    @Schema(description = "流程id")
    @TableField("flow_id")
    private String flowId;

    @Schema(description = "草稿表单")
    @TableField("draft_json")
    private String draftJson;

    @Schema(description = "提交方式：表单提交、列表提交")
    @TableField(exist = false)
    private String submitType;

    @TableField(exist = false)
    @Schema(description = "当前需要审批的用户ids")
    private List<String> approvalUserIds;

    @TableField(exist = false)
    @Schema(description = "车辆模型，更新用")
    private Long carModelId;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
