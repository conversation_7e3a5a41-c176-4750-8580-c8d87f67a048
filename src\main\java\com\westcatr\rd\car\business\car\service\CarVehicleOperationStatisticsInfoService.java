package com.westcatr.rd.car.business.car.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.car.business.car.entity.CarVehicleOperationStatisticsInfo;
import com.westcatr.rd.car.business.car.pojo.dto.CarVehicleOperationStatisticsDto;
import com.westcatr.rd.car.business.car.pojo.query.CarVehicleOperationStatisticsInfoQuery;

/**
 * <p>
 * 车型配件表—操作统计 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface CarVehicleOperationStatisticsInfoService extends IService<CarVehicleOperationStatisticsInfo> {

    IPage<CarVehicleOperationStatisticsInfo> entityPage(CarVehicleOperationStatisticsInfoQuery query);

    CarVehicleOperationStatisticsInfo getEntityById(Long id);

    boolean saveEntity(CarVehicleOperationStatisticsInfo param);

    boolean updateEntity(CarVehicleOperationStatisticsInfo param);

    boolean removeEntityById(Long id);

    /**
     * 获取车型配件表—操作统计统计数据
     *
     * @param query 查询条件
     * @return 统计数据
     */
    CarVehicleOperationStatisticsDto getCarVehicleOperationStatisticsDto(CarVehicleOperationStatisticsInfoQuery query);

    /**
     * 导出Excel数据
     *
     * @param query 查询条件
     */
    void exportExcel(CarVehicleOperationStatisticsInfoQuery query);
}
