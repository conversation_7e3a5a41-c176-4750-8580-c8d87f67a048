package com.westcatr.rd.car.business.car.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.car.business.car.entity.CarBasicInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 车辆—车辆基本信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="车辆—车辆基本信息表VO对象")
public class CarBasicInfoVO extends CarBasicInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
