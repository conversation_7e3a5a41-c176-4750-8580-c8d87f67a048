package com.westcatr.rd.car.business.car.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 车辆—参数信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("car_parameter_info")
@Schema(description = "车辆—参数信息表")
public class CarParameterInfo extends Model<CarParameterInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "关联车辆ID")
    @TableField("car_id")
    private Long carId;

    @Schema(description = "关联车辆ID")
    @TableField("car_model_id")
    private Long carModelId;

    @Schema(description = "参考价(元)")
    @TableField("reference_price")
    private BigDecimal referencePrice;

    @Schema(description = "优惠信息")
    @Length(max = 255, message = "优惠信息长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("discount_info")
    private String discountInfo;

    @Schema(description = "厂商指导价(元)")
    @TableField("manufacturer_price")
    private BigDecimal manufacturerPrice;

    @Schema(description = "厂商")
    @Length(max = 100, message = "厂商长度不能超过100", groups = { Insert.class, Update.class })
    @TableField("manufacturer")
    private String manufacturer;

    @Schema(description = "级别")
    @Length(max = 50, message = "级别长度不能超过50", groups = { Insert.class, Update.class })
    @TableField("vehicle_level")
    private String vehicleLevel;

    @Schema(description = "能源类型")
    @Length(max = 50, message = "能源类型长度不能超过50", groups = { Insert.class, Update.class })
    @TableField("energy_type")
    private String energyType;

    @Schema(description = "环保标准")
    @Length(max = 50, message = "环保标准长度不能超过50", groups = { Insert.class, Update.class })
    @TableField("emission_standard")
    private String emissionStandard;

    @Schema(description = "上市时间")
    @TableField("release_date")
    private Date releaseDate;

    @Schema(description = "WLTC纯电续航里程(km)")
    @TableField("wltc_range")
    private Integer wltcRange;

    @Schema(description = "CLTC纯电续航里程(km)")
    @TableField("cltc_range")
    private Integer cltcRange;

    @Schema(description = "电池快充时间(小时)")
    @TableField("fast_charge_time")
    private BigDecimal fastChargeTime;

    @Schema(description = "电池慢充时间(小时)")
    @TableField("slow_charge_time")
    private BigDecimal slowChargeTime;

    @Schema(description = "电池快充电量范围(%)")
    @Length(max = 20, message = "电池快充电量范围(%)长度不能超过20", groups = { Insert.class, Update.class })
    @TableField("fast_charge_range")
    private String fastChargeRange;

    @Schema(description = "最大功率(kW)")
    @TableField("max_power")
    private Integer maxPower;

    @Schema(description = "最大扭矩(N·m)")
    @TableField("max_torque")
    private Integer maxTorque;

    @Schema(description = "变速箱")
    @Length(max = 50, message = "变速箱长度不能超过50", groups = { Insert.class, Update.class })
    @TableField("transmission")
    private String transmission;

    @Schema(description = "车身结构")
    @Length(max = 50, message = "车身结构长度不能超过50", groups = { Insert.class, Update.class })
    @TableField("body_structure")
    private String bodyStructure;

    @Schema(description = "发动机")
    @Length(max = 255, message = "发动机长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("engine")
    private String engine;

    @Schema(description = "电动机(Ps)")
    @TableField("electric_motor_ps")
    private Integer electricMotorPs;

    @Schema(description = "长*宽*高(mm)")
    @Length(max = 50, message = "长*宽*高(mm)长度不能超过50", groups = { Insert.class, Update.class })
    @TableField("dimensions")
    private String dimensions;

    @Schema(description = "官方0-100km/h加速(s)")
    @TableField("acceleration")
    private BigDecimal acceleration;

    @Schema(description = "最高车速(km/h)")
    @TableField("max_speed")
    private Integer maxSpeed;

    @Schema(description = "WLTC综合油耗(L/100km)")
    @TableField("wltc_fuel_consumption")
    private BigDecimal wltcFuelConsumption;

    @Schema(description = "最低荷电状态油耗(L/100km)")
    @TableField("min_charge_state_fuel_consumption")
    private BigDecimal minChargeStateFuelConsumption;

    @Schema(description = "整车质保")
    @Length(max = 255, message = "整车质保长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("warranty")
    private String warranty;

    @Schema(description = "整备质量(kg)")
    @TableField("curb_weight")
    private Integer curbWeight;

    @Schema(description = "最大满载质量(kg)")
    @TableField("max_payload")
    private Integer maxPayload;

    @Schema(description = "准拖挂车总质量(kg)")
    @TableField("max_towing_mass")
    private Integer maxTowingMass;

    @Schema(description = "长度(mm)")
    @TableField("length")
    private Integer length;

    @Schema(description = "宽度(mm)")
    @TableField("width")
    private Integer width;

    @Schema(description = "高度(mm)")
    @TableField("height")
    private Integer height;

    @Schema(description = "轴距(mm)")
    @TableField("wheelbase")
    private Integer wheelbase;

    @Schema(description = "前轮距(mm)")
    @TableField("front_track")
    private Integer frontTrack;

    @Schema(description = "后轮距(mm)")
    @TableField("rear_track")
    private Integer rearTrack;

    @Schema(description = "满载最小离地间隙(mm)")
    @TableField("ground_clearance")
    private BigDecimal groundClearance;

    @Schema(description = "接近角(°)")
    @TableField("approach_angle")
    private BigDecimal approachAngle;

    @Schema(description = "离去角(°)")
    @TableField("departure_angle")
    private BigDecimal departureAngle;

    @Schema(description = "纵向通过角(°)")
    @TableField("ramp_breakover_angle")
    private BigDecimal rampBreakoverAngle;

    @Schema(description = "最大爬坡角度(°)")
    @TableField("max_climb_angle")
    private BigDecimal maxClimbAngle;

    @Schema(description = "最小转弯半径(m)")
    @TableField("turning_radius")
    private BigDecimal turningRadius;

    @Schema(description = "最大涉水深度(mm)")
    @TableField("max_wading_depth")
    private Integer maxWadingDepth;

    @Schema(description = "油箱容积(L)")
    @TableField("fuel_tank_capacity")
    private BigDecimal fuelTankCapacity;

    @Schema(description = "前备厢容积(L)")
    @TableField("front_trunk_volume")
    private BigDecimal frontTrunkVolume;

    @Schema(description = "后备厢容积(L)")
    @TableField("rear_trunk_volume")
    private BigDecimal rearTrunkVolume;

    @Schema(description = "风阻系数(Cd)")
    @TableField("drag_coefficient")
    private BigDecimal dragCoefficient;

    @Schema(description = "发动机型号")
    @Length(max = 255, message = "发动机型号长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("engine_model")
    private String engineModel;

    @Schema(description = "排量(mL)")
    @TableField("displacement_ml")
    private Integer displacementMl;

    @Schema(description = "排量(L)")
    @TableField("displacement_l")
    private BigDecimal displacementL;

    @Schema(description = "进气形式")
    @Length(max = 50, message = "进气形式长度不能超过50", groups = { Insert.class, Update.class })
    @TableField("intake_form")
    private String intakeForm;

    @Schema(description = "发动机布局")
    @Length(max = 50, message = "发动机布局长度不能超过50", groups = { Insert.class, Update.class })
    @TableField("engine_layout")
    private String engineLayout;

    @Schema(description = "气缸排列形式")
    @Length(max = 50, message = "气缸排列形式长度不能超过50", groups = { Insert.class, Update.class })
    @TableField("cylinder_arrangement")
    private String cylinderArrangement;

    @Schema(description = "气缸数(个)")
    @TableField("cylinder_count")
    private Integer cylinderCount;

    @Schema(description = "每缸气门数(个)")
    @TableField("valve_per_cylinder")
    private Integer valvePerCylinder;

    @Schema(description = "配气机构")
    @Length(max = 50, message = "配气机构长度不能超过50", groups = { Insert.class, Update.class })
    @TableField("valve_mechanism")
    private String valveMechanism;

    @Schema(description = "最大马力(Ps)")
    @TableField("max_horsepower")
    private Integer maxHorsepower;

    @Schema(description = "最大功率转速(rpm)")
    @TableField("max_rpm")
    private Integer maxRpm;

    @Schema(description = "最大扭矩转速(rpm)")
    @TableField("torque_rpm")
    private Integer torqueRpm;

    @Schema(description = "燃油标号")
    @Length(max = 50, message = "燃油标号长度不能超过50", groups = { Insert.class, Update.class })
    @TableField("fuel_label")
    private String fuelLabel;

    @Schema(description = "供油方式")
    @Length(max = 50, message = "供油方式长度不能超过50", groups = { Insert.class, Update.class })
    @TableField("fuel_supply_mode")
    private String fuelSupplyMode;

    @Schema(description = "电池能量(kWh)")
    @TableField("battery_capacity")
    private BigDecimal batteryCapacity;

    @Schema(description = "电池能量密度(Wh/kg)")
    @TableField("battery_density")
    private BigDecimal batteryDensity;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
