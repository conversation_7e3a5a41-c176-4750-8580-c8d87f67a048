<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.car.business.demand.mapper.DemandInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.car.business.demand.entity.DemandInfo">
        <id column="id" property="id" />
        <result column="create_user_id" property="createUserId" />
        <result column="demand_title" property="demandTitle" />
        <result column="description_info" property="descriptionInfo" />
        <result column="car_id" property="carId" />
        <result column="contact_phone" property="contactPhone" />
        <result column="status_info" property="statusInfo" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_user_id, demand_title, description_info, car_id, contact_phone, status_info, create_time, update_time
    </sql>

</mapper>
