package com.westcatr.rd.car.business.basic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.car.business.basic.pojo.query.BasicComplaintConfigInfoQuery;
import com.westcatr.rd.car.business.basic.entity.BasicComplaintConfigInfo;
import com.baomidou.mybatisplus.extension.service.IService;
/**
 * <p>
 * 投诉配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public interface BasicComplaintConfigInfoService extends IService<BasicComplaintConfigInfo> {

    IPage<BasicComplaintConfigInfo> entityPage(BasicComplaintConfigInfoQuery query);

    BasicComplaintConfigInfo getEntityById(Long id);

    boolean saveEntity(BasicComplaintConfigInfo param);

    boolean updateEntity(BasicComplaintConfigInfo param);

    boolean removeEntityById(Long id);
}
