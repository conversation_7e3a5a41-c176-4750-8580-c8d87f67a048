package com.westcatr.rd.car.business.demand.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.car.business.demand.entity.DemandUsedCarInfo;
import com.westcatr.rd.car.business.demand.pojo.dto.GetPhoneNumberReturnDto;
import com.westcatr.rd.car.business.demand.pojo.dto.GetUserPhoneAuditDto;
import com.westcatr.rd.car.business.demand.pojo.query.DemandUsedCarApplicationPhoneInfoQuery;
import com.westcatr.rd.car.business.demand.pojo.query.DemandUsedCarInfoQuery;
import com.westcatr.rd.car.business.demand.pojo.vo.DemandUsedCarApplicationPhoneInfoVO;
import com.westcatr.rd.car.business.demand.pojo.vo.DemandUsedCarInfoVO;

/**
 * <p>
 * 二手车需求信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
public interface DemandUsedCarInfoService extends IService<DemandUsedCarInfo> {

    IPage<DemandUsedCarInfo> entityPage(DemandUsedCarInfoQuery query);

    DemandUsedCarInfo getEntityById(Long id);

    boolean saveEntity(DemandUsedCarInfo param);

    boolean updateEntity(DemandUsedCarInfo param);

    boolean removeEntityById(Long id);

    GetPhoneNumberReturnDto mobilePhoneNumberTheUserNeed(Long id);

    boolean ignoreNeeds(Long id);

    boolean getUserPhoneNumber(GetUserPhoneAuditDto dto);

    IPage<DemandUsedCarApplicationPhoneInfoVO> getPhoneVoPage(DemandUsedCarApplicationPhoneInfoQuery query);

    /**
     * 获取VO分页数据
     *
     * @param query 查询条件
     * @return VO分页数据
     */
    IPage<DemandUsedCarInfoVO> myVoPage(DemandUsedCarInfoQuery query);

    /**
     * 我的发布需求
     *
     * @param query
     * @return
     */
    IPage<DemandUsedCarInfoVO> myPublishedRequirements(DemandUsedCarInfoQuery query);
}
