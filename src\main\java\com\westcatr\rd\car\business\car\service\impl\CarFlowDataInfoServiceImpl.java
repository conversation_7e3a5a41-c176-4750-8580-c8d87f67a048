package com.westcatr.rd.car.business.car.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.car.business.car.pojo.query.CarFlowDataInfoQuery;
import com.westcatr.rd.car.business.car.entity.CarFlowDataInfo;
import com.westcatr.rd.car.business.car.mapper.CarFlowDataInfoMapper;
import com.westcatr.rd.car.business.car.service.CarFlowDataInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Service
public class CarFlowDataInfoServiceImpl extends ServiceImpl<CarFlowDataInfoMapper, CarFlowDataInfo> implements CarFlowDataInfoService {

    @Override
    public IPage<CarFlowDataInfo> entityPage(CarFlowDataInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<CarFlowDataInfo>().create(query));
    }

    @Override
    public CarFlowDataInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(CarFlowDataInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(CarFlowDataInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }
}

