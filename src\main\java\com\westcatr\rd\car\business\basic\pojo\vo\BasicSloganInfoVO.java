package com.westcatr.rd.car.business.basic.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.car.business.basic.entity.BasicSloganInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 基础信息—奖品宣传语配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description="基础信息—奖品宣传语配置表VO对象")
public class BasicSloganInfoVO extends BasicSloganInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
