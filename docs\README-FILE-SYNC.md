# 文件多云存储同步功能说明

## 📂 功能概述

系统实现了云存储和文件同步功能，支持以下特性：

- ☁️ 支持多种云存储：阿里云OSS、华为云OBS和本地存储
- 🔄 自动同步文件到不同的云存储平台
- 🧩 文件分块存储，支持大文件的同步
- 📊 文件同步状态跟踪和异常处理
- 🔍 多种存储模式：单一存储、混合存储

## ⚙️ 配置说明

### 基本配置

在`application.properties`文件中进行配置：

```properties
# 文件存储类型: LOCAL(本地), OSS(阿里云), OBS(华为云)
file.storage.type=OBS

# 存储模式: SINGLE(单一存储), HYBRID(混合存储)
file.storage.mode=SINGLE

# 是否启用存储切面 - 同时处理文件上传到云存储和文件同步功能
file.storage.aspect-enabled=true

# 文件同步相关增强配置
file.storage.chunk-size=4194304
file.storage.sync-interval=60000
file.storage.max-retry-count=3
file.storage.chunk-retention-hours=24
file.storage.storage-first=true
```

### 存储服务配置

#### 华为云OBS配置

```properties
huaweicloud.obs.end-point=obs.cn-southwest-2.myhuaweicloud.com
huaweicloud.obs.ak=您的AccessKeyId
huaweicloud.obs.sk=您的SecretAccessKey
huaweicloud.obs.bucket-name=您的BucketName
huaweicloud.obs.domain=https://您的BucketName.obs.cn-southwest-2.myhuaweicloud.com
```

#### 阿里云OSS配置

```properties
aliyun.oss.endpoint=https://oss-cn-chengdu.aliyuncs.com
aliyun.oss.access-key-id=您的AccessKeyId
aliyun.oss.access-key-secret=您的AccessKeySecret
aliyun.oss.bucket-name=您的BucketName
aliyun.oss.domain=https://您的BucketName.oss-cn-chengdu.aliyuncs.com
```

## 🧮 存储模式说明

系统支持三种存储模式：

1. **本地存储模式**（`file.storage.mode=SINGLE`, `file.storage.type=LOCAL`）
   - 仅存储在本地文件系统中
   - 不进行文件同步

2. **直接存储模式**（`file.storage.mode=SINGLE`, `file.storage.type=OBS/OSS`）
   - 直接存储到指定的云存储平台
   - 根据配置创建同步记录

3. **混合存储模式**（`file.storage.mode=HYBRID`）
   - 同时存储在本地文件系统和指定的云存储平台
   - 本地文件访问更快，云存储用于备份和共享
   - 根据配置创建同步记录

## 🔄 同步机制

文件同步通过以下组件实现：

1. **FileStorageAspect**：拦截文件上传操作，处理云存储和同步
2. **SyncedFileService**：管理同步记录和状态
3. **FileChunkService**：处理文件分块存储
4. **FileSyncTask**：定期执行同步任务

## 🚨 注意事项

1. 文件同步功能依赖于`file.storage.aspect-enabled=true`配置
2. 同步任务以固定间隔运行，可通过`file.storage.sync-interval`调整间隔时间
3. 对于大文件，系统会自动进行分块处理，块大小可通过`file.storage.chunk-size`设置
4. 文件块会临时存储在数据库中，通过`file.storage.chunk-retention-hours`控制保留时间
5. 如果遇到同步失败，系统会自动重试，最大重试次数由`file.storage.max-retry-count`控制

## 🛠️ 故障排除

1. **检查配置**：确保云存储配置（AK/SK、endpoint等）正确
2. **查看日志**：同步过程的详细日志可帮助诊断问题
3. **数据库表**：检查`synced_files`和`file_sync_chunks`表中的记录状态
4. **重置同步**：对于状态异常的记录，可以通过更新`obs_sync_status`或`oss_sync_status`字段重置同步状态

## 📊 数据表结构

### synced_files（文件同步记录表）

| 字段名           | 类型       | 说明                     |
|-----------------|------------|-------------------------|
| id              | BIGINT     | 主键                     |
| file_path       | VARCHAR    | 文件路径                  |
| original_storage| VARCHAR    | 原始存储类型 (OBS/OSS)     |
| file_size       | BIGINT     | 文件大小                  |
| file_hash       | VARCHAR    | 文件哈希值                |
| chunk_size      | INT        | 分块大小                  |
| total_chunks    | INT        | 总块数                    |
| obs_sync_status | VARCHAR    | OBS同步状态               |
| oss_sync_status | VARCHAR    | OSS同步状态               |
| last_error      | TEXT       | 最后错误信息              |
| retry_count     | INT        | 重试次数                  |
| created_at      | TIMESTAMP  | 创建时间                  |
| updated_at      | TIMESTAMP  | 更新时间                  |

### file_sync_chunks（文件块存储表）

| 字段名        | 类型       | 说明           |
|--------------|------------|---------------|
| id           | BIGINT     | 主键           |
| file_id      | BIGINT     | 文件ID         |
| chunk_index  | INT        | 块索引         |
| chunk_data   | LONGBLOB   | 块数据         |
| chunk_hash   | VARCHAR    | 块哈希值       |
| created_at   | TIMESTAMP  | 创建时间       |
``` 