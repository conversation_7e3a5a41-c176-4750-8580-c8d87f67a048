apiVersion: v1
kind: ConfigMap
metadata:
  name: e-car-config-external
  namespace: default
data:
  # 应用基础配置
  SERVER_PORT: "8080"
  LOGGING_FILE_PATH: "/app/logs"
  WESTCATR_BOOT_FILE_UPLOAD_FOLDER: "/app/uploads"
  DOWNLOAD_PREFIX: "http://e-car-back-service:7330"
  
  # 数据库配置 - 外网环境（使用NDS数据库）
  SPRING_DATASOURCE_DRIVER_CLASS_NAME: "sgcc.nds.jdbc.driver.NdsDriver"
  SPRING_DATASOURCE_URL: "jdbc:nds://************:18701/v_18700_ecxyygkn4?appname=appecxyygkn4&allowMultiQueries=true&characterEncoding=utf8&autoReconnect=true&useSSL=false&zeroDateTimeBehavior=convertToNull&allowMultiQueries=true"
  SPRING_DATASOURCE_USERNAME: "root"
  SPRING_DATASOURCE_PASSWORD: "Yhblsqt@wyy888"
  
  # Redis配置 - 外网环境
  WESTCATR_BOOT_CACHE_ENABLE_REDIS: "true"
  SPRING_REDIS_DATABASE: "8"
  SPRING_REDIS_HOST: "redis"
  SPRING_REDIS_PASSWORD: "7Bk_3!z:2x^pQa~"
  SPRING_REDIS_PORT: "6379"
  SPRING_REDIS_TIMEOUT: "5000"
  SPRING_REDIS_JEDIS_POOL_MAX_ACTIVE: "16"
  SPRING_REDIS_JEDIS_POOL_MAX_IDLE: "16"
  SPRING_REDIS_JEDIS_POOL_MAX_WAIT: "5000"
  SPRING_REDIS_JEDIS_POOL_MIN_IDLE: "1"
  
  # 安全配置
  WESTCATR_BOOT_SECURITY_ENCRYPTION: "true"
  WESTCATR_BOOT_WEB_SHOW_EXCEPTION_DETAILS: "false"
  
  # E-Travel对接配置
  ETRAVEL_API_URL: "http://e-travel:16666/e-travel/openapi/jiluo"
  ETRAVEL_PARTNER_ID: "P0000000001"
  ETRAVEL_PARTNER_CONTEXT_PATH: "/e-travel"
  ETRAVEL_PUBLIC_KEY: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApIToWOWO9EarE7rqjwNbX8VskJz/WXwcIlfuWc3+fnX4pLkwieeC2YSAXzy5Nelm8buX1gOCpe8YSSGo+77Ou63eHGk9oLO5/WqqLvdqIJYG1SszOFmcH2ThwmTRW5ECFFtXRtH2XTGq6eRZ+YCICIAiEci9HBo/uNteiiwtnTye9vufQe24l4Cdp2y1PHYtLIMwYkvPHW3pNeR3i7gnPCLS+Y6Xq+CzUfD7CM13X8w9Y69GAenFtmDaUgtkmoievaP+soePduMjFM/bIBFM+wMdQC4lEcDN3eiU3slrifGrz/h7YsN9hwEaHbTxrLdLXg8q+F92crw+gwhNjCdGEwIDAQAB"
  ETRAVEL_PRIVATE_KEY: "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCb4z9BqWvA+410MLdo2kPOkkGQWhMXKLC/99l32cWuZn9p2ZnvrCsbWA4Mh9nCBLk8F48EQbjdxO6iSZ3lBfZj5Bf++BgGD2R7Aer0iC2G74mjiJ2vq9BdIHsw1e5RJq3Gx1e2kCwEBNlm6CtH6P6IU0MJZcU0Zf4J0+QtPVBFSRkb6Ch2YeaEOPKNnethEMHyfXXcQrjgyiYsEJjVLLTUjB629wHAlPu2uijoZ84S2sOfWmoEp0YnfbhqM/uIStZiWWuXIMUnkpSVQ2kw408YsxdWhLMRBpDuNLJYx6QiQZ86suJnCquSO1JxrHOCPywEKC1i+vkIMoADVfXbjHINAgMBAAECggEAKSqy+skd/qGvsl5nIRZ6z6OKEu/mmLgTLS7rzB3DeZHx1ec0IroJzDfNxfteQ0FyFW93Qj2E73PhPRRJ0UBo1+pVd+zWk0sfUuinvpnzw+2gIIKkec7awK0iOzxTqjSXsaXVAQ5DwW/yZZ2sSFo8VY3/ZwzsLjE9DCVb3aKSfykqAQjhGWTFwnQZSzqrzs4yKifKo8cZfSphwqo72eJPPVAWCXOxwsQnKtAbhNcJjfRgS7zqXxc562/YdQb2Dfc7HFQYD/GmVQjAFQeu+WxpNlHH/RaJ952qodbtiUgxabusBWbHCUDO5BNAdOiP+tWd7RONGr3833HIBZRCiMoooQKBgQD0a6/QNclqj1/cfho79SKyYFX+qFSvdBGjiXSBZYSOW/QLm4UqyYpy4fHvO8tvb/cbo5OKneXcLP2HB632K00VkQN6/glZ6Pkthm2CQLiejDs0/Z99W0uxQk1BAPMnZOx6z8uNddVMjPxMrmThE+N0U0us1uZdm2yeDc7BUQC/mQKBgQCjRdcDavnL6sMcBE2EeWe1Toby+K2Zp5f4OoF9CeM2M0KxFuSPnii5sE09/oTf5q5BIpZPVIIt5kx7kS9mR5GQx+F6nmu2dwsLpgKeCwXViC3F7y+gRFtjvTeE+72rDbHUqN562lfCfxVU5WOTCBv0POMn3dkAlDZ4Xt7YKpEelQKBgBDrt5MpdhUrcc546GYINWu14Z4slxhCZ8ozhN5EYeXdFcsL7C8nHq+PVsliEVZYoPnH3Dn3bmdMsgx8Cp4FJ2P70wrbtVgFGeL98GzZAC7xtFOZz0XFYTsXRPPgFfU/NmPWKNhVNPwEsp64YAF4+AEglxeTFQWb8ewLNqoJaozZAoGALPAuC9zHwB5vcSimBU49AyQH9JwJe/8qzsGbnkS4atSVCJTad8T6RJGH0QZaoB2n3HmekVsbMXLvnhsJxsbwA79gChXMY75EJgKdOc6i3nDK2G3K1/u4g60yuMFpJMlyJqwMABhUi03bGzC/xVo4B3MyumhPL9+bVmoWP70HkDkCgYBPjNHdTaDjDi4Tt15dHfhnzKGkDLNdhzsPaDAw/ysGoW5T7Sfdy5Kb2mwGNUGP20nVv4ZXZLdUOjWqTh+0SM3ycuKSYe9t8Lzo/8WBn3BNQyC4Ww/oIzUjf4zA3onWhgXV9TM5FHLRMxJ7a+4apZ2Xys/boEA7tFpEDA6JWapgNg=="
  
  # Dubbo配置
  DUBBO_APPLICATION_QOS_ENABLE: "false"
  DUBBO_REGISTRY_PARAMETERS_NAMESPACE: "etravel"
  DUBBO_REGISTRY_GROUP: "etravel"
  DUBBO_REGISTRY_REGISTER_MODE: "interface"
  DUBBO_REGISTRY_ADDRESS: "nacos://nacos:18848"
  DUBBO_PROTOCOL_NAME: "dubbo"
  DUBBO_PROTOCOL_PORT: "-1"
  
  # 文件存储配置
  FILE_STORAGE_TYPE: "OSS"
  FILE_STORAGE_ASPECT_ENABLED: "true"
  
  # 华为云OBS配置
  HUAWEICLOUD_OBS_END_POINT: "obsv3.cq-region-2.sgic.sgcc.com.cn"
  HUAWEICLOUD_OBS_AK: "EQKJHDLVQNTNBDIX6CUC"
  HUAWEICLOUD_OBS_SK: "LJEzNHv0gLfu6Uim89oj0lPZOMItiURg19xKt131"
  HUAWEICLOUD_OBS_BUCKET_NAME: "ecxyygkxt-production-obsv3-01"
  HUAWEICLOUD_OBS_DOMAIN: "https://ecxyygkxt-production-obsv3-01.obsv3.cq-region-2.sgic.sgcc.com.cn"
  
  # 阿里云OSS配置
  ALIYUN_OSS_ENDPOINT: "http://oss-cn-cq-cqdlww-d01-a.ops.cq.sgcc.com.cn/"
  ALIYUN_OSS_ACCESS_KEY_ID: "dVo0TD4hGELoILff"
  ALIYUN_OSS_ACCESS_KEY_SECRET: "wxGe0FdhaBnpLC7SSQjNEuEpJa3ZsH"
  ALIYUN_OSS_BUCKET_NAME: "ecxyygk"
  ALIYUN_OSS_DOMAIN: "https://ecxyygk.oss-cn-cq-cqdlww-d01-a.ops.cq.sgcc.com.cn"
  
  # OpenAPI安全配置
  OPENAPI_SECURITY_ENABLED: "true"
  OPENAPI_SECURITY_APP_ID: "etravel"
  OPENAPI_SECURITY_APP_SECRET: "3EC294C7-9CD6-BE47-5DA3-07C21F9C92FE"
  OPENAPI_SECURITY_SM4_KEY: "31323334353637383930616263646566"
  OPENAPI_SECURITY_SM4_IV: "66656463626130393837363534333231"
  OPENAPI_SECURITY_REQUEST_EXPIRE_SECONDS: "20"
  OPENAPI_SECURITY_DEBUG_MODE: "false"
  
  # 用户同步配置
  SYNC_USER_ETRAVEL_ENABLE: "true"
  
  # 接口地址配置
  DOWNLOAD_URL: "http://e-car-back-service:7330"
  
  # 泛泛平台配置（外网环境）
  WESTCATR_FANFAN_NEW_BASE_URL: "https://ffzs.cqfanzai.com:10443"
  WESTCATR_FANFAN_TEST_API_URL: "https://ffzs.cqfanzai.com:10443"
  
  # MyBatis Plus配置
  MYBATIS_PLUS_DIALECT_TYPE: "mysql"
  
  # 文件上传配置
  SPRING_SERVLET_MULTIPART_ENABLED: "true"
  SPRING_SERVLET_MULTIPART_MAX_FILE_SIZE: "100MB"
  SPRING_SERVLET_MULTIPART_MAX_REQUEST_SIZE: "100MB"
  SPRING_SERVLET_MULTIPART_LOCATION: "${java.io.tmpdir}"
  SPRING_SERVLET_MULTIPART_FILE_SIZE_THRESHOLD: "0"
  SPRING_SERVLET_MULTIPART_RESOLVE_LAZILY: "false"
  
  # Tomcat配置
  SERVER_TOMCAT_MAX_HTTP_FORM_POST_SIZE: "100MB"
  SERVER_TOMCAT_MAX_SWALLOW_SIZE: "100MB"
  SERVER_TOMCAT_CONNECTION_TIMEOUT: "1800000"
  SERVER_TOMCAT_KEEP_ALIVE_TIMEOUT: "1800000"
  
  # 输出配置
  SPRING_OUTPUT_ANSI_ENABLED: "ALWAYS"
  
  # 管理员配置
  CARADMIN_USERNAME: "cargws"

  # 泛泛平台相关配置
  WESTCATR_FANFAN_TEST-API-URL: "https://ffzs.cqfanzai.com:10443"
  WESTCATR_FANFAN_DEBUG: "false"
  WESTCATR_FANFAN_RSA-PRIVATE-KEY: "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCbFMmOVVs8Vq6uJkHzkO451ZpGKvHnyrud9xsmmsThQOeGaEZhghsgAQUmpzj+/oz6SSgTkPoSIoK5MWjiDjyUUuWUu6EJTE0/Wc1jlL6UMVTwjbnHzpsIbT1EL5fENKo2WN4C3QFjY87kVXcEqtZDDTr5beL2v0xw2B12KXmFuucfd0Vc3ET/v0PVsc3O1KD+jyqALQOMEFN8u7zFJdVckkQuzKjRdqskclp0ZIaTUmNBHsuThDkmkAl/vfrIyn18rRR+J9F01MDG10J7Zodv9tlw5pgBtgK4crg5mF79dtNK6dZ9YAwjtWk7g6lCcn7YKetRHheQt2YuQ1sQThJ5AgMBAAECggEBAI6W5GPsc4DdKquRHZ63qXKfxcOyhM3dB/jMVbh/IXsDVF2uztek6w/YIEZUBwHeRv1x+llTObsm5A8nMQp4kJbISbMheYW1FGmlRkXDZB+Z5EFx7lmay81Js8VYBTEwQzjJKQh08GvUP0yOkGpIMc1hAtKIiODbL4uqChy1MbkUm1rpHJn1UR2CJiaySBA+LzvToNQEre3Ltvc16ZkqHhekClplKSpsHlCM8hfensVbd72BixPAWBOEb0JRg6Y+ypcI+6LGSzM7rxyLtnmM75/xF85MKtYufpzWOPLkoYHg3e1HeO3XAgHn7ZEhUhLfJatkAHqzFIrRiJcsHzQZ7gECgYEA1XTFw0aj26afudPVRIansrS1XywEXFEGpBvU80Cb6or8NhTyYR3I44TX4tr3e2p2MzlNuTnsXqQNreGzaVlW6yG+4/vtOH4W3CVyouHfA8iY2Vo4Skt4ExbOBiXUje6dYVl87QY1AmCoWtX1EcfBIqU970AaeNAPpmdKcydohKECgYEAuf2JAYjB7XhGjKESlvN4SoeX4+uDq9EeRy/WV6ohOQ7oL2Xo2HAmOiynQrhkzInOu4bdJKWF5F3ebQYqjwX2O12gL6BGHFw9oemhG5i7sCgBciBvcH8MsZlhgylh7oxYcpI62RP+Z7UF53foA/CoO3eLGKE0558cDkjeSKlt5tkCgYEAp5mylitnCVaBX6VmdRHNptGg/4NOQTYVotF/mSgHxFcwxT4ZOgvq6koqQKNxYmQrPvklf+RtJbpmSztGZdeSGbk7a0Mp9gt644w7wIzdUNk2uqLOQ/eR5lsnlUgq5u6OgBuFrY4MHTvLtNb+P+G6Ufuw76RDpMypHR1JJbWxqUECgYEApgc8sWDqdNjkaY8z8AqBfE6UeYJ49PzlQXjjpzgZFj1HaWBYvjVG+rF30iiW2ru+D0rxyE/2OQjOkJZrkWcqCo6mez1guNpirlHHXsI18aeUutNXh53yr+uSz6o2YONp5UTCQZwGg39mON2iATpog0VQpvjpKEaxndLQkx6kG+kCgYBIg2jJYgu4HBdlx2jovhzP2fQe3nSykTlvW47qpAJ11rVx0cbW0Mau7tAKDY9HB3tegNBBBMrThgeQZvIJ6BoXI8aFwOXJ7Pujbt61ovVgZtnQGn5xabmzGvxQbRK/ntvGDhLtlYAlgIS5G5GadQvdClBWfhOoWk5UreNjeYlTZQ=="
  WESTCATR_FANFAN_RSA-PUBLIC-KEY: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA65KdtFZA2kDqZm1UEkz6UY662ge2ThYDosG1LI3ZxfqNtzwvWsmoYa3a1zjJay8anEHV5xTyj5ckvspbc8MqJQJb+C4HS3tMHzcuSyEsIrxTr7Qb9N/6GtkxmqVJk2DsIDgZtWW55xrtxkOF7Sy4X9wKCiLOt9bUBq1/NDXXrL7yuDyWrmHml+Edbillhd/kGVE4z5M3JpScyuibNJ3LIC4gxHLJfPxe2x5K7N6aqODDSOhyJroUNNb/6T0KxyXXREtOndp9nmtlikxDDxdcO7AxM1ULmuTOmyo3sN/8vaVv3Etu0o2VtCKwfPQTeILgja69XIMMkXf6HeVtzIRuPwIDAQAB"
  WESTCATR_FANFAN_PARTNER-ID: "25061705155qhn0sb94p00fl"
