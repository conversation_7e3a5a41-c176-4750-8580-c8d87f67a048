package com.westcatr.rd.car.business.car.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.car.business.car.entity.CarVehicleOperationStatisticsInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 车型配件表—操作统计
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "车型配件表—操作统计VO对象")
public class CarVehicleOperationStatisticsInfoVO extends CarVehicleOperationStatisticsInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
