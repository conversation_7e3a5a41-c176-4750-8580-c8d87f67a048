# E出行综合运营管理平台-国密加密通信接口文档

## 概述

本文档详细说明了基于国密算法（SM4和SM3）的前后端加密通信方案，采用RSA+SM4组合加密保障密钥交换安全，包括密钥获取、请求加密和响应解密的完整流程。

## 加密流程

1. **初始化阶段**：
   - 前端在用户登录成功后，首先调用获取RSA公钥接口
   - 前端使用获取的RSA公钥加密本地生成的SM4密钥和初始向量
   - 前端将加密后的SM4密钥和初始向量发送给后端，后端使用RSA私钥解密并保存

2. **请求阶段**：前端使用SM4密钥（CBC模式）对请求参数进行加密，并计算SM3摘要，添加nonce值和时间戳，组装加密请求包发送给后端。

3. **响应阶段**：后端返回加密的响应数据，前端使用SM4密钥进行解密。

## API接口

### 1. 获取RSA公钥

**接口地址**：`/api/crypto/rsa-public-key`

**请求方式**：GET

**请求头**：
- Authorization: Bearer {token} （用户登录后获取的认证令牌）

**响应示例**：
```json
{
  "enabled": true,
  "publicKey": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC8WsY7S+ROSzgS+4/xDkJ+GQgCnrM6K9y9wZ9OWG6LLVQ8cMZNsWFKoj6+UQvEcgJvHhBmM+MF5n6BBdS4Ul6aQzL2GZD+m4JfXw/bIQ+tS0ZTMdz5jZOhk0aNq01HOH3+NRZhO9w+wSc9YCfUSNQEzgP8YWO0JBhSDq0pMY1jMQIDAQAB",
  "keyId": "550e8400-e29b-41d4-a716-446655440000"
}
```

**响应参数说明**：
- enabled: 是否启用加密
- publicKey: RSA公钥（Base64编码）
- keyId: 公钥ID，用于跟踪密钥使用

### 2. 注册SM4密钥

**接口地址**：`/api/crypto/registerSm4Key`

**请求方式**：POST

**请求头**：
- Authorization: Bearer {token} （用户登录后获取的认证令牌）

**请求参数**：
```json
{
  "encryptedSm4Key": "RSA加密后的SM4密钥（Base64字符串）",
  "encryptedSm4Iv": "RSA加密后的SM4初始向量（Base64字符串）",
  "keyId": "550e8400-e29b-41d4-a716-446655440000"
}
```

**响应示例**：
```json
{
  "enabled": true,
  "sessionId": "662f9400-e29b-41d4-a716-************",
  "expiresAt": 1711382500000
}
```

**响应参数说明**：
- enabled: 是否启用加密
- sessionId: 会话ID，用于跟踪密钥使用
- expiresAt: 密钥过期时间戳（毫秒）

### 3. 测试加密连接

**接口地址**：`/api/crypto/test-connection`

**请求方式**：POST

**请求体**：按照下方"请求体加密格式"进行加密

**响应示例**：
```json
{
  "data": "加密后的Base64字符串",
  "signature": "SM3摘要值（十六进制字符串）",
  "encrypted": true,
  "timestamp": 1711382500000
}
```

**解密后内容**：
```json
{
  "timestamp": 1711382500000,
  "status": "success",
  "message": "加密通道测试成功"
}
```

## 请求加密示例

前端在发送请求前，需要对请求体进行加密处理，具体步骤如下：

1. 将原始请求体转换为JSON字符串
2. 使用SM4密钥和初始向量对JSON字符串进行加密（**必须使用CBC模式**）
3. 生成当前时间戳和nonce随机数（防重放攻击）
4. 计算签名：使用SM3算法对"原始数据 + 时间戳 + nonce + SM4密钥"组合字符串进行哈希计算
5. 将加密后的数据、签名、会话ID、nonce和时间戳封装成加密请求格式

### 请求体加密格式

```json
{
  "data": "加密后的Base64字符串",
  "signature": "SM3摘要值（十六进制字符串）",
  "encrypted": true,
  "sessionId": "662f9400-e29b-41d4-a716-************",
  "nonce": "随机生成的UUID字符串",
  "timestamp": 1711382500000
}
```

> **注意**：
> 1. 签名计算公式：signature = SM3(decryptedData + timestamp + nonce + sm4Key)
> 2. timestamp字段是防重放攻击的关键，服务器会拒绝处理超过3分钟的请求
> 3. nonce与timestamp结合使用，服务器会拒绝处理使用相同nonce的重复请求
> 4. 服务器会维护已处理的nonce黑名单，有效期与timestamp验证时间一致
> 5. 密钥有效期默认为2小时，过期后需要重新获取

## 响应解密示例

后端返回的响应体也会进行加密，格式如下：

```json
{
  "data": "加密后的Base64字符串",
  "signature": "SM3摘要值（十六进制字符串）",
  "encrypted": true,
  "timestamp": 1711382500000,
  "nonce": "随机生成的UUID字符串"
}
```

前端需要对响应体中的data字段进行解密。响应体可能是简单字符串或JSON对象，需要根据实际情况处理。

## 错误处理

在使用国密加密通信过程中，可能会遇到各种错误情况。系统会返回未加密的标准错误响应，便于前端直接处理。所有错误响应均使用统一的 `IResult` 格式：

```json
{
  "success": false,
  "message": "错误消息",
  "status": 401,
  "timestamp": 1711382500000,
  "data": null
}
```

### 常见错误类型

#### 1. 会话过期错误

当加密会话过期或不存在时，系统会返回以下错误：

```json
{
  "success": false,
  "message": "会话已过期，请重新登录",
  "status": 401,
  "timestamp": 1711382500000,
  "data": null
}
```

**处理方式**：前端应捕获此错误，并引导用户重新登录或自动刷新会话（重新获取RSA公钥并注册SM4密钥）。

#### 2. 签名验证错误

当请求的签名验证失败时，系统会返回以下错误：

```json
{
  "success": false,
  "message": "签名验证失败，请确保请求未被篡改",
  "status": 400,
  "timestamp": 1711382500000,
  "data": null
}
```

**处理方式**：前端应检查SM3签名计算逻辑，确保使用正确的密钥和算法。

#### 3. 重放攻击错误

当检测到重放攻击（使用过期的时间戳或重复的nonce）时，系统会返回以下错误：

```json
{
  "success": false,
  "message": "检测到重放攻击，请确保请求时间戳和随机数的唯一性",
  "status": 400,
  "timestamp": 1711382500000,
  "data": null
}
```

**处理方式**：前端应确保每次请求使用当前时间戳和唯一的nonce值。

#### 4. 加密处理错误

当加密或解密过程中发生其他错误时，系统会返回以下错误：

```json
{
  "success": false,
  "message": "加密处理异常: 具体错误信息",
  "status": 500,
  "timestamp": 1711382500000,
  "data": null
}
```

**处理方式**：前端应记录错误并尝试重新建立加密通道。

### 前端错误处理示例

```javascript
// 发送加密请求并处理可能的错误
async function apiRequest(url, method, data = null) {
  try {
    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`
      },
      body: data ? JSON.stringify(encryptRequest(data)) : null
    });
    
    const result = await response.json();
    
    // 检查是否是未加密的错误响应
    if (!result.encrypted && !result.success) {
      // 处理特定类型的错误
      switch (result.status) {
        case 401:
          // 会话过期，重新获取密钥
          console.log('加密会话已过期，正在重新获取密钥...');
          await fetchRsaPublicKey();
          // 重试请求
          return await apiRequest(url, method, data);
          
        case 400:
          if (result.message.includes('重放攻击')) {
            console.error('检测到重放攻击错误，请检查时间戳和nonce生成逻辑');
          } else if (result.message.includes('签名验证失败')) {
            console.error('签名验证失败，请检查SM3签名计算逻辑');
          }
          throw new Error(result.message);
          
        default:
          console.error('加密通信错误:', result.message);
          throw new Error(result.message);
      }
    }
    
    // 正常响应，解密处理
    return decryptResponse(result);
  } catch (error) {
    console.error('API请求错误:', error);
    throw error;
  }
}
```

## 安全建议

1. **密钥存储安全**：
   - 前端SM4密钥应仅存储在内存中，避免持久化存储
   - 如需持久化，可考虑使用SessionStorage（会话结束自动清除）
   - 绝对不要使用localStorage存储密钥

2. **密钥生命周期**：
   - SM4密钥有效期默认为2小时
   - 应用应实现定期检查密钥是否过期的机制
   - 密钥过期前应主动更新，避免加密中断

3. **防重放攻击**：
   - 每个请求必须包含时间戳和nonce
   - 服务器应拒绝处理超过3分钟的请求
   - 服务器应维护已处理的nonce黑名单

4. **错误处理**：
   - 加密或解密失败时应提供明确的错误信息
   - 不应在生产环境暴露详细的加密错误信息
   - 应实现失败重试机制

## 调试模式

在开发环境中，可通过配置启用或禁用加密功能：

```javascript
// 开发环境配置示例
const devConfig = {
  enableEncryption: false, // 设置为false可禁用加密
  logEncryption: true  // 设置为true可记录加密日志
};

// 根据环境决定是否启用加密
if (process.env.NODE_ENV === 'development' && !devConfig.enableEncryption) {
  // 跳过加密处理，直接使用原始数据
  // 仅用于开发调试
}
```

> **警告**：调试模式仅适用于开发环境，生产环境必须启用加密功能。

## 作者：liusheng