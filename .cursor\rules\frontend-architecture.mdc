---
description: 
globs: 
alwaysApply: false
---
# 前端架构与技术栈

## 管理端 (e-admin)

### 技术栈
- **框架**: React
- **UI 组件**: Ant Design Pro
- **状态管理**: 
  - umi Model (基于 dva)
  - valtio
- **路由**: umi router
- **构建工具**: umi
- **HTTP 客户端**: axios
- **样式处理**: Less
- **代码规范**: ESLint + Prettier

### 目录结构

- **src/components**: 公共组件
  - action: 操作类组件
  - button: 按钮类组件
  - form: 表单类组件
  - modal: 弹窗类组件
  - table: 表格类组件

- **src/pages**: 页面组件
  - 按业务模块组织，如 cars, maintenance, system 等

- **src/services**: API 请求
  - base: 基础请求
  - biz: 业务请求

- **src/utils**: 工具函数
  - hooks: 自定义钩子
  - request: 请求工具

- **src/styles**: 全局样式

### 主要业务模块
- **车辆管理 (cars)**
- **维修服务 (maintenance)**
- **采购管理 (procurement)**
- **系统设置 (system)**

## 客户端/移动端 (e-app)

### 技术栈
- **框架**: Vue 3
- **UI 组件**: 自定义组件
- **状态管理**: Pinia
- **路由**: Vue Router
- **构建工具**: Vite
- **HTTP 客户端**: axios
- **样式处理**: SCSS
- **代码规范**: ESLint

### 目录结构

- **src/components**: 公共组件
  - Button: 按钮组件
  - Card: 卡片组件
  - CarPanel: 车辆面板
  - List: 列表组件
  - Steps: 步骤组件

- **src/pages**: 页面组件
  - car: 车辆相关页面
  - demand: 需求相关页面
  - drive: 试驾相关页面
  - used: 二手车相关页面

- **src/services**: API 请求
  - base: 基础请求

- **src/utils**: 工具函数
  - hooks: 自定义钩子
  - request: 请求工具

- **src/styles**: 全局样式
  - mixin: 混合样式

### 主要功能模块
- **车辆展示与选择**
- **试驾申请**
- **需求发布**
- **二手车交易**

## 通用规范

### 组件设计原则
- **单一职责**: 每个组件只做一件事
- **可复用性**: 公共组件放在 components 目录
- **可测试性**: 避免副作用，方便单元测试
- **可维护性**: 组件结构清晰，职责明确

### 状态管理
- **管理端**: 使用 umi Model(dva)/valtio 管理全局状态
- **客户端**: 使用 Pinia 管理全局状态
- 本地状态使用组件内部状态
- 全局状态应用于跨组件共享的数据

### 路由设计
- 路径命名遵循 RESTful 规范
- 权限控制集成到路由配置
- 懒加载优化首屏加载
