package com.westcatr.rd.car.business.car.pojo.query;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import jakarta.validation.constraints.Pattern;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.westcatr.rd.boot.orm.association.annotation.JoinExpression;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 车辆—车型信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "车辆—车型信息表查询对象")
public class CarModelInfoQuery extends PageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @QueryCondition
    private Long id;

    @QueryCondition
    @Pattern(regexp = "^$|^[0-9]+$", message = "车辆id只能输入数字")
    private String carId;

    @QueryCondition(condition = QueryCondition.Condition.IN, field = "car_id")
    private List<Long> carIds;

    @Schema(description = "年款")
    @QueryCondition
    private Integer yearModel;

    @Schema(description = "配置名称")
    @QueryCondition
    private String configName;

    @Schema(description = "是否支持试驾（0: 否, 1: 是）")
    @QueryCondition
    private Integer isTestDrive;

    @Schema(description = "供应商")
    @QueryCondition
    private Long supplierId;

    @Schema(description = "需求标题")
    @JoinExpression(value = "join supplier_info si on si.id=@m.supplier_id", allJoin = true)
    private Boolean joinSupplierTable = true;

    @Schema(description = "品牌车型")
    @JoinExpression(value = "join car_info ci on ci.id=@m.car_id")
    @QueryCondition(condition = QueryCondition.Condition.LIKE, field = "ci.brand_model")
    private String brandModel;

    @Schema(description = "供应商名称")
    @QueryCondition(condition = QueryCondition.Condition.LIKE, field = "si.company_name")
    private String companyName;

    @Schema(description = "供应商类型")
    @QueryCondition(condition = QueryCondition.Condition.EQ, field = "si.supplier_type")
    private String supplierType;

    @QueryCondition
    private Date createTime;

    @QueryCondition
    private Date updateTime;

    @QueryCondition(condition = QueryCondition.Condition.GE, field = "price")
    private Double lowPrice;

    @QueryCondition(condition = QueryCondition.Condition.LE, field = "price")
    private Double highPrice;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "开始时间")
    @QueryCondition(condition = QueryCondition.Condition.GE, field = "create_time")
    private Date beginTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "结束时间")
    @QueryCondition(condition = QueryCondition.Condition.LE, field = "create_time")
    private Date endTime;

    @Schema(description = "时间排序，1倒叙，0正序")
    @QueryCondition(condition = QueryCondition.Condition.DEFAULT, field = "create_time", sort = QueryCondition.Sort.AUTO)
    private Integer timeSort;

    @Schema(description = "id排序，1倒叙，0正序")
    @QueryCondition(condition = QueryCondition.Condition.DEFAULT, field = "id", sort = QueryCondition.Sort.AUTO)
    private Integer idSort;
}
