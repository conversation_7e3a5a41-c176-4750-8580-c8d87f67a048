<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.car.business.car.mapper.CarFlowParamDataInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.car.business.car.entity.CarFlowParamDataInfo">
        <id column="id" property="id" />
        <result column="car_model_id" property="carModelId" />
        <result column="flow_id" property="flowId" />
        <result column="create_user_id" property="createUserId" />
        <result column="bus_json" property="busJson" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, car_model_id, flow_id, create_user_id, bus_json, create_time, update_time
    </sql>

</mapper>
