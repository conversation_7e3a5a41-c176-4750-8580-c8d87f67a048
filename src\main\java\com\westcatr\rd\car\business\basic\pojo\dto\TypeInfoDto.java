package com.westcatr.rd.car.business.basic.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TypeInfoDto {

    @Schema(description = "车辆评价标签数据")
    private Datas carDatas;

    @Schema(description = "随机标签（车辆）")
    private List<String> randomCar;

    @Schema(description = "供应商评价标签数据")
    private Datas supplierDatas;

    @Schema(description = "随机标签（供应商）")
    private List<String> randomSupplier;

    @Data
    public static class Datas {

        @Schema(description = "优点的集合")
        private List<String> advantages;

        @Schema(description = "缺点的集合")
        private List<String> disadvantages;
    }


}
