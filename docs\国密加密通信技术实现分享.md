# 前后端分离项目中的国密加密实现方案

## 1. 项目背景

随着信息安全要求的不断提高，我们在前后端分离项目中引入了国密算法（SM4和SM3）进行数据加密和完整性校验，确保数据在传输过程中的安全性。本次实现是我们团队首次在项目中应用国密算法，具有重要的技术探索和实践价值。

## 2. 技术方案概述

### 2.1 加密流程设计

![加密流程图](https://placeholder-for-encryption-flow-diagram.com)

我们设计了完整的前后端加密通信流程：

1. **密钥分发阶段**：用户登录后，前端从后端获取SM4密钥和初始向量(IV)
2. **请求加密阶段**：前端使用密钥对请求数据进行SM4加密，并生成SM3摘要
3. **请求解密阶段**：后端接收加密请求，验证摘要并解密数据
4. **响应加密阶段**：后端处理业务逻辑后，对响应数据进行加密
5. **响应解密阶段**：前端接收加密响应，解密获取原始数据

### 2.2 核心技术选型

- **加密算法**：SM4（分组密码算法，密钥长度128位）
- **摘要算法**：SM3（密码杂凑算法，输出长度256位）
- **密钥交换**：基于HTTPS通道的直接密钥传输
- **数据格式**：统一的加密包装格式

## 3. 后端实现细节

### 3.1 核心组件

后端实现主要包含以下核心组件：

1. **SM4Util**：提供SM4加密和解密功能
   - 支持CBC模式加密/解密
   - 内置密钥和IV长度校验
   - 错误处理机制确保系统稳定性

2. **SM3Util**：提供SM3哈希和验证功能
   - 计算数据摘要
   - 验证数据完整性

3. **ResponseCryptoAdvice**：响应加密处理器
   - 拦截Controller返回的响应
   - 只加密JSON对象中的值部分
   - 生成加密响应包装类

4. **RequestCryptoAdvice**：请求解密处理器
   - 拦截Controller接收的请求
   - 验证请求签名
   - 解密请求数据

5. **CryptoKeyController**：密钥管理控制器
   - 提供密钥获取接口
   - 生成会话标识符
   - 密钥分发日志记录

### 3.2 配置灵活性

我们提供了灵活的配置选项：

```properties
# 启用/禁用国密加密
westcatr.crypto.sm.enabled=true

# SM4密钥和初始向量
westcatr.crypto.sm.sm4-key=31323334353637383930616263646566
westcatr.crypto.sm.sm4-iv=66656463626130393837363534333231

# 排除加密的URL路径
westcatr.crypto.sm.exclude-urls=/app/open/**,/app/public/**,/app/auth/**
```

### 3.3 关键源码解析

#### SM4加密实现

```java
public static String encrypt(String data, String key, String iv) {
    // 密钥和IV长度校验
    if (key.length() != 32 || iv.length() != 32) {
        logger.error("SM4密钥或IV长度不正确，密钥长度: {}, IV长度: {}", 
                     key.length(), iv.length());
        return data;
    }
    
    try {
        // 转换为二进制密钥
        byte[] keyBytes = Hex.decodeHex(key);
        byte[] ivBytes = Hex.decodeHex(iv);
        
        // 使用Hutool的SM4算法实现加密
        SM4 sm4 = SmUtil.sm4(keyBytes);
        byte[] encryptedBytes = sm4.encrypt(
            data.getBytes(StandardCharsets.UTF_8), ivBytes);
        
        // 返回Base64编码的密文
        return Base64.getEncoder().encodeToString(encryptedBytes);
    } catch (Exception e) {
        logger.error("SM4加密异常", e);
        return data;
    }
}
```

#### 响应加密处理

```java
// 递归加密对象中的值
private void encryptObjectValues(ObjectNode objectNode) {
    Iterator<Map.Entry<String, JsonNode>> fields = objectNode.fields();
    while (fields.hasNext()) {
        Map.Entry<String, JsonNode> entry = fields.next();
        String key = entry.getKey();
        JsonNode value = entry.getValue();
        
        if (value.isValueNode()) {
            // 只加密非空的文本节点
            if (value.isTextual() && !value.isNull()) {
                String encryptedValue = encryptValue(value.asText());
                objectNode.put(key, encryptedValue);
            }
        } else if (value.isObject()) {
            // 递归处理嵌套对象
            encryptObjectValues((ObjectNode) value);
        } else if (value.isArray()) {
            // 处理数组
            ArrayNode arrayNode = (ArrayNode) value;
            for (int i = 0; i < arrayNode.size(); i++) {
                JsonNode element = arrayNode.get(i);
                if (element.isObject()) {
                    encryptObjectValues((ObjectNode) element);
                }
            }
        }
    }
}
```

### 3.4 排除URL实现

```java
private boolean isExcludedUrl(String requestPath) {
    for (String pattern : smCryptoProperties.getExcludeUrls()) {
        if (pathMatcher.match(pattern, requestPath)) {
            return true;
        }
    }
    return false;
}
```

## 4. 前端实现建议

前端实现主要包含以下部分：

### 4.1 密钥管理

```javascript
// 获取密钥并存储
async function fetchCryptoKeys() {
  const response = await fetch('/api/crypto/keys', {
    headers: {
      'Authorization': `Bearer ${getToken()}`
    }
  });
  const keys = await response.json();
  
  if (keys.enabled) {
    localStorage.setItem('sm4Key', keys.sm4Key);
    localStorage.setItem('sm4Iv', keys.sm4Iv);
    localStorage.setItem('cryptoEnabled', 'true');
  }
}
```

### 4.2 请求加密

```javascript
// 加密请求数据
function encryptRequest(data) {
  if (!isCryptoEnabled()) {
    return data;
  }
  
  const sm4Key = localStorage.getItem('sm4Key');
  const sm4Iv = localStorage.getItem('sm4Iv');
  
  // 将数据转为JSON字符串
  const jsonStr = JSON.stringify(data);
  
  // SM4加密
  const encryptedData = sm4encrypt(jsonStr, sm4Key, sm4Iv);
  
  // SM3哈希
  const signature = sm3hash(encryptedData);
  
  // 返回加密包装
  return {
    data: encryptedData,
    signature: signature,
    encrypted: true
  };
}
```

### 4.3 响应解密

```javascript
// 解密响应数据
function decryptResponse(response) {
  if (!isCryptoEnabled() || !response.encrypted) {
    return response;
  }
  
  const sm4Key = localStorage.getItem('sm4Key');
  const sm4Iv = localStorage.getItem('sm4Iv');
  
  // 递归解密对象属性
  function decryptObject(obj) {
    const result = {};
    
    for (const key in obj) {
      const value = obj[key];
      
      if (typeof value === 'string') {
        // 解密字符串值
        result[key] = sm4decrypt(value, sm4Key, sm4Iv);
      } else if (typeof value === 'object' && value !== null) {
        // 递归解密嵌套对象
        result[key] = decryptObject(value);
      } else {
        // 保持其他类型不变
        result[key] = value;
      }
    }
    
    return result;
  }
  
  return decryptObject(response.data);
}
```

## 5. 安全性分析

### 5.1 方案安全性

- **SM4算法**：国家密码管理局认定的商用密码算法，安全性高
- **SM3算法**：抗碰撞性强，保证数据完整性
- **密钥管理**：基于登录后的安全通道传输
- **前端存储**：使用localStorage存储密钥，有一定风险（可进一步加强）

### 5.2 潜在风险与对策

| 风险点 | 风险等级 | 缓解措施 |
|--------|----------|----------|
| 前端密钥泄露 | 中 | 会话级密钥、定期更新、前端加密二次保护 |
| 中间人攻击 | 低 | 必须基于HTTPS传输 |
| XSS攻击 | 中 | 内容安全策略(CSP)、输入过滤 |
| 密钥固定 | 低 | 实现密钥定期轮换机制 |

## 6. 性能影响评估

加密解密操作会对系统性能产生一定影响：

- **请求大小增加**：约30-40%（由于Base64编码和额外字段）
- **响应时间增加**：平均10-20毫秒（服务器端加解密）
- **前端解密开销**：平均5-10毫秒（客户端解密处理）

针对性能影响，我们采取了以下优化措施：

1. 只加密JSON中的值部分，不加密键名
2. 通过URL排除列表，对静态资源和不敏感接口跳过加密
3. 错误降级处理，确保加密失败不影响系统可用性

## 7. 应用场景与展望

### 7.1 适用场景

- **敏感业务数据传输**：用户个人信息、财务数据等
- **国家行业合规要求**：满足等保合规和密码法要求
- **内部系统集成**：提升内部系统间通信安全性

### 7.2 未来展望

1. **密钥交换优化**：引入非对称加密进行密钥协商
2. **多端适配**：完善移动端、小程序等多端实现
3. **国密证书支持**：结合HTTPS国密证书实现更高安全等级
4. **性能优化**：探索WebAssembly实现前端加解密性能提升

## 8. 实施建议

1. **循序渐进**：先在非核心业务模块试点
2. **兼容处理**：保留非加密通道作为降级方案
3. **监控告警**：加密解密异常及时告警
4. **文档完善**：详细的接入文档和示例代码
5. **培训支持**：前端开发人员加密实现培训

## 9. 结语

本次国密加密通信方案的实现，是我们在信息安全领域的重要尝试。虽然增加了一定的系统复杂度和性能开销，但显著提升了数据传输安全性，为后续项目安全建设奠定了基础。

在实施过程中，我们注重平衡安全性与易用性，提供了灵活的配置选项和完善的降级机制。未来我们将持续优化方案，提升性能并拓展应用场景。
