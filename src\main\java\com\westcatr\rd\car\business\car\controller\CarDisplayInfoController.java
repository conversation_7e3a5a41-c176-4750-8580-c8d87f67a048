package com.westcatr.rd.car.business.car.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.car.business.car.entity.CarDisplayInfo;
import com.westcatr.rd.car.business.car.pojo.dto.CarYearInfoDto;
import com.westcatr.rd.car.business.car.pojo.query.CarDisplayInfoQuery;
import com.westcatr.rd.car.business.car.pojo.query.CarInfoQuery;
import com.westcatr.rd.car.business.car.pojo.vo.CarDisplayInfoVO;
import com.westcatr.rd.car.business.car.pojo.vo.CarInfoVO;
import com.westcatr.rd.car.business.car.service.CarDisplayInfoService;
import com.westcatr.rd.car.business.car.service.CarModelInfoService;
import com.westcatr.rd.car.common.annotation.AuditLog;
import com.westcatr.rd.car.enums.OperationTypeEnum;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * CarDisplayInfo 控制器
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
@Validated
@Tag(name = "车辆—信息展示表接口", description = "车辆—信息展示表接口")
@Slf4j
@RestController
public class CarDisplayInfoController {

    @Autowired
    private CarDisplayInfoService carDisplayInfoService;

    @Autowired
    private CarModelInfoService carModelInfoService;

    @Operation(summary = "获取车辆—信息展示表分页数据")
    @PostMapping("/carDisplayInfo/page")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车辆信息展示表分页数据")
    public IResult<IPage<CarDisplayInfo>> getCarDisplayInfoPage(@RequestBody CarDisplayInfoQuery query) {
        return IResult.ok(carDisplayInfoService.entityPage(query));
    }

    @Operation(summary = "获取车辆—信息展示表数据")
    @PostMapping("/carDisplayInfo/get")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车辆信息展示表数据: ID={#id.id}")
    public IResult<CarDisplayInfo> getCarDisplayInfoById(@RequestBody @Id ID id) {
        return IResult.ok(carDisplayInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增车辆—信息展示表数据")
    @PostMapping("/carDisplayInfo/add")
    @AuditLog(operationType = OperationTypeEnum.ADD, description = "新增车辆信息展示表数据: ID={#param.id}")
    public IResult addCarDisplayInfo(@RequestBody @Validated(Insert.class) CarDisplayInfo param) {
        return IResult.auto(carDisplayInfoService.saveEntity(param));
    }

    @Operation(summary = "更新车辆—信息展示表数据")
    @PostMapping("/carDisplayInfo/update")
    @AuditLog(operationType = OperationTypeEnum.UPDATE, description = "更新车辆信息展示表数据: ID={#param.id}")
    public IResult updateCarDisplayInfoById(@RequestBody @Validated(Update.class) CarDisplayInfo param) {
        return IResult.auto(carDisplayInfoService.updateEntity(param));
    }

    @Operation(summary = "删除车辆—信息展示表数据")
    @PostMapping("/carDisplayInfo/delete")
    @AuditLog(operationType = OperationTypeEnum.DELETE, description = "删除车辆信息展示表数据: ID={#id.id}")
    public IResult deleteCarDisplayInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            carDisplayInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取车辆—信息展示表VO分页数据")
    @PostMapping("/carDisplayInfo/voPage")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车辆信息展示表VO分页数据")
    public IResult<IPage<CarDisplayInfoVO>> getCarDisplayInfoVoPage(@RequestBody CarDisplayInfoQuery query) {
        AssociationQuery<CarDisplayInfoVO> associationQuery = new AssociationQuery<>(CarDisplayInfoVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取车辆—信息展示表VO数据")
    @PostMapping("/carDisplayInfo/getVo")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车辆信息展示表VO数据: ID={#id.id}")
    public IResult<CarDisplayInfoVO> getCarDisplayInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<CarDisplayInfoVO> associationQuery = new AssociationQuery<>(CarDisplayInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    @Operation(summary = "（app）设置车辆展示")
    @PostMapping("/carDisplayInfo/setUpCarouselDisplay")
    @AuditLog(operationType = OperationTypeEnum.UPDATE, description = "设置车辆轮播展示: ID={#carDisplayInfo.id}")
    public IResult setUpCarouselDisplay(@RequestBody CarDisplayInfo carDisplayInfo) {
        return IResult.auto(carDisplayInfoService.setUpCarouselDisplay(carDisplayInfo));
    }

    @Operation(summary = "（app）获取轮播展示车辆")
    @PostMapping("/carDisplayInfo/getTopOfLineImgUrls")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "获取轮播展示车辆图片列表")
    public IResult<List<String>> getTopOfLineImgUrls() {
        return IResult.ok(carDisplayInfoService.getTopOfLineImgUrls());
    }

    @Operation(summary = "（app）获取车辆展示")
    @PostMapping("/carDisplayInfo/getCarInfoVoPage")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "获取车辆展示信息分页数据")
    public IResult<IPage<CarInfoVO>> getCarInfoVoPage(@Validated @RequestBody CarInfoQuery query) {
        return IResult.ok(carDisplayInfoService.getCarInfoVoPage(query));
    }

    @Operation(summary = "（app）获取车辆某些参数信息")
    @PostMapping("/carDisplayInfo/listPurchasedVehicles")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "获取车辆参数信息: carId={#carId}")
    public IResult<CarYearInfoDto> listPurchasedVehicles(@RequestParam Long carId) {
        return IResult.ok(carModelInfoService.getCarModelInfoYearModelList(carId));
    }

    @Operation(summary = "设置车辆排序")
    @PostMapping("/carDisplayInfo/setSortInfoDisplay")
    @AuditLog(operationType = OperationTypeEnum.UPDATE, description = "设置车辆排序: ID={#carDisplayInfo.id}")
    public IResult setSortInfoDisplay(@Validated @RequestBody CarDisplayInfo carDisplayInfo) {
        return IResult.auto(carDisplayInfoService.setSortInfoDisplay(carDisplayInfo));
    }

    /*
     * @Operation(summary = "（app）获取车型信息")
     * 
     * @GetMapping("/carDisplayInfo/getCarModelYearDtoList")
     * public IResult<List<CarModelYearDto>> getCarModelYearDtoList(Long carId) {
     * return IResult.ok(carDisplayInfoService.getCarModelYearDtoList(carId));
     * }
     */
}
