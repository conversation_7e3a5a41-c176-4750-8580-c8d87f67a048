package com.westcatr.rd.car.business.car.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class CarExcelParameterDto {

    @ExcelProperty("主键ID")
    private Long id;

    @ExcelProperty("关联车辆ID")
    private Long carId;

    @ExcelProperty("参考价(元)")
    private BigDecimal referencePrice;

    @ExcelProperty("优惠信息")
    private String discountInfo;

    @ExcelProperty("厂商指导价(元)")
    private BigDecimal manufacturerPrice;

    @ExcelProperty("厂商")
    private String manufacturer;

    @ExcelProperty("级别")
    private String vehicleLevel;

    @ExcelProperty("能源类型")
    private String energyType;

    @ExcelProperty("环保标准")
    private String emissionStandard;

    @ExcelProperty("上市时间")
    private Date releaseDate;

    @ExcelProperty("WLTC纯电续航里程(km)")
    private Integer wltcRange;

    @ExcelProperty("CLTC纯电续航里程(km)")
    private Integer cltcRange;

    @ExcelProperty("电池快充时间(小时)")
    private BigDecimal fastChargeTime;

    @ExcelProperty("电池慢充时间(小时)")
    private BigDecimal slowChargeTime;

    @ExcelProperty("电池快充电量范围(%)")
    private String fastChargeRange;

    @ExcelProperty("最大功率(kW)")
    private Integer maxPower;

    @ExcelProperty("最大扭矩(N·m)")
    private Integer maxTorque;

    @ExcelProperty("变速箱")
    private String transmission;

    @ExcelProperty("车身结构")
    private String bodyStructure;

    @ExcelProperty("发动机")
    private String engine;

    @ExcelProperty("电动机(Ps)")
    private Integer electricMotorPs;

    @ExcelProperty("长*宽*高(mm)")
    private String dimensions;

    @ExcelProperty("官方0-100km/h加速(s)")
    private BigDecimal acceleration;

    @ExcelProperty("最高车速(km/h)")
    private Integer maxSpeed;

    @ExcelProperty("WLTC综合油耗(L/100km)")
    private BigDecimal wltcFuelConsumption;

    @ExcelProperty("最低荷电状态油耗(L/100km)")
    private BigDecimal minChargeStateFuelConsumption;

    @ExcelProperty("整车质保")
    private String warranty;

    @ExcelProperty("整备质量(kg)")
    private Integer curbWeight;

    @ExcelProperty("最大满载质量(kg)")
    private Integer maxPayload;

    @ExcelProperty("准拖挂车总质量(kg)")
    private Integer maxTowingMass;

    @ExcelProperty("长度(mm)")
    private Integer length;

    @ExcelProperty("宽度(mm)")
    private Integer width;

    @ExcelProperty("高度(mm)")
    private Integer height;

    @ExcelProperty("轴距(mm)")
    private Integer wheelbase;

    @ExcelProperty("前轮距(mm)")
    private Integer frontTrack;

    @ExcelProperty("后轮距(mm)")
    private Integer rearTrack;

    @ExcelProperty("满载最小离地间隙(mm)")
    private BigDecimal groundClearance;

    @ExcelProperty("接近角(°)")
    private BigDecimal approachAngle;

    @ExcelProperty("离去角(°)")
    private BigDecimal departureAngle;

    @ExcelProperty("纵向通过角(°)")
    private BigDecimal rampBreakoverAngle;

    @ExcelProperty("最大爬坡角度(°)")
    private BigDecimal maxClimbAngle;

    @ExcelProperty("最小转弯半径(m)")
    private BigDecimal turningRadius;

    @ExcelProperty("最大涉水深度(mm)")
    private Integer maxWadingDepth;

    @ExcelProperty("油箱容积(L)")
    private BigDecimal fuelTankCapacity;

    @ExcelProperty("前备厢容积(L)")
    private BigDecimal frontTrunkVolume;

    @ExcelProperty("后备厢容积(L)")
    private BigDecimal rearTrunkVolume;

    @ExcelProperty("风阻系数(Cd)")
    private BigDecimal dragCoefficient;

    @ExcelProperty("发动机型号")
    private String engineModel;

    @ExcelProperty("排量(mL)")
    private Integer displacementMl;

    @ExcelProperty("排量(L)")
    private BigDecimal displacementL;

    @ExcelProperty("进气形式")
    private String intakeForm;

    @ExcelProperty("发动机布局")
    private String engineLayout;

    @ExcelProperty("气缸排列形式")
    private String cylinderArrangement;

    @ExcelProperty("气缸数(个)")
    private Integer cylinderCount;

    @ExcelProperty("每缸气门数(个)")
    private Integer valvePerCylinder;

    @ExcelProperty("配气机构")
    private String valveMechanism;

    @ExcelProperty("最大马力(Ps)")
    private Integer maxHorsepower;

    @ExcelProperty("最大功率转速(rpm)")
    private Integer maxRpm;

    @ExcelProperty("最大扭矩转速(rpm)")
    private Integer torqueRpm;

    @ExcelProperty("燃油标号")
    private String fuelLabel;

    @ExcelProperty("供油方式")
    private String fuelSupplyMode;

    @ExcelProperty("电池能量(kWh)")
    private BigDecimal batteryCapacity;

    @ExcelProperty("电池能量密度(Wh/kg)")
    private BigDecimal batteryDensity;
}
