# 近3个月维修次数功能实现说明

## 功能概述

本功能用于统计配件在近3个月内的维修使用次数，当向泛泛平台提交维修数据时，会自动计算并填充 `recentThreeMonthsRepairCount` 参数。

## 实现方案

### 1. 数据库设计

创建了新表 `car_parts_repair_record` 用于存储配件维修记录：

```sql
CREATE TABLE `car_parts_repair_record` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `part_code` varchar(100) NOT NULL COMMENT '配件编码',
  `part_name` varchar(255) DEFAULT NULL COMMENT '配件名称',
  `repair_order_id` bigint NOT NULL COMMENT '维修工单ID',
  `repair_order_code` varchar(100) DEFAULT NULL COMMENT '维修工单编号',
  `repair_time` datetime NOT NULL COMMENT '维修时间',
  `quantity` int DEFAULT 1 COMMENT '使用数量',
  `audit_type` varchar(50) DEFAULT NULL COMMENT '审核类型',
  `audit_result` varchar(50) DEFAULT NULL COMMENT '审核结果',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_part_code_repair_time` (`part_code`, `repair_time`),
  KEY `idx_repair_order_id` (`repair_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配件维修记录表';
```

### 2. 核心组件

#### 2.1 实体类
- `CarPartsRepairRecord.java` - 配件维修记录实体

#### 2.2 数据访问层
- `CarPartsRepairRecordMapper.java` - Mapper接口
- `CarPartsRepairRecordMapper.xml` - MyBatis映射文件

#### 2.3 服务层
- `CarPartsRepairRecordService.java` - 服务接口
- `CarPartsRepairRecordServiceImpl.java` - 服务实现

### 3. 核心功能

#### 3.1 记录保存
当泛泛平台回调"修理厂整改"审核通过时，自动保存配件维修记录：

```java
// 在 FanFanApiServiceImpl.java 中
if ("修理厂整改".equals(auditType) && "通过".equals(auditResult)) {
    // 保存配件维修记录
    carPartsRepairRecordService.savePartsRepairRecords(orderInfo, auditType, auditResult);
}
```

#### 3.2 统计查询
提供单个和批量查询近3个月维修次数的方法：

```java
// 单个配件查询
Integer getRecentThreeMonthsRepairCount(String partCode);

// 批量配件查询
Map<String, Integer> getRecentThreeMonthsRepairCountBatch(List<String> partCodes);
```

#### 3.3 集成到提交接口
在向泛泛平台提交数据时，自动统计并填充维修次数：

```java
// 在 FanFanOpenApiServiceImpl.java 中
Integer repairCount = carPartsRepairRecordService.getRecentThreeMonthsRepairCount(partInfo.getPartCode());
fanPart.setRecentThreeMonthsRepairCount(repairCount);
```

## 使用说明

### 1. 数据库迁移
系统启动时会自动执行数据库迁移脚本，创建必要的表结构。

### 2. 自动记录
当泛泛平台回调"修理厂整改"审核通过时，系统会自动：
- 查询该订单的所有配件信息
- 为每个配件创建维修记录
- 记录维修时间、数量等信息

### 3. 自动统计
在向泛泛平台提交维修数据时，系统会自动：
- 根据配件编码查询近3个月的维修记录
- 统计总的使用次数
- 填充到提交请求的 `recentThreeMonthsRepairCount` 字段

## 技术特点

### 1. 高性能
- 使用索引优化查询性能
- 支持批量查询减少数据库访问

### 2. 容错性
- 统计异常时不影响主流程
- 提供详细的日志记录

### 3. 可扩展性
- 预留了扩展字段
- 支持不同审核类型的记录

## 注意事项

1. **时间范围**：统计的是近3个月（90天）的维修记录
2. **审核状态**：只统计审核结果为"通过"的记录
3. **配件编码**：依赖配件编码进行统计，确保配件编码的准确性
4. **性能考虑**：大量数据时建议使用批量查询接口

## 日志说明

系统会记录详细的操作日志：
- 📊 配件维修记录保存日志
- 📈 维修次数统计日志
- ⚠️ 异常和警告日志
- 🔧 调试信息日志

## 作者信息

- 作者：liusheng
- 实现日期：2025-06-14
- 版本：v1.0
