package com.westcatr.rd.car.business.car.pojo.query;

import com.westcatr.rd.boot.orm.association.annotation.JoinExpression;
import com.westcatr.rd.boot.orm.dto.TimeDTO;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 车辆—信息展示表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "车辆—信息展示表查询对象")
public class CarDisplayInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @QueryCondition
    private Long carId;

    @QueryCondition
    private Long id;

    @Schema(description = "是否轮播展示")
    @QueryCondition
    private Integer isCarousel;

    @QueryCondition
    private Integer sortNum;

    @Schema(description = "车辆id信息")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String carIdInfo;

    @JoinExpression(value = "join car_info ci on ci.id=car_display_info.car_id")
    @QueryCondition(condition = QueryCondition.Condition.LIKE, field = "ci.brand_model")
    @Schema(description = "品牌车型")
    private String carBrandModel;

}
