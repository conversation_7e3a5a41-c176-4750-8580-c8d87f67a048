package com.westcatr.rd.car.business.car.entity;

import java.io.Serializable;
import java.util.Date;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.web.excel.annotation.ExcelField;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 车型信息表—配件
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("car_vehicle_model_info")
@Schema(description = "车型信息表—配件")
public class CarVehicleModelInfo extends Model<CarVehicleModelInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @ExcelField(name = "主键ID")
    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "VIN码")
    @ExcelField(name = "VIN码")
    @Length(max = 255, message = "VIN码长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("vin")
    private String vin;

    @Schema(description = "车型编码")
    @ExcelField(name = "车型编码")
    @Length(max = 255, message = "车型编码长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("model_code")
    private String modelCode;

    @Schema(description = "车型名称")
    @ExcelField(name = "车型名称")
    @Length(max = 255, message = "车型名称长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("model_name")
    private String modelName;

    @Schema(description = "车身类型")
    @ExcelField(name = "车身类型")
    @Length(max = 255, message = "车身类型长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("body_type")
    private String bodyType;

    @Schema(description = "发动机类型")
    @ExcelField(name = "发动机类型")
    @Length(max = 255, message = "发动机类型长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("engine_type")
    private String engineType;

    @Schema(description = "发动机排量")
    @ExcelField(name = "发动机排量")
    @Length(max = 255, message = "发动机排量长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("engine_displacement")
    private String engineDisplacement;

    @Schema(description = "变速器类型")
    @ExcelField(name = "变速器类型")
    @Length(max = 255, message = "变速器类型长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("transmission_type")
    private String transmissionType;

    @Schema(description = "燃料类型")
    @ExcelField(name = "燃料类型")
    @Length(max = 255, message = "燃料类型长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("fuel_type")
    private String fuelType;

    @Schema(description = "创建时间")
    @ExcelField(name = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "更新时间")
    @ExcelField(name = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    // 车辆级别
    @Schema(description = "车辆级别")
    @ExcelField(name = "车辆级别")
    @Length(max = 255, message = "车辆级别长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("vehicle_class")
    private String vehicleClass;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
