package com.westcatr.rd.car.business.car.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.car.business.car.pojo.query.CarBasicInfoQuery;
import com.westcatr.rd.car.business.car.entity.CarBasicInfo;
import com.westcatr.rd.car.business.car.mapper.CarBasicInfoMapper;
import com.westcatr.rd.car.business.car.service.CarBasicInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 车辆—车辆基本信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Service
public class CarBasicInfoServiceImpl extends ServiceImpl<CarBasicInfoMapper, CarBasicInfo> implements CarBasicInfoService {

    @Override
    public IPage<CarBasicInfo> entityPage(CarBasicInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<CarBasicInfo>().create(query));
    }

    @Override
    public CarBasicInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(CarBasicInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(CarBasicInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }
}

