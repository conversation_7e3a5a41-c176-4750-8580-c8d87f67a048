package com.westcatr.rd.car.business.basic.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.boot.web.excel.pojo.ExcelExportParam;
import com.westcatr.rd.boot.web.excel.service.IExcelExportService;
import com.westcatr.rd.car.business.basic.entity.BasicSloganInfo;
import com.westcatr.rd.car.business.basic.pojo.query.BasicSloganInfoQuery;
import com.westcatr.rd.car.business.basic.pojo.vo.BasicSloganInfoVO;
import com.westcatr.rd.car.business.basic.service.BasicSloganInfoService;
import com.westcatr.rd.car.common.annotation.AuditLog;
import com.westcatr.rd.car.enums.OperationTypeEnum;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * BasicSloganInfo 控制器
 * 
 * <AUTHOR>
 * @since 2024-12-26
 */
@Validated
@Tag(name = "基础信息—奖品宣传语配置表接口", description = "基础信息—奖品宣传语配置表接口")
@Slf4j
@RestController
public class BasicSloganInfoController {

    @Autowired
    private BasicSloganInfoService basicSloganInfoService;
    @Autowired
    private IExcelExportService iExcelExportService;

    @Operation(summary = "获取基础信息—奖品宣传语配置表分页数据")
    @PostMapping("/basicSloganInfo/page")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询奖品宣传语配置表分页数据")
    public IResult<IPage<BasicSloganInfo>> getBasicSloganInfoPage(@RequestBody BasicSloganInfoQuery query) {
        return IResult.ok(basicSloganInfoService.entityPage(query));
    }

    @Operation(summary = "获取基础信息—奖品宣传语配置表数据")
    @PostMapping("/basicSloganInfo/get")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询奖品宣传语配置表数据: ID={#id.id}")
    public IResult<BasicSloganInfo> getBasicSloganInfoById(@RequestBody @Id ID id) {
        return IResult.ok(basicSloganInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增基础信息—奖品宣传语配置表数据")
    @PostMapping("/basicSloganInfo/add")
    @AuditLog(operationType = OperationTypeEnum.ADD, description = "新增奖品宣传语配置表数据: ID={#param.id}")
    public IResult addBasicSloganInfo(@RequestBody @Validated(Insert.class) BasicSloganInfo param) {
        return IResult.auto(basicSloganInfoService.saveEntity(param));
    }

    @Operation(summary = "更新基础信息—奖品宣传语配置表数据")
    @PostMapping("/basicSloganInfo/update")
    @AuditLog(operationType = OperationTypeEnum.UPDATE, description = "更新奖品宣传语配置表数据: ID={#param.id}")
    public IResult updateBasicSloganInfoById(@RequestBody @Validated(Update.class) BasicSloganInfo param) {
        return IResult.auto(basicSloganInfoService.updateEntity(param));
    }

    @Operation(summary = "删除基础信息—奖品宣传语配置表数据")
    @PostMapping("/basicSloganInfo/delete")
    @AuditLog(operationType = OperationTypeEnum.DELETE, description = "删除奖品宣传语配置表数据: ID={#id.id}")
    public IResult deleteBasicSloganInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            basicSloganInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取基础信息—奖品宣传语配置表VO分页数据")
    @PostMapping("/basicSloganInfo/voPage")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询奖品宣传语配置表VO分页数据")
    public IResult<IPage<BasicSloganInfoVO>> getBasicSloganInfoVoPage(@RequestBody BasicSloganInfoQuery query) {
        AssociationQuery<BasicSloganInfoVO> associationQuery = new AssociationQuery<>(BasicSloganInfoVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取基础信息—奖品宣传语配置表VO数据")
    @PostMapping("/basicSloganInfo/getVo")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询奖品宣传语配置表VO数据: ID={#id.id}")
    public IResult<BasicSloganInfoVO> getBasicSloganInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<BasicSloganInfoVO> associationQuery = new AssociationQuery<>(BasicSloganInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    @Operation(summary = "导出基础信息—奖品宣传语配置表数据")
    @PostMapping("/basicSloganInfo/export")
    @AuditLog(operationType = OperationTypeEnum.EXPORT, description = "导出奖品宣传语配置表数据")
    public void export(@RequestBody ExcelExportParam<BasicSloganInfoQuery> query) {
        iExcelExportService.exportExcel("基础信息—奖品宣传语配置表数据", BasicSloganInfoVO.class, query, false);
    }

    @Operation(summary = "获取审计日志大小信息")
    @PostMapping("/basicSloganInfo/getAuditLogSize")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "获取审计日志大小信息")
    public IResult<String> getAuditLogSize() {
        return IResult.ok("成功", basicSloganInfoService.getAuditLogSizeInfo());
    }
}
