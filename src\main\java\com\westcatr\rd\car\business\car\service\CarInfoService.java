package com.westcatr.rd.car.business.car.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.car.business.car.entity.CarFlowDataInfo;
import com.westcatr.rd.car.business.car.entity.CarFlowParamDataInfo;
import com.westcatr.rd.car.business.car.entity.CarInfo;
import com.westcatr.rd.car.business.car.pojo.dto.SaveOrUpdateReturnInfoDto;
import com.westcatr.rd.car.business.car.pojo.query.CarInfoQuery;
import com.westcatr.rd.car.business.car.pojo.vo.CarInfoVO;
import com.westcatr.rd.car.business.flow.pojo.dto.CompleteTaskDto;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 车辆—基础信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
public interface CarInfoService extends IService<CarInfo> {

    IPage<CarInfo> entityPage(CarInfoQuery query);

    CarInfo getEntityById(Long id);

    SaveOrUpdateReturnInfoDto saveEntity(CarInfo param) throws Exception;

    SaveOrUpdateReturnInfoDto updateEntity(CarInfo param) throws Exception;

    boolean removeEntityById(Long id);

    void exportTemplate();

    boolean importExcel(MultipartFile file) throws IOException;

    /**
     * 热门推荐车列表
     *
     * @return
     */
    List<CarInfoVO> hotRecommendationList(Integer queryCount);

    CarInfoVO getMyVo(Long id);

    boolean startedFlow(CarInfo carInfo) throws Exception;

    /**
     * 处理流程
     *
     * @param completeTaskDto
     * @return
     */
    Map<String, Object> processingFlow(CompleteTaskDto completeTaskDto);

    IPage<CarInfoVO> myPageVo(CarInfoQuery query);

    /**
     * 获取流程数据
     *
     * @param carId
     * @return
     */
    CarFlowDataInfo getFlowOldData(Long carId);

    /**
     * 获取流程参数详情
     *
     * @param carModelId
     * @return
     */
    CarFlowParamDataInfo getCarFlowParamDataInfo(Long carModelId);

    /**
     * 车辆上下架
     *
     * @param carInfo
     * @return
     */
    boolean upperAndLowerFrames(CarInfo carInfo);
}
