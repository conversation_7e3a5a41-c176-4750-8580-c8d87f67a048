---
description:
globs:
alwaysApply: false
---
# 后端架构与技术栈

## 技术栈概览

- **框架**: Spring Boot
- **ORM**: MyBatis
- **数据库**: MySQL
- **缓存**: Redis
- **文件存储**: 阿里云 OSS
- **工作流引擎**: Flowable
- **权限控制**: Spring Security
- **接口文档**: Swagger

## 模块划分

后端代码主要位于 `src/main/java/com/westcatr/rd/car` 目录，按以下模块组织：

### 业务模块 (`business` 目录)

1. **车辆管理 (car)**
   - 车辆基本信息管理
   - 车型管理
   - 经销商车辆管理

2. **试驾管理 (testdrive)**
   - 试驾预约
   - 试驾记录
   - 试驾数据统计

3. **维修服务 (repair)**
   - 维修工单
   - 配件管理
   - 服务项目管理

4. **二手车 (usedcar)**
   - 二手车信息管理
   - 车辆估价
   - 交易记录

5. **供应商管理 (supplier)**
   - 供应商信息
   - 供应商评分
   - 供应商分类

6. **开放接口 (openapi)**
   - 面向合作伙伴的 API
   - 接口安全控制
   - 流量限制

### 基础架构 (`common` 和 `configs` 目录)

1. **通用组件 (common)**
   - 注解
   - 工具类
   - 通用枚举

2. **配置 (configs)**
   - 安全配置
   - 拦截器
   - 切面
   - 过滤器

3. **工具类 (utils)**
   - 日期处理
   - 字符串处理
   - HTTP 客户端
   - 加解密

## 数据库设计

- 遵循三范式
- 使用 Flyway 进行数据库版本控制，脚本位于 `src/main/resources/db/migration`
- 一般表结构包含:
  - ID (主键)
  - 业务字段
  - create_time (创建时间)
  - update_time (更新时间)
  - create_by (创建人)
  - update_by (更新人)
  - is_deleted (逻辑删除标记)

## 接口设计

- 遵循 RESTful 风格
- 统一的返回格式
- Controller 层进行参数校验，使用 JSR-303 注解
- 敏感数据传输使用加密

## 业务流程

- 使用 Flowable 工作流引擎处理复杂业务流程
- 流程定义文件位于 `src/main/resources/processes`
- 主要应用于:
  - 审批流程
  - 维修流程
  - 交易流程
