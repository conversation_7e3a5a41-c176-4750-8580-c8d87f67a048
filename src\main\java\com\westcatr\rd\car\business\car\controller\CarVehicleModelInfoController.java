package com.westcatr.rd.car.business.car.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.boot.web.excel.pojo.ExcelExportParam;
import com.westcatr.rd.boot.web.excel.service.IExcelExportService;
import com.westcatr.rd.car.business.car.entity.CarVehicleModelInfo;
import com.westcatr.rd.car.business.car.pojo.query.CarVehicleModelInfoQuery;
import com.westcatr.rd.car.business.car.pojo.vo.CarVehicleBiddingModelVO;
import com.westcatr.rd.car.business.car.pojo.vo.CarVehicleModelInfoVO;
import com.westcatr.rd.car.business.car.service.CarVehicleModelInfoService;
import com.westcatr.rd.car.business.car.task.CarVehiclePartsDeduplicationTask;
import com.westcatr.rd.car.common.annotation.AuditLog;
import com.westcatr.rd.car.enums.OperationTypeEnum;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * CarVehicleModelInfo 控制器
 * 
 * <AUTHOR>
 * @since 2024-12-30
 */
@Validated
@Tag(name = "车型信息表—配件接口", description = "车型信息表—配件接口")
@Slf4j
@RestController
public class CarVehicleModelInfoController {

    @Autowired
    private CarVehicleModelInfoService carVehicleModelInfoService;
    @Autowired
    private IExcelExportService iExcelExportService;
    @Autowired
    private CarVehiclePartsDeduplicationTask carVehiclePartsDeduplicationTask;

    @Operation(summary = "获取车型信息表—配件分页数据")
    @PostMapping("/carVehicleModelInfo/page")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车型信息表配件分页数据")
    public IResult<IPage<CarVehicleModelInfo>> getCarVehicleModelInfoPage(@RequestBody CarVehicleModelInfoQuery query) {
        return IResult.ok(carVehicleModelInfoService.entityPage(query));
    }

    @Operation(summary = "获取车型信息表—配件数据")
    @PostMapping("/carVehicleModelInfo/get")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车型信息表配件数据: ID={#id.id}")
    public IResult<CarVehicleModelInfo> getCarVehicleModelInfoById(@RequestBody @Id ID id) {
        return IResult.ok(carVehicleModelInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增车型信息表—配件数据")
    @PostMapping("/carVehicleModelInfo/add")
    @AuditLog(operationType = OperationTypeEnum.ADD, description = "新增车型信息表配件数据: ID={#param.id}")
    public IResult addCarVehicleModelInfo(@RequestBody @Validated(Insert.class) CarVehicleModelInfo param) {
        return IResult.auto(carVehicleModelInfoService.saveEntity(param));
    }

    @Operation(summary = "更新车型信息表—配件数据")
    @PostMapping("/carVehicleModelInfo/update")
    @AuditLog(operationType = OperationTypeEnum.UPDATE, description = "更新车型信息表配件数据: ID={#param.id}")
    public IResult updateCarVehicleModelInfoById(@RequestBody @Validated(Update.class) CarVehicleModelInfo param) {
        return IResult.auto(carVehicleModelInfoService.updateEntity(param));
    }

    @Operation(summary = "删除车型信息表—配件数据")
    @PostMapping("/carVehicleModelInfo/delete")
    @AuditLog(operationType = OperationTypeEnum.DELETE, description = "删除车型信息表配件数据: ID={#id.id}")
    public IResult deleteCarVehicleModelInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            carVehicleModelInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取车型信息表—配件VO分页数据")
    @PostMapping("/carVehicleModelInfo/voPage")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车型信息表配件VO分页数据")
    public IResult<IPage<CarVehicleModelInfoVO>> getCarVehicleModelInfoVoPage(
            @RequestBody CarVehicleModelInfoQuery query) {
        return IResult.ok(carVehicleModelInfoService.myVoPage(query));
    }

    @Operation(summary = "获取车型信息表—配件VO数据")
    @PostMapping("/carVehicleModelInfo/getVo")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车型信息表配件VO数据: ID={#id.id}")
    public IResult<CarVehicleModelInfoVO> getCarVehicleModelInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<CarVehicleModelInfoVO> associationQuery = new AssociationQuery<>(CarVehicleModelInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    /*
     * @Operation(summary = "获取竞价配件库车型信息表—配件VO分页数据")
     * 
     * @PostMapping("/carVehicleModelInfo/biddingVoPage")
     * 
     * @AuditLog(operationType = OperationTypeEnum.QUERY, description =
     * "查询竞价配件库车型信息表配件VO分页数据")
     * public IResult<IPage<CarVehicleModelInfoVO>>
     * getCarVehicleModelInfoBiddingVoPage(
     * 
     * @RequestBody CarVehicleModelInfoQuery query) {
     * return IResult.ok(carVehicleModelInfoService.myBiddingVoPage(query));
     * }
     */

    @Operation(summary = "获取竞价配件库车型信息表VO分页数据")
    @PostMapping("/carVehicleModelInfo/biddingModelVoPage")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询竞价配件库车型信息表VO分页数据")
    public IResult<IPage<CarVehicleBiddingModelVO>> getCarVehicleBiddingModelVoPage(
            @RequestBody CarVehicleModelInfoQuery query) {
        return IResult.ok(carVehicleModelInfoService.getBiddingModelVoPage(query));
    }

    @Operation(summary = "导出车型信息表—配件数据")
    @PostMapping("/carVehicleModelInfo/export")
    @AuditLog(operationType = OperationTypeEnum.EXPORT, description = "导出车型信息表配件数据")
    public void export(@RequestBody ExcelExportParam<CarVehicleModelInfoQuery> query) {
        iExcelExportService.exportExcel("车型信息表—配件数据", CarVehicleModelInfoVO.class, query, false);
    }

    @Operation(summary = "手动执行配件库去重任务")
    @PostMapping("/carVehicleModelInfo/deduplication")
    @AuditLog(operationType = OperationTypeEnum.UPDATE, description = "执行配件库去重任务")
    public IResult manualDeduplication() {
        log.info("开始手动执行配件库去重任务");
        carVehiclePartsDeduplicationTask.deduplicateCarParts();
        return IResult.ok("配件库去重任务执行成功");
    }

    @Operation(summary = "删除竞价配件和工时数据")
    @PostMapping("/carVehicleModelInfo/removeBiddingPartsAndLabor")
    @AuditLog(operationType = OperationTypeEnum.DELETE, description = "删除竞价配件和工时数据: ID={#id.id}")
    public IResult removeBiddingPartsAndLabor(@RequestBody @Id ID id) {
        return IResult.auto(carVehicleModelInfoService.removeBiddingPartsAndLabor(id.longId()));
    }
}
