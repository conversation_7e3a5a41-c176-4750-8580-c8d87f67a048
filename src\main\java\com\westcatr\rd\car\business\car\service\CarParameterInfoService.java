package com.westcatr.rd.car.business.car.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.car.business.car.entity.CarParameterInfo;
import com.westcatr.rd.car.business.car.pojo.query.CarParameterInfoQuery;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Map;

/**
 * <p>
 * 车辆—参数信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
public interface CarParameterInfoService extends IService<CarParameterInfo> {

    IPage<CarParameterInfo> entityPage(CarParameterInfoQuery query);

    CarParameterInfo getEntityById(Long id);

    boolean saveEntity(CarParameterInfo param);

    boolean updateEntity(CarParameterInfo param);

    boolean removeEntityById(Long id);

    void exportTemplate();

    CarParameterInfo importExcel(Long carId, Long carModelId, MultipartFile file) throws IOException;

    /**
     * 根据车型id获取车型的参数：27张表集合
     *
     * @param carModelId
     * @return
     */
    Map<String, Map<String, String>> collectBusinessDataSorted(Long carModelId) throws Exception;

    /**
     * 新增或者更新参数（27张表集合）
     *
     * @param data
     * @throws Exception
     */
    Boolean processEntities(Map<String, Object> data) throws Exception;

    /**
     * 导出模板（27张表集合）
     */
    void exportExcelAllTable(ByteArrayOutputStream byteArrayOutputStream) throws Exception;

    /**
     * 返回部分属性的值
     *
     * @param searchTerm
     * @return
     */
    Map<String, String> findEntitiesByDescription(String searchTerm) throws Exception;

    /**
     * 导入excel，所有27个表
     *
     * @param file
     * @return
     * @throws IOException
     */
    Map<String, Map<String, String>> importExcel(MultipartFile file) throws IOException;

    /**
     * 批量删除27个表
     *
     * @param carId
     * @param carModelId
     * @return
     */
    void removeBatchByIdsAllTable(Long carId, Long carModelId) throws Exception;
}
