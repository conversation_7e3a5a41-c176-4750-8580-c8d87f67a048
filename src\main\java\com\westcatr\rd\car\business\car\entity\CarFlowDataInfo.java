package com.westcatr.rd.car.business.car.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("car_flow_data_info")
@Schema(description = "")
public class CarFlowDataInfo extends Model<CarFlowDataInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = {Update.class})
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("car_id")
    private Long carId;

    @Length(max = 255, message = "长度不能超过255", groups = {Insert.class, Update.class})
    @TableField("flow_id")
    private String flowId;

    @Length(max = 65535, message = "长度不能超过65535", groups = {Insert.class, Update.class})
    @TableField("bus_json")
    private String busJson;

    @TableField("create_user_id")
    private Long createUserId;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
