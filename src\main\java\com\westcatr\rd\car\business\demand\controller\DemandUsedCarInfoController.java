package com.westcatr.rd.car.business.demand.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.boot.web.excel.pojo.ExcelExportParam;
import com.westcatr.rd.boot.web.excel.service.IExcelExportService;
import com.westcatr.rd.car.business.demand.entity.DemandUsedCarInfo;
import com.westcatr.rd.car.business.demand.pojo.dto.GetPhoneNumberReturnDto;
import com.westcatr.rd.car.business.demand.pojo.dto.GetUserPhoneAuditDto;
import com.westcatr.rd.car.business.demand.pojo.query.DemandUsedCarApplicationPhoneInfoQuery;
import com.westcatr.rd.car.business.demand.pojo.query.DemandUsedCarInfoQuery;
import com.westcatr.rd.car.business.demand.pojo.vo.DemandUsedCarApplicationPhoneInfoVO;
import com.westcatr.rd.car.business.demand.pojo.vo.DemandUsedCarInfoVO;
import com.westcatr.rd.car.business.demand.service.DemandUsedCarInfoService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * DemandUsedCarInfo 控制器
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Validated
@Tag(name = "二手车需求信息表接口", description = "二手车需求信息表接口")
@Slf4j
@RestController
public class DemandUsedCarInfoController {

    @Autowired
    private DemandUsedCarInfoService demandUsedCarInfoService;
    @Autowired
    private IExcelExportService iExcelExportService;

    @Operation(summary = "获取二手车需求信息表分页数据")
    @PostMapping("/demandUsedCarInfo/page")
    public IResult<IPage<DemandUsedCarInfo>> getDemandUsedCarInfoPage(@RequestBody DemandUsedCarInfoQuery query) {
        return IResult.ok(demandUsedCarInfoService.entityPage(query));
    }

    @Operation(summary = "获取二手车需求信息表数据")
    @PostMapping("/demandUsedCarInfo/get")
    public IResult<DemandUsedCarInfo> getDemandUsedCarInfoById(@RequestBody @Id ID id) {
        return IResult.ok(demandUsedCarInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增二手车需求信息表数据")
    @PostMapping("/demandUsedCarInfo/add")
    public IResult addDemandUsedCarInfo(@RequestBody @Validated(Insert.class) DemandUsedCarInfo param) {
        return IResult.auto(demandUsedCarInfoService.saveEntity(param));
    }

    @Operation(summary = "更新二手车需求信息表数据")
    @PostMapping("/demandUsedCarInfo/update")
    public IResult updateDemandUsedCarInfoById(@RequestBody @Validated(Update.class) DemandUsedCarInfo param) {
        return IResult.auto(demandUsedCarInfoService.updateEntity(param));
    }

    @Operation(summary = "删除二手车需求信息表数据")
    @PostMapping("/demandUsedCarInfo/delete")
    public IResult deleteDemandUsedCarInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            demandUsedCarInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取二手车需求信息表VO分页数据")
    @PostMapping("/demandUsedCarInfo/voPage")
    public IResult<IPage<DemandUsedCarInfoVO>> getDemandUsedCarInfoVoPage(@RequestBody DemandUsedCarInfoQuery query) {
        return IResult.ok(demandUsedCarInfoService.myVoPage(query));
    }

    @Operation(summary = "获取二手车需求信息表VO数据")
    @PostMapping("/demandUsedCarInfo/getVo")
    public IResult<DemandUsedCarInfoVO> getDemandUsedCarInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<DemandUsedCarInfoVO> associationQuery = new AssociationQuery<>(DemandUsedCarInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    @Operation(summary = "导出二手车需求信息表数据")
    @PostMapping("/demandUsedCarInfo/export")
    public void export(@RequestBody ExcelExportParam<DemandUsedCarInfoQuery> query) {
        iExcelExportService.exportExcel("二手车需求信息表数据", DemandUsedCarInfoVO.class, query, false);
    }

    @Operation(summary = "获取手机号码")
    @PostMapping("/demandUsedCarInfo/getMobilePhoneNumber")
    public IResult<GetPhoneNumberReturnDto> getMobilePhoneNumber(Long id) {
        return IResult.ok(demandUsedCarInfoService.mobilePhoneNumberTheUserNeed(id));
    }

    @Operation(summary = "忽略需求")
    @PostMapping("/demandUsedCarInfo/ignoreNeeds")
    public IResult ignoreNeeds(@RequestBody @Id ID id) {
        return IResult.auto(demandUsedCarInfoService.ignoreNeeds(id.longId()));
    }

    @Operation(summary = "用户是否同意供应商获取手机号码")
    @PostMapping("/demandUsedCarInfo/getUserPhoneNumber")
    public IResult getUserPhoneNumber(@RequestBody GetUserPhoneAuditDto getUserPhoneAuditDto) {
        return IResult.auto(demandUsedCarInfoService.getUserPhoneNumber(getUserPhoneAuditDto));
    }

    @Operation(summary = "供应商-我的需求")
    @PostMapping("/demandUsedCarInfo/getPhoneVoPage")
    public IResult<IPage<DemandUsedCarApplicationPhoneInfoVO>> getPhoneVoPage(
            @RequestBody DemandUsedCarApplicationPhoneInfoQuery query) {
        return IResult.ok(demandUsedCarInfoService.getPhoneVoPage(query));
    }

    @Operation(summary = "我发布的需求")
    @PostMapping("/demandUsedCarInfo/myPublishedRequirements")
    public IResult<IPage<DemandUsedCarInfoVO>> myPublishedRequirements(@RequestBody DemandUsedCarInfoQuery query) {
        return IResult.ok(demandUsedCarInfoService.myPublishedRequirements(query));
    }
}
