package com.westcatr.rd.car.business.demand.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.car.business.demand.entity.DemandInfo;
import com.westcatr.rd.car.business.demand.pojo.dto.GetPhoneNumberReturnDto;
import com.westcatr.rd.car.business.demand.pojo.dto.GetUserPhoneAuditDto;
import com.westcatr.rd.car.business.demand.pojo.query.DemandApplicationPhoneInfoQuery;
import com.westcatr.rd.car.business.demand.pojo.query.DemandInfoQuery;
import com.westcatr.rd.car.business.demand.pojo.vo.DemandApplicationPhoneInfoVO;
import com.westcatr.rd.car.business.demand.pojo.vo.DemandInfoVO;

/**
 * <p>
 * 需求—基本信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
public interface DemandInfoService extends IService<DemandInfo> {

    IPage<DemandInfo> entityPage(DemandInfoQuery query);

    DemandInfo getEntityById(Long id);

    boolean saveEntity(DemandInfo param);

    boolean updateEntity(DemandInfo param);

    boolean removeEntityById(Long id);

    GetPhoneNumberReturnDto mobilePhoneNumberTheUserNeed(Long id);

    /**
     * 供应商忽略需求
     *
     * @param id
     * @return
     */
    boolean ignoreNeeds(Long id);

    IPage<DemandInfoVO> myVoPage(DemandInfoQuery query);

    /**
     * 用户审批是否能让供应商获取自己的电话号码
     *
     * @param getUserPhoneAuditDto
     * @return
     */
    boolean getUserPhoneNumber(GetUserPhoneAuditDto getUserPhoneAuditDto);

    /**
     * 供应商我的申请分页查询列表
     *
     * @param query
     * @return
     */
    IPage<DemandApplicationPhoneInfoVO> getPhoneVoPage(DemandApplicationPhoneInfoQuery query);

    /**
     * 我的发布需求
     *
     * @param query
     * @return
     */
    IPage<DemandInfoVO> myPublishedRequirements(DemandInfoQuery query);
}
