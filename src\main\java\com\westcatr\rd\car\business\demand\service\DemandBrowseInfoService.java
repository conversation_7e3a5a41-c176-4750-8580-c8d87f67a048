package com.westcatr.rd.car.business.demand.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.car.business.demand.entity.DemandBrowseInfo;
import com.westcatr.rd.car.business.demand.pojo.query.DemandBrowseInfoQuery;

/**
 * <p>
 * 需求—当前登录用户查看车辆次数信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
public interface DemandBrowseInfoService extends IService<DemandBrowseInfo> {

    IPage<DemandBrowseInfo> entityPage(DemandBrowseInfoQuery query);

    DemandBrowseInfo getEntityById(Long id);

    void saveEntity(DemandBrowseInfo param);

    boolean updateEntity(DemandBrowseInfo param);

    boolean removeEntityById(Long id);
}
