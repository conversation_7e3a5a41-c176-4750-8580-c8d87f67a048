# 一、接口鉴权说明

泛泛助手开放平台接口均采用RSA+AES(ECB模式)进行接口请求的签名验签及敏感信息加解密

*   商户随机生成key,并使用泛泛助手颁发的RSA公钥对key进行加密后放到请求报文中
*   使用key 对业务数据进行AES加密
*   商户使用泛泛助手提供的RSA商户私钥对请求参数加签

**sign签名规则为:**

第1步,设所有发送或者接收到的数据为集合 M,将集合 M 内非空参数值的参数按照参数名 ASCII 码从小到大排序(字典序),使用URL 键值对的格式(即 `key1=value1&key2=value2...`)拼接成字符串 stringA。

**特别注意以下重要规则:**
*   参数名 ASCII 码从小到大排序(字典序);
*   如果参数的值为空(null)时不参与签名;
*   参数名区分大小写;
*   传送的 sign 参数不参与签名

# 二、接口内容

## 1、接收维修厂订单提交接口

### 1. 接口说明
接收维修厂订单提交接口

### 2. 接口地址
url: `/logistics/openapi/fee/vehicleMaintenance/submit`
method: POST

### 3. 请求参数

#### 公共请求参数:

| 字段名称        | 数据类型 | 长度   | 是否必需 | 描述                               |
|-----------------|----------|--------|----------|------------------------------------|
| partnerId       | string   | 24     | 是       | 商户号【由泛泛助手在上线前分配】     |
| orderNo         | string   | 1-32   | 是       | 商户订单号                         |
| requestDateTime | long     | 13     | 否       | 时间戳                             |
| version         | string   | 16     | 是       | 默认1.0                            |
| sign            | string   | 512    | 是       | 签名串                             |
| key             | string   | 512    | 否       | rsa私钥加密后的秘钥串, 用于敏感信息AES加解密及报文RSA加解签 |
| content         | string   | 无限制 | 否       | 业务参数json字符串, AES加密后并base64编码 |

#### content 业务参数
见吉洛提供接口文档 4.1 接收维修订单提交

#### 公共响应参数:

| 名称            | 数据类型 | 长度   | 是否必需 | 描述                                   |
|-----------------|----------|--------|----------|----------------------------------------|
| resultCode      | string   | 1-6    | 是       | “0000”成功, 其它失败                   |
| resultMessage   | string   | 1-255  | 是       | 应答描述                               |
| key             | string   | 512    | 否       | rsa私钥加密后的秘钥串, 用于敏感信息AES加解密及报文RSA加解签 |
| sign            | string   | 512    | 是       | 签名串, 对data中的数据加签             |
| data            |          |        |          |                                        |

#### data数据:

| 字段名称        | 数据类型 | 长度 | 是否必需 | 描述               |
|-----------------|----------|------|----------|--------------------|
| partnerId       | string   | 24   | 是       | 商户号             |
| orderNo         | string   | 1-32 | 是       | 商户请求时的订单号 |
| responseDateTime| long     | 13   | 是       | 时间戳             |

## 2、维修订单状态查询接口

### 1. 接口说明
维修订单状态查询接口

### 2. 接口地址
url: `/logistics/openapi/fee/vehicleMaintenance/queryStatus`
method: POST

### 3. 请求参数

#### 公共请求参数:

| 字段名称        | 数据类型 | 长度   | 是否必需 | 描述                               |
|-----------------|----------|--------|----------|------------------------------------|
| partnerId       | string   | 24     | 是       | 商户号【由泛泛助手在上线前分配】     |
| orderNo         | string   | 1-32   | 是       | 商户订单号                         |
| requestDateTime | long     | 13     | 否       | 时间戳                             |
| version         | string   | 16     | 是       | 默认1.0                            |
| sign            | string   | 512    | 是       | 签名串                             |
| key             | string   | 512    | 否       | rsa私钥加密后的秘钥串, 用于敏感信息AES加解密及报文RSA加解签 |
| content         | string   | 无限制 | 否       | 业务参数json字符串, AES加密后并base64编码 |

#### content 业务参数

| 字段名称        | 数据类型 | 长度 | 是否必需 | 描述   |
|-----------------|----------|------|----------|--------|
| repairOrderCode | string   | 32   | 是       | 订单号 |

#### 公共响应参数:

| 名称            | 数据类型 | 长度   | 是否必需 | 描述                                   |
|-----------------|----------|--------|----------|----------------------------------------|
| resultCode      | string   | 1-6    | 是       | “0000”成功, 其它失败                   |
| resultMessage   | string   | 1-255  | 是       | 应答描述                               |
| key             | string   | 512    | 否       | rsa私钥加密后的秘钥串, 用于敏感信息AES加解密及报文RSA加解签 |
| sign            | string   | 512    | 是       | 签名串, 对data中的数据加签             |
| data            |          |        |          |                                        |

#### data数据:

| 字段名称        | 数据类型 | 长度 | 是否必需 | 描述   |
|-----------------|----------|------|----------|--------|
| repairOrderCode | string   | 32   | 是       | 订单号 |
| status          | string   | 32   | 是       | 状态   |
| processTime     | long     | 13   | 是       | 时间戳 |

## 3、修理厂材料查询接口

### 1. 接口说明
修理厂材料查询接口

### 2. 接口地址
url: `/logistics/openapi/fee/vehicleMaintenance/queryVehicleGarageMaterial`
method: POST

### 3. 请求参数

#### 公共请求参数:

| 字段名称        | 数据类型 | 长度   | 是否必需 | 描述                               |
|-----------------|----------|--------|----------|------------------------------------|
| partnerId       | string   | 24     | 是       | 商户号【由泛泛助手在上线前分配】     |
| orderNo         | string   | 1-32   | 是       | 商户订单号                         |
| requestDateTime | long     | 13     | 否       | 时间戳                             |
| version         | string   | 16     | 是       | 默认1.0                            |
| sign            | string   | 512    | 是       | 签名串                             |
| key             | string   | 512    | 否       | rsa私钥加密后的秘钥串, 用于敏感信息AES加解密及报文RSA加解签 |
| content         | string   | 无限制 | 否       | 业务参数json字符串, AES加密后并base64编码 |

#### content 业务参数

| 字段名称 | 数据类型 | 长度 | 是否必需 | 描述     |
|----------|----------|------|----------|----------|
| garageId | string   | 24   | 是       | 修理厂ID |

#### 公共响应参数:

| 名称            | 数据类型 | 长度   | 是否必需 | 描述                                   |
|-----------------|----------|--------|----------|----------------------------------------|
| resultCode      | string   | 1-6    | 是       | “0000”成功, 其它失败                   |
| resultMessage   | string   | 1-255  | 是       | 应答描述                               |
| key             | string   | 512    | 否       | rsa私钥加密后的秘钥串, 用于敏感信息AES加解密及报文RSA加解签 |
| sign            | string   | 512    | 是       | 签名串, 对data中的数据加签             |
| data            |          |        |          |                                        |

#### data数据:

| 字段名称          | 数据类型    | 长度 | 是否必需 | 描述                     |
|-------------------|-------------|------|----------|--------------------------|
| garageId          | string      | 24   | 是       | 修理厂id                 |
| garageName        | string      | 24   | 是       | 修理厂名称               |
| materialId        | string      | 24   | 是       | 材料id                   |
| name              | string      | 255  |          | 材料名称                 |
| manufacturer      | string      | 128  |          | 品牌                     |
| model             | string      | 64   |          | 型号                     |
| specification     | string      | 128  |          | 规格                     |
| price             | BigDecimal  | 11   |          | 不含税单价               |
| priceExclusiveTax | BigDecimal  | 11   |          | 含税单价                 |
| units             | string      | 16   |          | 单位                     |
| workHour          | double      | 5    |          | 工时数                   |
| workPrice         | BigDecimal  | 11   |          | 工时费折前单价(不含税)   |
| workPriceTax      | BigDecimal  | 11   |          | 工时费折前单价(含税)     |