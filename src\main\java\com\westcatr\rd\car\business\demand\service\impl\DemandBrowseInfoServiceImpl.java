package com.westcatr.rd.car.business.demand.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.car.business.demand.entity.DemandBrowseInfo;
import com.westcatr.rd.car.business.demand.mapper.DemandBrowseInfoMapper;
import com.westcatr.rd.car.business.demand.pojo.query.DemandBrowseInfoQuery;
import com.westcatr.rd.car.business.demand.service.DemandBrowseInfoService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 需求—当前登录用户查看车辆次数信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Service
public class DemandBrowseInfoServiceImpl extends ServiceImpl<DemandBrowseInfoMapper, DemandBrowseInfo> implements DemandBrowseInfoService {

    @Override
    public IPage<DemandBrowseInfo> entityPage(DemandBrowseInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<DemandBrowseInfo>().create(query));
    }

    @Override
    public DemandBrowseInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Async
    @Override
    public void saveEntity(DemandBrowseInfo param) {
        this.save(param);
    }

    @Override
    public boolean updateEntity(DemandBrowseInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }
}

