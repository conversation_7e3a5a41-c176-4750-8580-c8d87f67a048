package com.westcatr.rd.car.business.car.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.boot.sso.util.AuthUtil;
import com.westcatr.rd.car.business.car.entity.CarVehicleModelInfo;
import com.westcatr.rd.car.business.car.entity.CarVehicleModelPartsInfo;
import com.westcatr.rd.car.business.car.entity.CarVehicleOperationStatisticsInfo;
import com.westcatr.rd.car.business.car.mapper.CarVehicleModelInfoMapper;
import com.westcatr.rd.car.business.car.pojo.query.CarVehicleModelInfoQuery;
import com.westcatr.rd.car.business.car.pojo.vo.CarVehicleBiddingModelVO;
import com.westcatr.rd.car.business.car.pojo.vo.CarVehicleModelInfoVO;
import com.westcatr.rd.car.business.car.service.CarVehicleModelInfoService;
import com.westcatr.rd.car.business.car.service.CarVehicleModelPartsInfoService;
import com.westcatr.rd.car.business.car.service.CarVehicleOperationStatisticsInfoService;
import com.westcatr.rd.car.business.repair.entity.CarVehicleModelLaborHourInfo;
import com.westcatr.rd.car.business.repair.service.CarVehicleModelLaborHourInfoService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <p>
 * 车型信息表—配件 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Service
public class CarVehicleModelInfoServiceImpl extends ServiceImpl<CarVehicleModelInfoMapper, CarVehicleModelInfo>
        implements CarVehicleModelInfoService {

    @Autowired
    private CarVehicleModelPartsInfoService carVehicleModelPartsInfoService;

    @Autowired
    private CarVehicleOperationStatisticsInfoService carVehicleOperationStatisticsInfoService;

    @Autowired
    private CarVehicleModelLaborHourInfoService carVehicleModelLaborHourInfoService;

    @Override
    public IPage<CarVehicleModelInfo> entityPage(CarVehicleModelInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<CarVehicleModelInfo>().create(query));
    }

    @Override
    public CarVehicleModelInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(CarVehicleModelInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(CarVehicleModelInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        if (this.removeById(id)) {
            carVehicleModelPartsInfoService
                    .remove(new QueryWrapper<CarVehicleModelPartsInfo>().eq("vehicle_model_id", id));
            return true;
        }
        return false;
    }

    @Override
    public IPage<CarVehicleModelInfoVO> myVoPage(CarVehicleModelInfoQuery query) {
        // 1. 先查询主表数据
        IPage<CarVehicleModelInfo> page = this.page(PageDTO.page(query),
                new WrapperFactory<CarVehicleModelInfo>().create(query));

        if (CollUtil.isEmpty(page.getRecords())) {
            return new Page<CarVehicleModelInfoVO>().setRecords(new ArrayList<>())
                    .setTotal(0).setSize(page.getSize()).setCurrent(page.getCurrent());
        }

        // 2. 获取所有车型ID
        List<Long> modelIds = page.getRecords().stream()
                .map(CarVehicleModelInfo::getId)
                .collect(Collectors.toList());

        // 3. 一次性查询所有车型的配件数量
        Map<Long, Long> partsCountMap = carVehicleModelPartsInfoService.getPartsCountByModelIds(modelIds);

        // 4. 转换数据
        List<CarVehicleModelInfoVO> records = page.getRecords().stream().map(x -> {
            CarVehicleModelInfoVO vo = new CarVehicleModelInfoVO();
            BeanUtils.copyProperties(x, vo);
            vo.setPartsCount(partsCountMap.getOrDefault(x.getId(), 0L));
            return vo;
        }).collect(Collectors.toList());

        // 5. 记录操作统计
        if (StrUtil.isNotBlank(query.getModelCode()) || StrUtil.isNotBlank(query.getModelName())) {
            List<CarVehicleOperationStatisticsInfo> statisticsInfos = new ArrayList<>();

            // 按照车型名称分组
            Map<String, List<CarVehicleModelInfoVO>> groupByModelName = records.stream()
                    .collect(Collectors.groupingBy(CarVehicleModelInfoVO::getModelName));
            String fullName = AuthUtil.getUserE().getFullName();
            groupByModelName.forEach((modelName, voList) -> {
                if (StrUtil.isNotBlank(query.getModelCode())) {
                    statisticsInfos.add(createStatisticsInfo("车型查询", modelName, "车型编码", fullName));
                }
                if (StrUtil.isNotBlank(query.getModelName())) {
                    statisticsInfos.add(createStatisticsInfo("车型查询", modelName, "车型名称", fullName));
                }
            });

            if (!statisticsInfos.isEmpty()) {
                carVehicleOperationStatisticsInfoService.saveBatch(statisticsInfos);
            }
        }

        // 6. 构建返回结果
        return new Page<CarVehicleModelInfoVO>()
                .setRecords(records)
                .setTotal(page.getTotal())
                .setSize(page.getSize())
                .setCurrent(page.getCurrent());
    }

    @Override
    public IPage<CarVehicleModelInfoVO> myBiddingVoPage(CarVehicleModelInfoQuery query) {
        // 1. 先不分页查询所有符合条件的主表数据
        List<CarVehicleModelInfo> allRecords = this.list(
                new WrapperFactory<CarVehicleModelInfo>().create(query));

        if (CollUtil.isEmpty(allRecords)) {
            Page<CarVehicleModelInfoVO> emptyPage = new Page<>();
            emptyPage.setRecords(new ArrayList<>());
            emptyPage.setTotal(0);
            // 使用PageDTO获取分页参数
            IPage<?> pageParam = PageDTO.page(query);
            emptyPage.setSize(pageParam.getSize());
            emptyPage.setCurrent(pageParam.getCurrent());
            return emptyPage;
        }

        // 2. 获取所有车型ID
        List<Long> modelIds = allRecords.stream()
                .map(CarVehicleModelInfo::getId)
                .collect(Collectors.toList());

        // 3. 查询每个车型的竞价配件数量
        Map<Long, Long> biddingPartsCountMap = carVehicleModelPartsInfoService.getBiddingPartsCountByModelIds(modelIds);

        // 4. 过滤掉没有竞价配件的车型
        List<CarVehicleModelInfoVO> filteredRecords = allRecords.stream()
                .filter(x -> biddingPartsCountMap.containsKey(x.getId()) && biddingPartsCountMap.get(x.getId()) > 0)
                .map(x -> {
                    CarVehicleModelInfoVO vo = new CarVehicleModelInfoVO();
                    BeanUtils.copyProperties(x, vo);
                    vo.setPartsCount(biddingPartsCountMap.getOrDefault(x.getId(), 0L));
                    return vo;
                }).collect(Collectors.toList());

        // 5. 记录操作统计
        if (StrUtil.isNotBlank(query.getModelCode()) || StrUtil.isNotBlank(query.getModelName())) {
            List<CarVehicleOperationStatisticsInfo> statisticsInfos = new ArrayList<>();

            // 按照车型名称分组
            Map<String, List<CarVehicleModelInfoVO>> groupByModelName = filteredRecords.stream()
                    .collect(Collectors.groupingBy(CarVehicleModelInfoVO::getModelName));
            String fullName = AuthUtil.getUserE().getFullName();
            groupByModelName.forEach((modelName, voList) -> {
                if (StrUtil.isNotBlank(query.getModelCode())) {
                    statisticsInfos.add(createStatisticsInfo("竞价车型查询", modelName, "车型编码", fullName));
                }
                if (StrUtil.isNotBlank(query.getModelName())) {
                    statisticsInfos.add(createStatisticsInfo("竞价车型查询", modelName, "车型名称", fullName));
                }
            });

            if (!statisticsInfos.isEmpty()) {
                carVehicleOperationStatisticsInfoService.saveBatch(statisticsInfos);
            }
        }

        // 6. 手动进行分页处理
        long total = filteredRecords.size();

        // 使用PageDTO获取分页参数
        IPage<?> pageParam = PageDTO.page(query);
        long size = pageParam.getSize();
        long current = pageParam.getCurrent();
        long start = (current - 1) * size;
        long end = Math.min(start + size, total);

        // 处理边界情况
        List<CarVehicleModelInfoVO> pageRecords = new ArrayList<>();
        if (start < total) {
            pageRecords = filteredRecords.subList((int) start, (int) end);
        }

        // 7. 构建返回结果
        return new Page<CarVehicleModelInfoVO>()
                .setRecords(pageRecords)
                .setTotal(total)
                .setSize(size)
                .setCurrent(current);
    }

    @Override
    public IPage<CarVehicleBiddingModelVO> getBiddingModelVoPage(CarVehicleModelInfoQuery query) {
        // 1. 先不分页查询所有符合条件的主表数据
        List<CarVehicleModelInfo> allRecords = this.list(
                new WrapperFactory<CarVehicleModelInfo>().create(query));

        if (CollUtil.isEmpty(allRecords)) {
            Page<CarVehicleBiddingModelVO> emptyPage = new Page<>();
            emptyPage.setRecords(new ArrayList<>());
            emptyPage.setTotal(0);
            // 使用PageDTO获取分页参数
            IPage<?> pageParam = PageDTO.page(query);
            emptyPage.setSize(pageParam.getSize());
            emptyPage.setCurrent(pageParam.getCurrent());
            return emptyPage;
        }

        // 2. 获取所有车型ID
        List<Long> modelIds = allRecords.stream()
                .map(CarVehicleModelInfo::getId)
                .collect(Collectors.toList());

        // 3. 查询每个车型的竞价配件数量
        Map<Long, Long> biddingPartsCountMap = carVehicleModelPartsInfoService.getBiddingPartsCountByModelIds(modelIds);

        // 4. 查询每个车型的竞价工时数量
        Map<Long, Long> biddingHoursCountMap = carVehicleModelLaborHourInfoService
                .getBiddingHoursCountByModelIds(modelIds);

        // 5. 过滤掉没有竞价配件的车型，并转换为新的VO
        List<CarVehicleBiddingModelVO> filteredRecords = allRecords.stream()
                .filter(x -> biddingPartsCountMap.containsKey(x.getId()) && biddingPartsCountMap.get(x.getId()) > 0)
                .map(x -> {
                    CarVehicleBiddingModelVO vo = new CarVehicleBiddingModelVO();
                    BeanUtils.copyProperties(x, vo);
                    vo.setBiddingPartsCount(biddingPartsCountMap.getOrDefault(x.getId(), 0L));
                    vo.setBiddingHoursCount(biddingHoursCountMap.getOrDefault(x.getId(), 0L));
                    return vo;
                }).collect(Collectors.toList());

        // 6. 记录操作统计
        if (StrUtil.isNotBlank(query.getModelCode()) || StrUtil.isNotBlank(query.getModelName())) {
            List<CarVehicleOperationStatisticsInfo> statisticsInfos = new ArrayList<>();

            // 按照车型名称分组
            Map<String, List<CarVehicleBiddingModelVO>> groupByModelName = filteredRecords.stream()
                    .collect(Collectors.groupingBy(CarVehicleBiddingModelVO::getModelName));
            String fullName = AuthUtil.getUserE().getFullName();
            groupByModelName.forEach((modelName, voList) -> {
                if (StrUtil.isNotBlank(query.getModelCode())) {
                    statisticsInfos.add(createStatisticsInfo("竞价车型查询", modelName, "车型编码", fullName));
                }
                if (StrUtil.isNotBlank(query.getModelName())) {
                    statisticsInfos.add(createStatisticsInfo("竞价车型查询", modelName, "车型名称", fullName));
                }
            });

            if (!statisticsInfos.isEmpty()) {
                carVehicleOperationStatisticsInfoService.saveBatch(statisticsInfos);
            }
        }

        // 7. 手动进行分页处理
        long total = filteredRecords.size();

        // 使用PageDTO获取分页参数
        IPage<?> pageParam = PageDTO.page(query);
        long size = pageParam.getSize();
        long current = pageParam.getCurrent();
        long start = (current - 1) * size;
        long end = Math.min(start + size, total);

        // 处理边界情况
        List<CarVehicleBiddingModelVO> pageRecords = new ArrayList<>();
        if (start < total) {
            pageRecords = filteredRecords.subList((int) start, (int) end);
        }

        // 8. 构建返回结果
        return new Page<CarVehicleBiddingModelVO>()
                .setRecords(pageRecords)
                .setTotal(total)
                .setSize(size)
                .setCurrent(current);
    }

    /**
     * 创建操作统计信息
     */
    private CarVehicleOperationStatisticsInfo createStatisticsInfo(String operationType, String modelName,
            String operationItem, String fullName) {
        CarVehicleOperationStatisticsInfo info = new CarVehicleOperationStatisticsInfo();
        info.setOperationType(operationType);
        info.setInvolvedVehicleModel(modelName);
        info.setOperationItem(operationItem);
        info.setOperatorFullName(fullName);
        return info;
    }

    @Override
    public boolean removeBiddingPartsAndLabor(Long id) {
        return carVehicleModelPartsInfoService
                .remove(new QueryWrapper<CarVehicleModelPartsInfo>().eq("vehicle_model_id", id).eq("type_info", "历史竞价"))
                && carVehicleModelLaborHourInfoService
                        .remove(new QueryWrapper<CarVehicleModelLaborHourInfo>().eq("vehicle_model_id", id)
                                .eq("type_info", "历史竞价"));
    }
}
