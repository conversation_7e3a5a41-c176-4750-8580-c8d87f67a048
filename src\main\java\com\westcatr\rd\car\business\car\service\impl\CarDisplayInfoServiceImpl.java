package com.westcatr.rd.car.business.car.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.core.exception.IRuntimeException;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.service.BaseDao;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.boot.sso.util.AuthUtil;
import com.westcatr.rd.car.business.car.entity.CarDisplayInfo;
import com.westcatr.rd.car.business.car.entity.CarInfo;
import com.westcatr.rd.car.business.car.entity.CarModelInfo;
import com.westcatr.rd.car.business.car.mapper.CarDisplayInfoMapper;
import com.westcatr.rd.car.business.car.pojo.dto.CarModelYearDto;
import com.westcatr.rd.car.business.car.pojo.query.CarDisplayInfoQuery;
import com.westcatr.rd.car.business.car.pojo.query.CarInfoQuery;
import com.westcatr.rd.car.business.car.pojo.query.CarModelInfoQuery;
import com.westcatr.rd.car.business.car.pojo.vo.CarDisplayInfoVO;
import com.westcatr.rd.car.business.car.pojo.vo.CarInfoVO;
import com.westcatr.rd.car.business.car.pojo.vo.CarModelInfoVO;
import com.westcatr.rd.car.business.car.service.CarDisplayInfoService;
import com.westcatr.rd.car.business.car.service.CarInfoService;
import com.westcatr.rd.car.business.car.service.CarModelInfoService;
import com.westcatr.rd.car.business.demand.mapper.DemandBrowseInfoMapper;
import com.westcatr.rd.car.business.openapi.pojo.dto.BannerReturnDto;
import com.westcatr.rd.car.business.param.entity.CarParamBasicInfo1;
import com.westcatr.rd.car.business.param.service.CarParamBasicInfo1Service;
import com.westcatr.rd.car.business.supplier.entity.SupplierInfo;
import com.westcatr.rd.car.business.supplier.service.SupplierInfoService;
import com.westcatr.rd.car.business.testdrive.entity.TestDriveEvaluationInfo;
import com.westcatr.rd.car.business.testdrive.mapper.TestDriveEvaluationInfoMapper;
import com.westcatr.rd.car.business.testdrive.service.TestDriveEvaluationInfoService;
import com.westcatr.rd.car.enums.CarStatusEnum;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 车辆—信息展示表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
@Slf4j
@Service
public class CarDisplayInfoServiceImpl extends ServiceImpl<CarDisplayInfoMapper, CarDisplayInfo>
        implements CarDisplayInfoService {

    @Autowired
    private CarInfoService carInfoService;

    @Autowired
    private TestDriveEvaluationInfoService testDriveEvaluationInfoService;

    @Autowired
    private TestDriveEvaluationInfoMapper testDriveEvaluationInfoMapper;

    @Autowired
    private DemandBrowseInfoMapper demandBrowseInfoMapper;

    @Autowired
    private CarModelInfoService carModelInfoService;

    @Autowired
    private SupplierInfoService supplierInfoService;

    @Autowired
    private CarParamBasicInfo1Service carParamBasicInfo1Service;
    @Autowired
    private BaseDao baseDao;

    public CarDisplayInfoServiceImpl(CarInfoService carInfoService) {
        this.carInfoService = carInfoService;
    }

    @Override
    public IPage<CarDisplayInfo> entityPage(CarDisplayInfoQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<CarDisplayInfo>().create(query));
    }

    @Override
    public CarDisplayInfo getEntityById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean saveEntity(CarDisplayInfo param) {
        return this.save(param);
    }

    @Override
    public boolean updateEntity(CarDisplayInfo param) {
        return this.updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return this.removeById(id);
    }

    @Transactional
    @Override
    public boolean setUpCarouselDisplay(CarDisplayInfo carDisplayInfo) {
        if (carDisplayInfo.getIsCarousel() != null && carDisplayInfo.getIsCarousel() == 1) {
            long count = this
                    .count(new LambdaQueryWrapper<>(CarDisplayInfo.class).eq(CarDisplayInfo::getIsCarousel, 1));
            if (count >= 5) {
                throw new IRuntimeException("轮播展示车辆信息不能超过5个！");
            }
        }
        if (carDisplayInfo.getSortNum() != null) {
            long count = this.count(new LambdaQueryWrapper<>(CarDisplayInfo.class).eq(CarDisplayInfo::getSortNum,
                    carDisplayInfo.getSortNum()));
            if (count >= 1) {
                throw new IRuntimeException("轮播展示车辆信息序号不能重复！");
            }
        }
        CarDisplayInfo carDisplayInfoData = this.getOne(new LambdaQueryWrapper<>(CarDisplayInfo.class)
                .eq(CarDisplayInfo::getCarId, carDisplayInfo.getCarId()).last("limit 1"));
        if (ObjectUtil.isNull(carDisplayInfoData)) {
            carDisplayInfoData = new CarDisplayInfo();
            carDisplayInfoData.setCarId(carDisplayInfo.getCarId());
            carDisplayInfoData.setIsCarousel(carDisplayInfo.getIsCarousel());
            carDisplayInfoData.setSortNum(carDisplayInfo.getSortNum());
            CarInfo carInfo = carInfoService.getById(carDisplayInfo.getCarId());
            if (ObjectUtil.isNotNull(carInfo)) {
                carDisplayInfoData.setCarIdInfo(carInfo.getBrandModel());
            }
            this.save(carDisplayInfoData);
        } else {
            LambdaUpdateWrapper<CarDisplayInfo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(CarDisplayInfo::getId, carDisplayInfoData.getId());
            // updateWrapper.set(CarDisplayInfo::getSortNum, carDisplayInfo.getSortNum());
            updateWrapper.set(CarDisplayInfo::getIsCarousel, carDisplayInfo.getIsCarousel());
            this.update(updateWrapper);
        }
        return true;
    }

    @Override
    public List<String> getTopOfLineImgUrls() {
        List<String> imgUrls = new ArrayList<>();
        List<CarDisplayInfo> list = this
                .list(new LambdaQueryWrapper<CarDisplayInfo>().eq(CarDisplayInfo::getIsCarousel, 1));
        if (CollUtil.isNotEmpty(list)) {
            List<Long> carId = list.stream().map(CarDisplayInfo::getCarId).toList();
            List<CarInfo> carInfos = carInfoService.listByIds(carId);
            imgUrls.addAll(carInfos.stream().map(CarInfo::getCarPromotionImgUrl).toList());
        }
        return imgUrls;
    }

    @Override
    public List<BannerReturnDto> getTopOfLineImgUrls(String urlFirst) {
        List<CarDisplayInfoVO> list = new AssociationQuery<>(CarDisplayInfoVO.class)
                .voList(new CarDisplayInfoQuery().setIsCarousel(1));
        List<BannerReturnDto> returnDtos = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(x -> {
                BannerReturnDto bannerReturnDto = new BannerReturnDto();
                bannerReturnDto.setIntroduce(x.getCarInfo().getIntroduction());
                bannerReturnDto.setDownLoadUrl(urlFirst + x.getCarInfo().getCarImgUrl());
                returnDtos.add(bannerReturnDto);
            });
        }
        return returnDtos;
    }

    @Override
    public IPage<CarInfoVO> getCarInfoVoPage(CarInfoQuery query) {
        // 移动端固定只查询已通过
        query.setStatusInfo(CarStatusEnum.ALREADY_THROUGH.getCode());
        query.setListingStatus(1);
        List<Long> queryIds = new ArrayList<>();
        if (query.getHighPrice() != null || query.getLowPrice() != null || query.getEnergyType() != null) {
            List<Long> queryCarModelIds = new ArrayList<>();
            if (query.getEnergyType() != null) {
                List<CarParamBasicInfo1> carDisplayInfos = carParamBasicInfo1Service
                        .list(new LambdaQueryWrapper<>(CarParamBasicInfo1.class)
                                .eq(CarParamBasicInfo1::getBasicEnergyType, query.getEnergyType()));
                if (CollUtil.isNotEmpty(carDisplayInfos)) {
                    queryCarModelIds.addAll(
                            carDisplayInfos.stream().map(CarParamBasicInfo1::getCarModelId).distinct().toList());
                }
            }
            List<CarModelInfo> carModelInfos = carModelInfoService.list(new LambdaQueryWrapper<>(CarModelInfo.class)
                    .gt(query.getLowPrice() != null, CarModelInfo::getPrice, query.getLowPrice())
                    .le(query.getHighPrice() != null, CarModelInfo::getPrice, query.getHighPrice())
                    .in(CollUtil.isNotEmpty(queryCarModelIds), CarModelInfo::getId, queryCarModelIds));
            if (CollUtil.isNotEmpty(carModelInfos)) {
                queryIds.addAll(carModelInfos.stream().map(CarModelInfo::getCarId).distinct().toList());
            }
        }
        if (query.getHighPrice() != null || query.getLowPrice() != null || query.getEnergyType() != null) {
            if (CollUtil.isNotEmpty(queryIds)) {
                query.setIds(queryIds);
            } else {
                query.setId("-1");
            }
        }
        List<Long> sortCarIds = new ArrayList<>();
        List<Long> sortCarNotExhibitIds = new ArrayList<>();
        List<CarDisplayInfo> carDisplayInfos = baseDao.selectListBySql(CarDisplayInfo.class, """
                SELECT
                    *\s
                FROM
                    car_display_info
                    LEFT JOIN car_info ci ON car_display_info.car_id = ci.id\s
                WHERE
                    car_display_info.is_carousel = 1
                ORDER BY
                    CASE WHEN car_display_info.sort_num IS NULL THEN 1 ELSE 0 END ASC,\s
                    car_display_info.sort_num ASC,
                    ci.update_time DESC;""", null);
        List<CarDisplayInfo> carDisplayNotExhibitInfos = baseDao.selectListBySql(CarDisplayInfo.class, """
                SELECT
                    *\s
                FROM
                    car_display_info
                    LEFT JOIN car_info ci ON car_display_info.car_id = ci.id\s
                WHERE
                    car_display_info.is_carousel = 0\s
                    OR car_display_info.is_carousel IS NULL\s
                ORDER BY
                    CASE WHEN car_display_info.sort_num IS NULL THEN 1 ELSE 0 END ASC,\s
                    car_display_info.sort_num ASC,
                    ci.update_time DESC;""", null);
        if (CollUtil.isNotEmpty(carDisplayInfos)) {
            sortCarIds.addAll(carDisplayInfos.stream().map(CarDisplayInfo::getCarId).toList());
        }
        if (CollUtil.isNotEmpty(carDisplayNotExhibitInfos)) {
            sortCarNotExhibitIds.addAll(carDisplayNotExhibitInfos.stream().map(CarDisplayInfo::getCarId).toList());
        }
        QueryWrapper<TestDriveEvaluationInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("car_id", "COUNT(car_id) as frequency")
                .notIn(CollUtil.isNotEmpty(sortCarIds), "car_id", sortCarIds).groupBy("car_id")
                .orderByDesc("frequency");
        List<Map<String, Object>> result = testDriveEvaluationInfoService.listMaps(queryWrapper);
        sortCarIds.addAll(result.stream().map(map -> (Long) map.get("car_id")).toList());
        if (CollUtil.isNotEmpty(sortCarNotExhibitIds)) {
            sortCarIds.addAll(sortCarNotExhibitIds);
        }
        QueryWrapper<CarInfoVO> wrapper = new WrapperFactory<CarInfoVO>().create(query);
        if (CollUtil.isEmpty(sortCarIds) && CollUtil.isNotEmpty(sortCarNotExhibitIds)) {
            wrapper.orderByDesc("update_time");
        } else {
            sortCarIds = sortCarIds.stream().distinct().toList();
            StringBuilder str = new StringBuilder();
            str.append("ORDER BY ");
            if (CollUtil.isNotEmpty(sortCarIds)) {
                str.append("FIELD(car_info.id, ").append(StrUtil.join(",", sortCarIds)).append(") > 0 DESC, ");
                str.append("FIELD(car_info.id, ").append(StrUtil.join(",", sortCarIds)).append("), ");
            }
            str.append("car_info.update_time DESC");
            wrapper.last(str.toString());
        }
        IPage<CarInfoVO> voList = new AssociationQuery<>(CarInfoVO.class).voPage(query, wrapper);
        if (CollUtil.isNotEmpty(voList.getRecords())) {
            voList.getRecords().forEach(x -> {
                List<SupplierInfo> supplierInfosList = new ArrayList<>();
                if (x.getSupplierId() != null) {
                    supplierInfosList = supplierInfoService
                            .list(new LambdaQueryWrapper<>(SupplierInfo.class).in(SupplierInfo::getId,
                                    voList.getRecords().stream().map(CarInfoVO::getSupplierId).distinct().toList()));
                }
                if (CollUtil.isNotEmpty(x.getCarModelInfos())) {
                    if (x.getSupplierId() != null && CollUtil.isNotEmpty(supplierInfosList)) {
                        Optional<String> optional = supplierInfosList.parallelStream()
                                .filter(y -> y.getId().equals(x.getSupplierId())).map(SupplierInfo::getCompanyName)
                                .findFirst();
                        optional.ifPresent(x::setCompanyName);
                    }
                    x.setLowestPrice(Optional.ofNullable(x.getCarModelInfos())
                            .orElse(Collections.emptyList())
                            .stream()
                            .filter(Objects::nonNull) // 过滤掉 null 的 CarModelInfo
                            .mapToDouble(carModelInfo -> Optional.ofNullable(carModelInfo.getPrice()).orElse(0.0)) // 如果价格为
                            // null
                            // 使用默认值
                            .min()
                            .orElse(0));

                    x.setHighestPrice(Optional.ofNullable(x.getCarModelInfos())
                            .orElse(Collections.emptyList())
                            .stream()
                            .filter(Objects::nonNull)
                            .mapToDouble(carModelInfo -> Optional.ofNullable(carModelInfo.getPrice()).orElse(0.0))
                            .max()
                            .orElse(0));
                }
                if (CollUtil.isNotEmpty(x.getTestDriveEvaluationInfos())) {
                    x.setTestDriveRating(Optional.ofNullable(x.getTestDriveEvaluationInfos())
                            .orElse(Collections.emptyList()) // 如果 getTestDriveEvaluationInfos() 为 null，使用空列表
                            .stream()
                            .mapToDouble(info -> Optional.ofNullable(info.getScoreInfo()).orElse(0.0)) // 处理
                            // getScoreInfo()
                            // 返回 null
                            // 的情况，默认值为 0.0
                            .average()
                            .orElse(0)); // 如果没有评分信息，默认返回 0
                }
            });
        }
        return voList;
    }

    @Override
    public List<CarModelYearDto> getCarModelYearDtoList(Long carId) {
        List<CarModelYearDto> result = new ArrayList<>();
        List<CarModelInfoVO> carModelInfoVos = new AssociationQuery<>(CarModelInfoVO.class)
                .voList(new CarModelInfoQuery().setCarId(carId.toString()));
        if (CollUtil.isNotEmpty(carModelInfoVos)) {
            carModelInfoVos.forEach(x -> {
                if (CollUtil.isNotEmpty(x.getTestDriveEvaluationInfos())) {
                    x.setTestDriveCount(x.getTestDriveEvaluationInfos().size());
                    x.setTestDriveRating(
                            x.getTestDriveEvaluationInfos().stream()
                                    .filter(Objects::nonNull) // 过滤掉 null 值
                                    .mapToDouble(info -> info.getScoreInfo() == null ? 0 : info.getScoreInfo()) // 处理
                                    // null
                                    // 分数
                                    .average()
                                    .orElse(0));
                }
            });
            // 按年份分组并排序
            Map<Integer, List<CarModelInfoVO>> groupedByYear = carModelInfoVos.stream()
                    .filter(carModel -> carModel.getYearModel() != null) // 过滤掉 yearModel 为 null 的项
                    .collect(Collectors.groupingBy(
                            CarModelInfoVO::getYearModel,
                            TreeMap::new,
                            Collectors.toList()));

            // 将分组结果转换为 List<CarModelYearDto>，并按年份从大到小排序
            result = groupedByYear.entrySet().stream().sorted((entry1, entry2) -> entry2.getKey() - entry1.getKey())
                    .map(entry -> {
                        CarModelYearDto dto = new CarModelYearDto();
                        dto.setYearInfo(entry.getKey());
                        dto.setCarModelInfoVos(entry.getValue());
                        return dto;
                    }).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public IPage<CarInfoVO> listPurchasedVehicles(CarInfoQuery query) {
        List<Long> sortCarIds = new ArrayList<>(testDriveEvaluationInfoMapper.selectTopCars());
        if (CollUtil.isEmpty(sortCarIds)) {
            // 前三位为按照用户试驾申请车辆倒序进行排序展示；若用户未申请试驾则展示点击查看最多的三个车辆进行顺序展示；
            sortCarIds = new ArrayList<>(demandBrowseInfoMapper.findCarIdsByUserId(AuthUtil.getUserIdE()));
        }
        List<CarDisplayInfo> carDisplayInfos = this
                .list(new LambdaQueryWrapper<CarDisplayInfo>().eq(CarDisplayInfo::getIsCarousel, 1)
                        .notIn(CollUtil.isNotEmpty(sortCarIds), CarDisplayInfo::getCarId, sortCarIds)
                        .orderByAsc(CarDisplayInfo::getSortNum));
        if (CollUtil.isNotEmpty(carDisplayInfos)) {
            sortCarIds.addAll(carDisplayInfos.stream().map(CarDisplayInfo::getCarId).toList());
        }
        QueryWrapper<TestDriveEvaluationInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("car_id, COUNT(car_id) as frequency")
                .notIn(CollUtil.isNotEmpty(sortCarIds), "car_id", sortCarIds).groupBy("car_id")
                .orderByDesc("frequency");
        List<Map<String, Object>> result = testDriveEvaluationInfoService.listMaps(queryWrapper);
        sortCarIds.addAll(result.stream().map(map -> (Long) map.get("car_id")).toList());
        QueryWrapper<CarInfoVO> wrapper = new WrapperFactory<CarInfoVO>().create(query);
        if (CollUtil.isEmpty(sortCarIds)) {
            // 如果 ids 为空，仅按 update_time 排序
            queryWrapper.orderByDesc("update_time");
        } else {
            queryWrapper.last("ORDER BY FIELD(id, " + StrUtil.join(",", sortCarIds) + "), update_time DESC");
        }
        IPage<CarInfoVO> voList = new AssociationQuery<>(CarInfoVO.class).voPage(query, wrapper);
        if (CollUtil.isNotEmpty(voList.getRecords())) {
            voList.getRecords().forEach(x -> {
                if (CollUtil.isNotEmpty(x.getCarModelInfos())) {
                    x.setLowestPrice(Optional.ofNullable(x.getCarModelInfos())
                            .orElse(Collections.emptyList()) // 如果为 null 则使用空集合
                            .stream()
                            .mapToDouble(CarModelInfo::getPrice)
                            .min()
                            .orElse(0));

                    x.setHighestPrice(Optional.ofNullable(x.getCarModelInfos())
                            .orElse(Collections.emptyList())
                            .stream()
                            .mapToDouble(CarModelInfo::getPrice)
                            .max()
                            .orElse(0));
                }
                if (CollUtil.isNotEmpty(x.getTestDriveEvaluationInfos())) {
                    x.setTestDriveRating(x.getTestDriveEvaluationInfos().stream()
                            .mapToDouble(TestDriveEvaluationInfo::getScoreInfo).average().orElse(0));
                }
            });
        }
        return voList;
    }

    @Override
    public boolean setSortInfoDisplay(CarDisplayInfo carDisplayInfo) {
        if (carDisplayInfo.getSortNum() != null) {
            long count = this.count(new LambdaQueryWrapper<>(CarDisplayInfo.class).eq(CarDisplayInfo::getSortNum,
                    carDisplayInfo.getSortNum()));
            if (count >= 1) {
                throw new IRuntimeException("轮播展示车辆信息序号不能重复！");
            }
        }
        CarDisplayInfo carDisplayInfoData = this.getOne(new LambdaQueryWrapper<>(CarDisplayInfo.class)
                .eq(CarDisplayInfo::getCarId, carDisplayInfo.getCarId()).last("limit 1"));
        if (ObjectUtil.isNotNull(carDisplayInfoData)) {
            LambdaUpdateWrapper<CarDisplayInfo> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(CarDisplayInfo::getId, carDisplayInfoData.getId());
            if (StrUtil.isNotEmpty(carDisplayInfo.getSortNum())) {
                updateWrapper.set(CarDisplayInfo::getSortNum, carDisplayInfo.getSortNum());
            } else {
                updateWrapper.set(CarDisplayInfo::getSortNum, null);
            }
            this.update(updateWrapper);
        }
        return true;
    }

    @Transactional
    @Scheduled(cron = "0 */1 * * * ?")
    public void cleanInvalidDisplayInfo() {
        List<CarDisplayInfo> displayInfoList = this
                .list(new LambdaQueryWrapper<>(CarDisplayInfo.class).eq(CarDisplayInfo::getIsCarousel, 1));
        if (CollUtil.isNotEmpty(displayInfoList)) {
            for (CarDisplayInfo displayInfo : displayInfoList) {
                CarInfo carInfo = carInfoService.getById(displayInfo.getCarId());
                if (ObjectUtil.isNull(carInfo)) {
                    if (this.removeById(displayInfo.getId())) {
                        log.info("已删除无效的展示信息记录: ID={}, CarID={},carInfo={}",
                                displayInfo.getId(),
                                displayInfo.getCarId(), carInfo);
                    }
                } else {
                    if (!carInfo.getStatusInfo().equals(CarStatusEnum.ALREADY_THROUGH.getCode())
                            || carInfo.getListingStatus() == 0) {
                        displayInfo.setIsCarousel(0);
                        if (this.updateById(displayInfo)) {
                            log.info("更新展示记录: ID={}, CarID={},carInfo={}",
                                    displayInfo.getId(),
                                    displayInfo.getCarId(), carInfo);
                        }
                    }
                }
            }
        }
    }
}
