package com.westcatr.rd.car.business.basic.entity;

import java.io.Serializable;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.web.excel.annotation.ExcelField;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 投诉配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("basic_complaint_config_info")
@Schema(description = "投诉配置表")
public class BasicComplaintConfigInfo extends Model<BasicComplaintConfigInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @ExcelField(name = "主键ID")
    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "投诉电话")
    @ExcelField(name = "投诉电话")
    @NotBlank(message = "投诉电话不能为空", groups = { Insert.class, Update.class })
    @Length(max = 20, message = "投诉电话长度不能超过20", groups = { Insert.class, Update.class })
    @TableField("complaint_phone")
    private String complaintPhone;

    @Schema(description = "投诉邮箱")
    @ExcelField(name = "投诉邮箱")
    @NotBlank(message = "投诉邮箱不能为空", groups = { Insert.class, Update.class })
    @Length(max = 100, message = "投诉邮箱长度不能超过100", groups = { Insert.class, Update.class })
    @TableField("complaint_email")
    private String complaintEmail;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
