package com.westcatr.rd.car.business.audit.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.car.business.audit.entity.AuditLogInfo;
import com.westcatr.rd.car.business.audit.pojo.query.AuditLogQuery;

/**
 * @Author: Roo
 * @Date: 2025-03-31
 * @Detail: 业务审计日志表 Service 接口
 */
public interface AuditLogService extends IService<AuditLogInfo> {

    /**
     * 保存审计日志 (具体实现应为异步)
     * 
     * @param auditLogInfo 日志信息
     */
    void saveLog(AuditLogInfo auditLogInfo);

    IPage<AuditLogInfo> entityPage(AuditLogQuery query);

    AuditLogInfo getEntityById(Long id);

    boolean saveEntity(AuditLogInfo param);

    boolean updateEntity(AuditLogInfo param);

    boolean removeEntityById(Long id);

}