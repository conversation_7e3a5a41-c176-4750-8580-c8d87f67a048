package com.westcatr.rd.car.business.car.pojo.vo;

import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.car.business.car.entity.CarDisplayInfo;
import com.westcatr.rd.car.business.car.entity.CarInfo;
import com.westcatr.rd.car.business.car.entity.CarModelInfo;
import com.westcatr.rd.car.business.car.pojo.dto.CarModelYearDto;
import com.westcatr.rd.car.business.supplier.entity.SupplierInfo;
import com.westcatr.rd.car.business.testdrive.entity.TestDriveEvaluationInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 车辆—基础信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "车辆—基础信息表VO对象")
public class CarInfoVO extends CarInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @JoinSelect(joinClass = CarModelInfo.class, relationId = "car_id")
    private List<CarModelInfo> carModelInfos;

    @Schema(description = "最低价格")
    @TableField(exist = false)
    private Double lowestPrice;

    @Schema(description = "最高价格")
    @TableField(exist = false)
    private Double highestPrice;

    @Schema(description = "试驾评分")
    @TableField(exist = false)
    private Double testDriveRating;

    @Schema(description = "试驾评分表记录")
    @TableField(exist = false)
    @JoinSelect(joinClass = TestDriveEvaluationInfo.class, relationId = "car_id")
    private List<TestDriveEvaluationInfo> testDriveEvaluationInfos;

    @Schema(description = "车型试驾年份记录信息")
    @TableField(exist = false)
    private List<CarModelYearDto> carModelYearDtoList;

    @Schema(description = "展示信息")
    @TableField(exist = false)
    @JoinSelect(joinClass = CarDisplayInfo.class, relationId = "car_id")
    private CarDisplayInfo carDisplayInfo;

    @Schema(description = "供应商信息")
    @TableField(exist = false)
    private String companyName;

    @TableField(exist = false)
    @Schema(description = "供应商类型")
    private String supplierType;

    @TableField(exist = false)
    @JoinSelect(joinClass = SupplierInfo.class, mainId = "supplierId")
    private SupplierInfo supplierInfo;
}
