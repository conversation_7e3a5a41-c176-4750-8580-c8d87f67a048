package com.westcatr.rd.car.business.fanfan.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * 泛泛平台API配置属性
 *
 * <AUTHOR>
 * @date 2025-05-29
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "westcatr.fanfan")
public class FanFanApiProperties {

    /**
     * 商户号
     */
    private String partnerId; // 用于订单提交等

    /**
     * RSA公钥
     */
    private String rsaPublicKey; // 泛泛平台提供，用于加密AES密钥 - 订单提交等

    /**
     * RSA私钥
     */
    private String rsaPrivateKey; // 我们自己生成，用于签名 - 订单提交等

    /**
     * 接口基础URL
     */
    private String baseUrl;

    /**
     * 接口版本
     */
    private String version = "1.0";

    /**
     * 是否开启调试模式
     */
    private Boolean debug = false;

    // 新增的配置项，用于新的泛泛接口 (如查询订单状态、查询修理厂材料等)
    /**
     * 新接口 - 商户号
     */
    private String newPartnerId;

    /**
     * 新接口 - RSA公钥 (泛泛平台为这些新接口提供的公钥)
     */
    private String newRsaPublicKey;

    /**
     * 新接口 - RSA私钥 (我们为这些新接口生成的私钥，用于签名)
     */
    private String newRsaPrivateKey;

    /**
     * 新接口 - 接口基础URL (如果与旧的不同)
     */
    private String newBaseUrl;

    /**
     * 新接口 - 接口版本 (如果与旧的不同)
     */
    private String newVersion;

    /**
     * 测试接口URL前缀
     */
    private String testApiUrl;
}
