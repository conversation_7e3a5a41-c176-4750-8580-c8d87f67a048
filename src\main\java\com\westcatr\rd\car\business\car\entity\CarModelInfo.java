package com.westcatr.rd.car.business.car.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 车辆—车型信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("car_model_info")
@Schema(description = "车辆—车型信息表")
public class CarModelInfo extends Model<CarModelInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("car_id")
    private Long carId;

    @Schema(description = "年款")
    @TableField("year_model")
    private Integer yearModel;

    @Schema(description = "配置名称")
    @Length(max = 255, message = "配置名称长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("config_name")
    private String configName;

    @Schema(description = "是否支持试驾（0: 否, 1: 是）")
    @TableField("is_test_drive")
    private Boolean isTestDrive;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Schema(description = "供应商id")
    @TableField("supplier_id")
    private Long supplierId;

    @Schema(description = "车型价格")
    @TableField("price")
    private Double price;

    @Schema(description = "草稿表单")
    @TableField("draft_json")
    private String draftJson;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
