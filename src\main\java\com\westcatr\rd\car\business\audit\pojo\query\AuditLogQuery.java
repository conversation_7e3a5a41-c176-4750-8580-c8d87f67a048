package com.westcatr.rd.car.business.audit.pojo.query;

import java.io.Serializable;
import java.util.Date;

import com.westcatr.rd.boot.orm.dto.TimeDTO;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 业务审计日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "业务审计日志表查询对象")
public class AuditLogQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @QueryCondition
    private Long id;

    @Schema(description = "用户唯一ID")
    @QueryCondition
    private String userId;

    @Schema(description = "用户名")
    @QueryCondition
    private String username;

    @Schema(description = "操作时间")
    @QueryCondition
    private Date operationTime;

    @Schema(description = "操作IP地址")
    @QueryCondition
    private String ipAddress;

    @Schema(description = "操作类型")
    @QueryCondition
    private String operationType;

    @Schema(description = "事件描述")
    @QueryCondition
    private String eventDescription;

    @Schema(description = "业务唯一标识")
    @QueryCondition
    private String businessId;

    @Schema(description = "事件结果 (成功/失败)")
    @QueryCondition
    private String result;

    @Schema(description = "失败时的错误信息")
    @QueryCondition
    private String errorMessage;

    @Schema(description = "请求参数 (JSON格式)")
    @QueryCondition
    private String requestParams;

    @Schema(description = "操作的类名")
    @QueryCondition
    private String className;

    @Schema(description = "操作的方法名")
    @QueryCondition
    private String methodName;

    @Schema(description = "同步状态：0-未同步，1-已同步")
    @QueryCondition
    private Integer syncStatus;

    @Schema(description = "同步时间")
    @QueryCondition
    private Date syncTime;
}
