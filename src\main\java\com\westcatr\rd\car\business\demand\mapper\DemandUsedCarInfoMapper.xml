<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.car.business.demand.mapper.DemandUsedCarInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.car.business.demand.entity.DemandUsedCarInfo">
        <id column="id" property="id" />
        <result column="contact_name" property="contactName" />
        <result column="contact_phone" property="contactPhone" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="demand_title" property="demandTitle" />
        <result column="description_info" property="descriptionInfo" />
        <result column="old_car_model" property="oldCarModel" />
        <result column="status" property="status" />
        <result column="supplier_id" property="supplierId" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        contact_name, contact_phone, create_time, create_user_id, demand_title, description_info, id, old_car_model, status, supplier_id, update_time
    </sql>

</mapper>
