#!/bin/sh

echo "开始连接数据库..."
# 等待MySQL服务就绪
until mysql -h"$MYSQL_HOST" -P"${MYSQL_PORT:-3306}" -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" &> /dev/null; do
  echo "等待MySQL数据库连接..."
  sleep 3
done

echo "MySQL数据库已就绪"

# 执行初始化SQL文件
echo "开始执行数据库初始化..."
echo "正在执行SQL文件，这可能需要一些时间..."
mysql -v -h"$MYSQL_HOST" -P"${MYSQL_PORT:-3306}" -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" "$MYSQL_DATABASE" < /docker-entrypoint-initdb.d/init.sql
echo "数据库初始化完成"

# 验证表是否创建成功
echo "验证数据库初始化结果..."
TABLES_COUNT=$(mysql -N -s -h"$MYSQL_HOST" -P"${MYSQL_PORT:-3306}" -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" "$MYSQL_DATABASE" -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$MYSQL_DATABASE';")
echo "数据库中有 $TABLES_COUNT 张表"

# 启动Java应用
echo "启动应用..."
java -jar app.jar
