<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.car.business.car.mapper.CarInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.car.business.car.entity.CarInfo">
        <id column="id" property="id" />
        <result column="brand_model" property="brandModel" />
        <result column="description" property="description" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="video_title" property="videoTitle" />
        <result column="car_img_url" property="carImgUrl" />
        <result column="car_promotion_img_url" property="carPromotionImgUrl" />
        <result column="car_video_url" property="carVideoUrl" />
        <result column="status_info" property="statusInfo" />
        <result column="test_drive_score_info" property="testDriveScoreInfo" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, brand_model, description, create_time, update_time, video_title, car_img_url, car_promotion_img_url, car_video_url, status_info, test_drive_score_info
    </sql>

</mapper>
