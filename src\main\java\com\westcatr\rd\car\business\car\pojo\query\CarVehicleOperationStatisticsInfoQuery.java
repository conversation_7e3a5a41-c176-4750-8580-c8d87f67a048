package com.westcatr.rd.car.business.car.pojo.query;

import java.io.Serializable;
import java.util.Date;

import com.westcatr.rd.boot.orm.dto.TimeDTO;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 车型配件表—操作统计
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "车型配件表—操作统计查询对象")
public class CarVehicleOperationStatisticsInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @QueryCondition
    private Long id;

    @Schema(description = "操作类型")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String operationType;

    @Schema(description = "操作项")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String operationItem;

    @Schema(description = "涉及车型")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String involvedVehicleModel;

    @Schema(description = "操作人")
    @QueryCondition(condition = QueryCondition.Condition.LIKE)
    private String operatorFullName;

    @QueryCondition
    private Date createTime;

    @QueryCondition
    private Date updateTime;
}
