package com.westcatr.rd.car.business.audit.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.core.domain.IUser;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.sso.util.AuthUtil;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.boot.web.excel.pojo.ExcelExportParam;
import com.westcatr.rd.boot.web.excel.service.IExcelExportService;
import com.westcatr.rd.car.business.audit.entity.AuditLogInfo;
import com.westcatr.rd.car.business.audit.pojo.query.AuditLogQuery;
import com.westcatr.rd.car.business.audit.pojo.vo.AuditLogVO;
import com.westcatr.rd.car.business.audit.service.AuditLogService;
import com.westcatr.rd.car.business.audit.service.AuditLogSyncService;
import com.westcatr.rd.car.common.annotation.AuditLog;
import com.westcatr.rd.car.enums.OperationTypeEnum;
import com.westcatr.rd.car.enums.RoleBusEnum;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: Roo
 * @Date: 2025-03-31
 * @Detail: 审计日志控制器
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/audit")
@Tag(name = "审计日志管理", description = "审计日志相关接口")
public class AuditLogController {

    // 使用setter注入替代字段注入，保持一致的注入风格
    @Autowired
    private AuditLogService auditLogService;

    @Autowired
    private AuditLogSyncService auditLogSyncService;

    @Autowired
    private IExcelExportService iExcelExportService;

    /**
     * 手动触发同步所有未同步的日志
     */
    @Operation(summary = "手动触发同步未同步的审计日志")
    @PostMapping("/syncAllLogs")
    @AuditLog(operationType = OperationTypeEnum.OTHER, description = "手动触发同步审计日志到外部系统")
    public IResult<String> syncAllLogs() {
        IUser user = AuthUtil.getUserE();
        // 只允许超级管理员或系统管理员执行
        if (!user.getRoleIds().contains(RoleBusEnum.SUPER_ADMIN.getRoleId()) &&
                !user.getRoleIds().contains(RoleBusEnum.CAR_ADMIN.getRoleId())) {
            return IResult.fail("您没有权限执行此操作");
        }

        try {
            // 只获取未同步的日志记录
            List<AuditLogInfo> logs = auditLogService.list(new LambdaQueryWrapper<AuditLogInfo>()
                    .eq(AuditLogInfo::getSyncStatus, 0)
                    .orderByDesc(AuditLogInfo::getId)
                    .last("LIMIT 1000")); // 限制数量防止一次同步过多

            log.info("开始手动同步未同步的审计日志，数量: {}", logs.size());

            if (logs.isEmpty()) {
                return IResult.ok("当前没有未同步的审计日志");
            }

            // 逐个同步
            for (AuditLogInfo log : logs) {
                auditLogSyncService.syncLog(log);
            }

            return IResult.ok("成功触发同步，共同步" + logs.size() + "条未同步的审计日志");
        } catch (Exception e) {
            log.error("手动同步审计日志失败", e);
            return IResult.fail("同步失败: " + e.getMessage());
        }
    }

    /**
     * 重新同步指定的日志，无论之前是否已同步
     */
    @Operation(summary = "重新同步指定的审计日志")
    @PostMapping("/resyncLog")
    @AuditLog(operationType = OperationTypeEnum.OTHER, description = "重新同步指定审计日志到外部系统: ID={#id.id}")
    public IResult<String> resyncLog(@RequestBody ID id) {
        IUser user = AuthUtil.getUserE();
        // 只允许超级管理员或系统管理员执行
        if (!user.getRoleIds().contains(RoleBusEnum.SUPER_ADMIN.getRoleId()) &&
                !user.getRoleIds().contains(RoleBusEnum.CAR_ADMIN.getRoleId())) {
            return IResult.fail("您没有权限执行此操作");
        }

        try {
            AuditLogInfo log = auditLogService.getById(id.longId());
            if (log == null) {
                return IResult.fail("未找到ID为" + id.longId() + "的审计日志");
            }

            // 重置同步状态，确保可以重新同步
            log.setSyncStatus(0);
            auditLogService.updateById(log);

            // 执行同步
            auditLogSyncService.syncLog(log);

            return IResult.ok("成功重新同步ID为" + id.longId() + "的审计日志");
        } catch (Exception e) {
            log.error("重新同步审计日志失败, ID: {}", id.longId(), e);
            return IResult.fail("同步失败: " + e.getMessage());
        }
    }

    // 以下是从MyBatis Plus自动生成的代码移植过来的方法

    @Operation(summary = "获取审计日志数据")
    @PostMapping("/get")
    public IResult<AuditLogInfo> getAuditLogById(@RequestBody @Id ID id) {
        return IResult.ok(auditLogService.getById(id.longId()));
    }

    @Operation(summary = "新增审计日志数据")
    @PostMapping("/add")
    public IResult<Boolean> addAuditLog(@RequestBody @Validated(Insert.class) AuditLogInfo param) {
        return IResult.auto(auditLogService.save(param));
    }

    @Operation(summary = "更新审计日志数据")
    @PostMapping("/update")
    public IResult<Boolean> updateAuditLogById(@RequestBody @Validated(Update.class) AuditLogInfo param) {
        return IResult.auto(auditLogService.updateById(param));
    }

    @Operation(summary = "删除审计日志数据")
    @PostMapping("/delete")
    public IResult<Boolean> deleteAuditLogById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            auditLogService.removeById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取审计日志VO分页数据")
    @PostMapping("/voPage")
    public IResult<IPage<AuditLogVO>> getAuditLogVoPage(@RequestBody AuditLogQuery query) {
        AssociationQuery<AuditLogVO> associationQuery = new AssociationQuery<>(AuditLogVO.class);
        return IResult.ok(associationQuery.voPage(query));
    }

    @Operation(summary = "获取审计日志VO数据")
    @PostMapping("/getVo")
    public IResult<AuditLogVO> getAuditLogVoById(@RequestBody @Id ID id) {
        AssociationQuery<AuditLogVO> associationQuery = new AssociationQuery<>(AuditLogVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    @Operation(summary = "导出审计日志数据")
    @PostMapping("/export")
    public void export(@RequestBody ExcelExportParam<AuditLogQuery> query) {
        iExcelExportService.exportExcel("审计日志数据", AuditLogVO.class, query, false);
    }
}