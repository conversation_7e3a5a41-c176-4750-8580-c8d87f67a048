<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.car.business.basic.mapper.BasicEvaluationLabelInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.car.business.basic.entity.BasicEvaluationLabelInfo">
        <id column="id" property="id" />
        <result column="module_info" property="moduleInfo" />
        <result column="label_type" property="labelType" />
        <result column="label_content" property="labelContent" />
        <result column="display_status" property="displayStatus" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, module_info, label_type, label_content, display_status, create_time, update_time
    </sql>

</mapper>
