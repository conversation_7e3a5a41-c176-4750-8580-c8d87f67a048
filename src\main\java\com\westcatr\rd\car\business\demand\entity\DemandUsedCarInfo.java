package com.westcatr.rd.car.business.demand.entity;

import java.io.Serializable;
import java.util.Date;

import org.hibernate.validator.constraints.Length;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.westcatr.rd.boot.core.annotation.Insert;
import com.westcatr.rd.boot.core.annotation.Update;
import com.westcatr.rd.boot.web.excel.annotation.ExcelField;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 二手车需求信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("demand_used_car_info")
@Schema(description = "二手车需求信息表")
public class DemandUsedCarInfo extends Model<DemandUsedCarInfo> {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Schema(description = "联系人")
    @ExcelField(name = "联系人")
    @Length(max = 255, message = "联系人长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("contact_name")
    private String contactName;

    @Schema(description = "联系电话")
    @ExcelField(name = "联系电话")
    @Length(max = 255, message = "联系电话长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("contact_phone")
    private String contactPhone;

    @Schema(description = "发布时间")
    @ExcelField(name = "发布时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @Schema(description = "创建用户ID")
    @ExcelField(name = "创建用户ID")
    @TableField("create_user_id")
    private Long createUserId;

    @Schema(description = "需求标题")
    @ExcelField(name = "需求标题")
    @Length(max = 255, message = "需求标题长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("demand_title")
    private String demandTitle;

    @Schema(description = "需求描述")
    @ExcelField(name = "需求描述")
    @Length(max = 65535, message = "需求描述长度不能超过65535", groups = { Insert.class, Update.class })
    @TableField("description_info")
    private String descriptionInfo;

    @NotNull(message = "id不能为空", groups = { Update.class })
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "旧车车型")
    @ExcelField(name = "旧车车型")
    @Length(max = 255, message = "旧车车型长度不能超过255", groups = { Insert.class, Update.class })
    @TableField("old_car_model")
    private String oldCarModel;

    @Schema(description = "状态")
    @ExcelField(name = "状态")
    @TableField("status_info")
    private Integer statusInfo;

    @Schema(description = "供应商ID")
    @ExcelField(name = "供应商ID")
    @TableField("supplier_id")
    private Long supplierId;

    @Schema(description = "更新时间")
    @ExcelField(name = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
