package com.westcatr.rd.car.business.car.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CarVehicleOperationStatisticsDto {

    // 车型查询数
    @Schema(description = "车型查询数")
    private Integer vehicleModelQueryCount;
    // 配件库查看数
    @Schema(description = "配件库查看数")
    private Integer partsLibraryQueryCount;
    // 配件查询数
    @Schema(description = "配件查询数")
    private Integer partsQueryCount;
    // 配件导出数
    @Schema(description = "配件导出数")
    private Integer partsExportCount;
}
