package com.westcatr.rd.car.business.car.service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.westcatr.rd.car.business.car.entity.CarVehicleModelPartsInfo;
import com.westcatr.rd.car.business.car.pojo.query.CarVehicleModelPartsInfoQuery;
import com.westcatr.rd.car.business.car.pojo.vo.CarVehicleModelPartsInfoVO;

/**
 * <p>
 * 车型配件信息表--配件 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
public interface CarVehicleModelPartsInfoService extends IService<CarVehicleModelPartsInfo> {

    IPage<CarVehicleModelPartsInfo> entityPage(CarVehicleModelPartsInfoQuery query);

    CarVehicleModelPartsInfo getEntityById(Long id);

    boolean saveEntity(CarVehicleModelPartsInfo param);

    boolean updateEntity(CarVehicleModelPartsInfo param);

    boolean removeEntityById(Long id);

    IPage<CarVehicleModelPartsInfoVO> myVoPage(CarVehicleModelPartsInfoQuery query);

    /**
     * 导出模板
     */
    void exportTemplate();

    /**
     * 导入Excel数据
     *
     * @param file           Excel文件
     * @param vehicleModelId 车型ID
     * @return 是否导入成功
     * @throws IOException IO异常
     */
    boolean importExcel(MultipartFile file) throws IOException;

    /**
     * 导出Excel数据
     *
     * @param query 查询条件
     */
    void exportExcel(CarVehicleModelPartsInfoQuery query);

    /**
     * 批量获取车型的配件数量
     * 
     * @param modelIds 车型ID列表
     * @return Map<车型ID, 配件数量>
     */
    Map<Long, Long> getPartsCountByModelIds(List<Long> modelIds);

    /**
     * 批量获取车型的竞价配件数量
     * 
     * @param modelIds 车型ID列表
     * @return Map<车型ID, 竞价配件数量>
     */
    Map<Long, Long> getBiddingPartsCountByModelIds(List<Long> modelIds);
}
