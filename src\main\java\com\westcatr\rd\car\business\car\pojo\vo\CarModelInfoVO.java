package com.westcatr.rd.car.business.car.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.westcatr.rd.boot.orm.association.annotation.JoinSelect;
import com.westcatr.rd.car.business.car.entity.CarInfo;
import com.westcatr.rd.car.business.car.entity.CarModelInfo;
import com.westcatr.rd.car.business.param.entity.CarParamBasicInfo1;
import com.westcatr.rd.car.business.testdrive.entity.TestDriveEvaluationInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 车辆—车型信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "车辆—车型信息表VO对象")
public class CarModelInfoVO extends CarModelInfo {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    @JoinSelect(joinClass = TestDriveEvaluationInfo.class, relationId = "car_model_id")
    private List<TestDriveEvaluationInfo> testDriveEvaluationInfos;

    @Schema(description = "试驾评分")
    @TableField(exist = false)
    private Double testDriveRating;

    @Schema(description = "试驾次数")
    @TableField(exist = false)
    private Integer testDriveCount;

    @Schema(description = "品牌与车型")
    @TableField(exist = false)
    @JoinSelect(joinClass = CarInfo.class, mainId = "carId", field = "brand_model")
    private String brandModel;

    @TableField(exist = false)
    //@JoinSelect(joinClass = CarInfo.class, mainId = "carId")
    private CarInfoVO carInfoByModel;

    @TableField(exist = false)
    @Schema(description = "车型试驾优点集合")
    private List<String> testDriveAdvantages;

    @TableField(exist = false)
    @Schema(description = "车型试驾缺点集合")
    private List<String> testDriveDisadvantages;

    @TableField(exist = false)
    @Schema(description = "厂家指导价（万元）")
    @JoinSelect(joinClass = CarParamBasicInfo1.class, relationId = "car_model_id", field = "basic_manufacturer_guide_price")
    private String basicManufacturerGuidePrice;
}
