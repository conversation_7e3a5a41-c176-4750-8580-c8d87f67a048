package com.westcatr.rd.car.business.car.pojo.query;

import java.io.Serializable;
import java.util.Date;

import com.westcatr.rd.boot.orm.dto.TimeDTO;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition;
import com.westcatr.rd.boot.orm.wrapper.QueryCondition.Condition;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 车型配件信息表--配件
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "车型配件信息表--配件查询对象")
public class CarVehicleModelPartsInfoQuery extends TimeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @QueryCondition
    private Long id;

    @Schema(description = "车型id")
    @QueryCondition
    private Long vehicleModelId;

    @Schema(description = "车型编码")
    @QueryCondition(condition = Condition.LIKE)
    private String modelCode;

    @Schema(description = "子组编码")
    @QueryCondition(condition = Condition.LIKE)
    private String subGroupCode;

    @Schema(description = "子组名称")
    @QueryCondition(condition = Condition.LIKE)
    private String subGroupName;

    @Schema(description = "配件编码")
    @QueryCondition(condition = Condition.LIKE)
    private String partCode;

    @Schema(description = "配件名称")
    @QueryCondition(condition = Condition.LIKE)
    private String partName;

    @Schema(description = "备注")
    @QueryCondition
    private String remarks;

    @Schema(description = "原厂价格")
    @QueryCondition
    private Double originalPrice;

    @Schema(description = "4S店价格")
    @QueryCondition
    private Double dealerPrice;

    @Schema(description = "创建时间")
    @QueryCondition
    private Date createTime;

    @Schema(description = "更新时间")
    @QueryCondition
    private Date updateTime;

    // typeInfo
    @Schema(description = "类型")
    @QueryCondition
    private String typeInfo;

    // supplierId
    @Schema(description = "供应商ID")
    @QueryCondition
    private Long supplierId;
}
