<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.car.business.demand.mapper.DemandBrowseInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.car.business.demand.entity.DemandBrowseInfo">
        <id column="id" property="id" />
        <result column="car_id" property="carId" />
        <result column="car_model_id" property="carModelId" />
        <result column="user_id" property="userId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        car_id, car_model_id, id, user_id
    </sql>

</mapper>
