package com.westcatr.rd.car.business.audit.service.impl;

import java.util.Date;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.westcatr.rd.car.business.audit.entity.AuditLogInfo;
import com.westcatr.rd.car.business.audit.pojo.vo.JiLuoSysLogRequestVO;
import com.westcatr.rd.car.business.audit.service.AuditLogService;
import com.westcatr.rd.car.business.audit.service.AuditLogSyncService;
import com.westcatr.rd.car.business.openapi.OpenApiClient;
import com.westcatr.rd.car.business.openapi.pojo.dto.ClientProperties;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: Roo
 * @Date: 2025-03-31
 * @Detail: 审计日志同步服务实现类
 */
@Slf4j
@Service
public class AuditLogSyncServiceImpl implements AuditLogSyncService {

    @Value("${etravel.api.url}")
    private String apiUrl;

    @Value("${etravel.partner.id}")
    private String partnerId;

    @Value("${etravel.public.key}")
    private String publicKey;

    @Value("${etravel.private.key}")
    private String privateKey;

    @Value("${etravel.partner.context.path}")
    private String partnerContextPath;

    @Value("${sync.user.etravel.enable:false}")
    private boolean syncEtravelEnable;

    // 使用setter注入替代字段注入，解决循环依赖问题
    @Lazy
    @Autowired
    private AuditLogService auditLogService;

    // 存储同步失败的日志ID及失败次数，定期重试
    private static final ConcurrentHashMap<Long, Integer> failedLogMap = new ConcurrentHashMap<>();

    // 最大重试次数
    private static final int MAX_RETRY_COUNT = 3;

    // 定时任务执行器，用于异步重试失败的日志同步
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    // 初始化定时重试任务
    {
        // 每10分钟执行一次，重试失败的日志同步
        scheduler.scheduleAtFixedRate(this::retryFailedLogs, 10, 10, TimeUnit.MINUTES);
    }

    /**
     * 异步同步审计日志到外部系统
     */
    @Async
    @Override
    public void syncLog(AuditLogInfo auditLogInfo) {
        try {
            // 检查是否启用了同步
            if (!syncEtravelEnable) {
                log.debug("审计日志同步已禁用，跳过同步操作");
                return;
            }

            // 如果已经同步过，则直接返回
            if (auditLogInfo.getSyncStatus() != null && auditLogInfo.getSyncStatus() == 1) {
                return;
            }

            // 1. 构建请求参数
            JiLuoSysLogRequestVO requestVO = buildRequestVO(auditLogInfo);

            // 2. 配置API客户端
            ClientProperties clientProperties = new ClientProperties();
            clientProperties.setBaseUrl(apiUrl);
            clientProperties.setContextPath(partnerContextPath);
            clientProperties.setPartnerId(partnerId);
            clientProperties.setPublicKey(publicKey);
            clientProperties.setPrivateKey(privateKey);

            // 打印json格式的参数
            // System.out.println(JSONObject.toJSONString(requestVO.toMap()));
            // 3. 发送同步请求
            try {
                // 根据错误信息，需要使用 /openapi/jiluo/sys-log/saveLog 路径
                // OpenApiClient 在调用 doPost 时会自动添加 baseUrl，但不会自动添加 contextPath，
                // 所以这里需要手动指定完整路径，由于我们需要的格式是 /e-travel/openapi/jiluo/sys-log/saveLog
                // 但是 contextPath 已经是 /e-travel，所以直接使用以下格式
                OpenApiClient.getInstance(clientProperties)
                        .setBody(requestVO.toMap())
                        .doPost("/sys-log/saveLog", String.class);

                // 同步成功，更新同步状态
                auditLogInfo.setSyncStatus(1);
                auditLogInfo.setSyncTime(new Date());
                auditLogService.updateById(auditLogInfo);

                // 如果之前同步失败过，从失败映射中移除
                failedLogMap.remove(auditLogInfo.getId());

                // log.info("同步审计日志成功，日志ID: {}, 响应结果: {}", auditLogInfo.getId(), response);
            } catch (Exception ex) {
                // 记录同步失败的日志ID，并增加失败次数
                Integer failCount = failedLogMap.getOrDefault(auditLogInfo.getId(), 0) + 1;

                if (failCount <= MAX_RETRY_COUNT) {
                    failedLogMap.put(auditLogInfo.getId(), failCount);
                    /*
                     * log.warn("同步审计日志失败，日志ID: {}，错误信息: {}，当前失败次数: {}/{}，将在下次定时任务中重试",
                     * auditLogInfo.getId(), ex.getMessage(), failCount, MAX_RETRY_COUNT);
                     */
                } else {
                    // 超过最大重试次数，不再重试
                    log.error("同步审计日志失败，日志ID: {}，已超过最大重试次数，请手动同步", auditLogInfo.getId());
                    failedLogMap.remove(auditLogInfo.getId());
                }
            }
        } catch (Exception e) {
            log.error("处理审计日志同步时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 定时重试失败的日志同步
     */
    private void retryFailedLogs() {
        // 检查是否启用了同步
        if (!syncEtravelEnable) {
            return;
        }

        if (failedLogMap.isEmpty()) {
            return;
        }

        log.info("开始重试同步失败的审计日志，数量: {}", failedLogMap.size());

        failedLogMap.forEach((logId, failCount) -> {
            try {
                AuditLogInfo log = auditLogService.getById(logId);
                if (log != null && (log.getSyncStatus() == null || log.getSyncStatus() != 1)) {
                    // 重新同步
                    syncLog(log);
                } else {
                    // 如果日志已经同步成功或不存在，从失败映射中移除
                    failedLogMap.remove(logId);
                }
            } catch (Exception e) {
                log.error("重试同步审计日志失败，日志ID: {}", logId, e);
            }
        });
    }

    /**
     * 构建请求VO对象
     */
    private JiLuoSysLogRequestVO buildRequestVO(AuditLogInfo auditLogInfo) {
        JiLuoSysLogRequestVO requestVO = new JiLuoSysLogRequestVO();

        // 设置用户信息
        requestVO.setUserId(auditLogInfo.getUserId() != null ? auditLogInfo.getUserId().toString() : "-1");
        requestVO.setUserName(StrUtil.isNotBlank(auditLogInfo.getUsername()) ? auditLogInfo.getUsername() : "未知用户");

        // 设置IP信息
        requestVO.setIp(StrUtil.isNotBlank(auditLogInfo.getIpAddress()) ? auditLogInfo.getIpAddress() : "unknown");

        // 设置操作类型和日志类型
        requestVO.setOperateType(auditLogInfo.getOperationType());
        requestVO.setLogType("BIZ"); // 默认为系统日志类型

        // 设置日志描述和结果
        requestVO.setLogDesc(auditLogInfo.getEventDescription());
        requestVO.setResult("成功".equals(auditLogInfo.getResult()) ? 1 : 0);

        // 设置请求参数和方法信息
        requestVO.setParams(auditLogInfo.getRequestParams());
        requestVO.setClazz(auditLogInfo.getClassName());
        requestVO.setMethod(auditLogInfo.getMethodName());

        return requestVO;
    }
}