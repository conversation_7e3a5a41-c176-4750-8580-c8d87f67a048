package com.westcatr.rd.car.business.demand.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.westcatr.rd.car.business.demand.entity.DemandBrowseInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 需求—当前登录用户查看车辆次数信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Mapper
public interface DemandBrowseInfoMapper extends BaseMapper<DemandBrowseInfo> {

    @Select("SELECT car_id " +
            "FROM demand_browse_info " +
            "WHERE user_id = #{userId} " +
            "GROUP BY car_id " +
            "ORDER BY COUNT(*) DESC " +
            "LIMIT 3")
    List<Long> findCarIdsByUserId(@Param("userId") Long userId);
}
