package com.westcatr.rd.car.business.audit.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.boot.orm.wrapper.WrapperFactory;
import com.westcatr.rd.car.business.audit.entity.AuditLogInfo;
import com.westcatr.rd.car.business.audit.mapper.AuditLogMapper;
import com.westcatr.rd.car.business.audit.pojo.query.AuditLogQuery;
import com.westcatr.rd.car.business.audit.service.AuditLogService;
import com.westcatr.rd.car.business.audit.service.AuditLogSyncService;

import lombok.extern.slf4j.Slf4j;

/**
 * @Author: Roo
 * @Date: 2025-03-31
 * @Detail: 业务审计日志表 Service 实现类
 */
@Slf4j
@Service
public class AuditLogServiceImpl extends ServiceImpl<AuditLogMapper, AuditLogInfo> implements AuditLogService {

    // 使用setter注入替代字段注入，解决循环依赖问题
    private AuditLogSyncService auditLogSyncService;

    @Autowired
    public void setAuditLogSyncService(AuditLogSyncService auditLogSyncService) {
        this.auditLogSyncService = auditLogSyncService;
    }

    /**
     * 异步保存审计日志
     * 
     * @param auditLogInfo 日志信息
     */
    @Async // 使用 @Async 注解实现异步执行
    @Override
    public void saveLog(AuditLogInfo auditLogInfo) {
        try {
            // 设置初始同步状态为未同步
            auditLogInfo.setSyncStatus(0);

            // 调用 BaseMapper 的 insert 方法保存日志
            this.save(auditLogInfo);
            // this.baseMapper.insert(auditLogInfo);

            // 同步日志到外部系统
            auditLogSyncService.syncLog(auditLogInfo);
        } catch (Exception e) {
            // 异步任务中的异常需要特别处理，否则可能丢失
            log.error("异步保存审计日志失败: {}", auditLogInfo, e);
            // 这里可以考虑将失败的日志记录到文件或其他地方，以便后续排查或补偿
        }
    }

    @Override
    public IPage<AuditLogInfo> entityPage(AuditLogQuery query) {
        return this.page(PageDTO.page(query), new WrapperFactory<AuditLogInfo>().create(query));
    }

    @Override
    public AuditLogInfo getEntityById(Long id) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getEntityById'");
    }

    @Override
    public boolean saveEntity(AuditLogInfo param) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'saveEntity'");
    }

    @Override
    public boolean updateEntity(AuditLogInfo param) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'updateEntity'");
    }

    @Override
    public boolean removeEntityById(Long id) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'removeEntityById'");
    }
}