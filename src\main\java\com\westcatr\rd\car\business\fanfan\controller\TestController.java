package com.westcatr.rd.car.business.fanfan.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.car.business.fanfan.service.FanFanService;

import io.swagger.v3.oas.annotations.Operation;

@RestController
public class TestController {

    @Autowired
    private FanFanService fanFanService;

    @Operation(summary = "测试提交维修订单到泛泛平台（认质认价）")
    @GetMapping("/fanfan/test")
    public IResult fanfan() {
        // 使用用户提供的订单ID (来自SQL示例)
        Long orderId = 1930241974942699522L;
        // 提交类型设置为 "认质认价"
        String submissionType = "认质认价";
        return IResult.ok(fanFanService.submitOrderToFanFan(orderId, submissionType));
    }

    // 测试维修厂材料查询
    @Operation(summary = "测试维修厂材料查询")
    @GetMapping("/fanfan/queryGarageMaterial")
    public IResult queryGarageMaterial() {
        // 使用用户提供的订单ID (来自SQL示例)
        String orderId = "GAR20201018";
        return IResult.ok(fanFanService.queryGarageMaterials(orderId));
    }
}
