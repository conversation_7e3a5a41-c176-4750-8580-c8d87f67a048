package com.westcatr.rd.car.business.basic.pojo.query;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.westcatr.rd.boot.orm.dto.PageDTO;
import com.westcatr.rd.car.business.basic.entity.BasicSloganInfo;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class BasicSloganInfoQuery extends PageDTO {

    public QueryWrapper<BasicSloganInfo> wrapper() {
        QueryWrapper<BasicSloganInfo> wrapper = new QueryWrapper<>();
        return wrapper;
    }
}
