package com.westcatr.rd.car.business.basic.service.impl;

import java.math.BigDecimal;
import java.util.Arrays;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.westcatr.rd.boot.core.domain.IUser;
import com.westcatr.rd.boot.orm.service.BaseDao;
import com.westcatr.rd.boot.sso.util.AuthUtil;
import com.westcatr.rd.boot.web.dic.pojo.vo.DicInfoVO;
import com.westcatr.rd.boot.web.dic.service.DicInfoService;
import com.westcatr.rd.car.business.basic.entity.BasicSloganInfo;
import com.westcatr.rd.car.business.basic.mapper.BasicSloganInfoMapper;
import com.westcatr.rd.car.business.basic.pojo.query.BasicSloganInfoQuery;
import com.westcatr.rd.car.business.basic.service.BasicSloganInfoService;
import com.westcatr.rd.car.enums.RoleBusEnum;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;

/**
 * <p>
 * 基础信息—奖品宣传语配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Service
public class BasicSloganInfoServiceImpl extends ServiceImpl<BasicSloganInfoMapper, BasicSloganInfo>
        implements BasicSloganInfoService {

    @Autowired
    private DicInfoService dicInfoService;

    @Autowired
    private BaseDao baseDao;

    @Override
    public IPage<BasicSloganInfo> entityPage(BasicSloganInfoQuery query) {
        return page(query.page(), query.wrapper());
    }

    @Override
    public BasicSloganInfo getEntityById(Long id) {
        return getById(id);
    }

    @Override
    public boolean saveEntity(BasicSloganInfo param) {
        return save(param);
    }

    @Override
    public boolean updateEntity(BasicSloganInfo param) {
        return updateById(param);
    }

    @Override
    public boolean removeEntityById(Long id) {
        return removeById(id);
    }

    @Override
    public String getAuditLogSizeInfo() {
        IUser user = AuthUtil.getUserE();
        if (CollUtil.containsAny(user.getRoleIds(),
                Arrays.asList(RoleBusEnum.SUPER_ADMIN.getRoleId(), RoleBusEnum.CAR_ADMIN.getRoleId()))) {
            DicInfoVO dicInfo = dicInfoService.getByCodeAndName("auditLogSize", "审计日志大小");
            if (ObjectUtil.isNull(dicInfo)) {
                return "未配置审计日志大小阈值";
            }

            double auditLogSize = Double.parseDouble(dicInfo.getDicValue());

            // 获取日志表大小
            String sizeQuery = """
                    SELECT CAST(ROUND(SUM(total_size) / 1024 / 1024, 2) AS CHAR) as total_mb FROM (
                        SELECT (data_length + index_length) as total_size
                        FROM information_schema.TABLES
                        WHERE table_schema = DATABASE()
                        AND table_name IN ('sys_auth_api_log', 'sys_auth_login_log')
                    ) t""";
            String totalSizeStr = baseDao.selectOneBySql(String.class, sizeQuery, new QueryWrapper<>());

            // 如果总大小超过3MB,添加警告信息
            if (totalSizeStr != null) {
                BigDecimal totalSize = new BigDecimal(totalSizeStr);
                if (totalSize.compareTo(new BigDecimal(auditLogSize)) > 0) {
                    return "审计日志存储容量已达到预设值【" + auditLogSize + "】MB，请及时清理";
                }
            }

        }
        return null;
    }
}
