package com.westcatr.rd.car.business.car.controller;

import static cn.hutool.core.text.StrPool.COMMA;

import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.westcatr.rd.boot.core.dto.ID;
import com.westcatr.rd.boot.core.vo.IResult;
import com.westcatr.rd.boot.orm.association.AssociationQuery;
import com.westcatr.rd.boot.web.annotation.validated.Id;
import com.westcatr.rd.car.business.car.entity.CarVehicleModelPartsInfo;
import com.westcatr.rd.car.business.car.pojo.query.CarVehicleModelPartsInfoQuery;
import com.westcatr.rd.car.business.car.pojo.vo.CarVehicleModelPartsInfoVO;
import com.westcatr.rd.car.business.car.service.CarVehicleModelPartsInfoService;
import com.westcatr.rd.car.common.annotation.AuditLog;
import com.westcatr.rd.car.enums.OperationTypeEnum;

import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

/**
 * CarVehicleModelPartsInfo 控制器
 * 
 * <AUTHOR>
 * @since 2024-12-30
 */
@Slf4j
@RestController
public class CarVehicleModelPartsInfoController {

    @Autowired
    private CarVehicleModelPartsInfoService carVehicleModelPartsInfoService;

    @Operation(summary = "获取车型配件信息表--配件分页数据")
    @PostMapping("/carVehicleModelPartsInfo/page")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车型配件信息表分页数据")
    public IResult<IPage<CarVehicleModelPartsInfo>> getCarVehicleModelPartsInfoPage(
            @RequestBody CarVehicleModelPartsInfoQuery query) {
        return IResult.ok(carVehicleModelPartsInfoService.entityPage(query));
    }

    @Operation(summary = "获取车型配件信息表--配件数据")
    @PostMapping("/carVehicleModelPartsInfo/get")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车型配件信息表数据: ID={#id.id}")
    public IResult<CarVehicleModelPartsInfo> getCarVehicleModelPartsInfoById(@RequestBody @Id ID id) {
        return IResult.ok(carVehicleModelPartsInfoService.getEntityById(id.longId()));
    }

    @Operation(summary = "新增车型配件信息表--配件数据")
    @PostMapping("/carVehicleModelPartsInfo/add")
    @AuditLog(operationType = OperationTypeEnum.ADD, description = "新增车型配件信息表数据: ID={#param.id}")
    public IResult addCarVehicleModelPartsInfo(@RequestBody CarVehicleModelPartsInfo param) {
        return IResult.auto(carVehicleModelPartsInfoService.saveEntity(param));
    }

    @Operation(summary = "更新车型配件信息表--配件数据")
    @PostMapping("/carVehicleModelPartsInfo/update")
    @AuditLog(operationType = OperationTypeEnum.UPDATE, description = "更新车型配件信息表数据: ID={#param.id}")
    public IResult updateCarVehicleModelPartsInfoById(
            @RequestBody CarVehicleModelPartsInfo param) {
        return IResult.auto(carVehicleModelPartsInfoService.updateEntity(param));
    }

    @Operation(summary = "删除车型配件信息表--配件数据")
    @PostMapping("/carVehicleModelPartsInfo/delete")
    @AuditLog(operationType = OperationTypeEnum.DELETE, description = "删除车型配件信息表数据: ID={#id.id}")
    public IResult deleteCarVehicleModelPartsInfoById(@RequestBody @Id(isLong = false) ID id) {
        for (String s : id.getId().split(COMMA)) {
            carVehicleModelPartsInfoService.removeEntityById(Long.valueOf(s));
        }
        return IResult.ok();
    }

    @Operation(summary = "获取车型配件信息表--配件VO分页数据")
    @PostMapping("/carVehicleModelPartsInfo/voPage")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车型配件信息表VO分页数据")
    public IResult<IPage<CarVehicleModelPartsInfoVO>> getCarVehicleModelPartsInfoVoPage(
            @RequestBody CarVehicleModelPartsInfoQuery query) {
        return IResult.ok(carVehicleModelPartsInfoService.myVoPage(query));
    }

    @Operation(summary = "获取车型配件信息表--配件VO数据")
    @PostMapping("/carVehicleModelPartsInfo/getVo")
    @AuditLog(operationType = OperationTypeEnum.QUERY, description = "查询车型配件信息表VO数据: ID={#id.id}")
    public IResult<CarVehicleModelPartsInfoVO> getCarVehicleModelPartsInfoVoById(@RequestBody @Id ID id) {
        AssociationQuery<CarVehicleModelPartsInfoVO> associationQuery = new AssociationQuery<>(
                CarVehicleModelPartsInfoVO.class);
        return IResult.ok(associationQuery.getVo(id.longId()));
    }

    @Operation(summary = "导出车型配件信息表--配件模板")
    @GetMapping("/carVehicleModelPartsInfo/exportTemplate")
    @AuditLog(operationType = OperationTypeEnum.EXPORT, description = "导出车型配件信息表模板")
    public void exportTemplate() {
        carVehicleModelPartsInfoService.exportTemplate();
    }

    @PostMapping("/carVehicleModelPartsInfo/import")
    @Operation(summary = "导入Excel数据")
    @AuditLog(operationType = OperationTypeEnum.IMPORT, description = "导入车型配件信息表数据")
    public IResult importExcel(@RequestPart MultipartFile file) throws IOException {
        return IResult.auto(carVehicleModelPartsInfoService.importExcel(file));
    }

    @Operation(summary = "导出车型配件信息表--配件数据")
    @PostMapping("/carVehicleModelPartsInfo/export")
    @AuditLog(operationType = OperationTypeEnum.EXPORT, description = "导出车型配件信息表数据")
    public void export(@RequestBody CarVehicleModelPartsInfoQuery query) {
        carVehicleModelPartsInfoService.exportExcel(query);
    }

}
