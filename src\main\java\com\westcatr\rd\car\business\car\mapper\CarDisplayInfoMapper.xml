<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.westcatr.rd.car.business.car.mapper.CarDisplayInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.westcatr.rd.car.business.car.entity.CarDisplayInfo">
        <id column="id" property="id" />
        <result column="car_id" property="carId" />
        <result column="is_carousel" property="isCarousel" />
        <result column="sort_num" property="sortNum" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        car_id, id, is_carousel, sort_num
    </sql>

</mapper>
